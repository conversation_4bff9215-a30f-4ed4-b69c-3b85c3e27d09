# برنامج محاسبة المبيعات والمخازن

## وصف المشروع
برنامج محاسبة شامل لإدارة المبيعات والمخازن مصمم للشركات والمتاجر الصغيرة والمتوسطة.

## المميزات الرئيسية
- ✅ إدارة المنتجات والمخازن
- ✅ إدارة العملاء والموردين  
- ✅ نظام المبيعات والمشتريات
- ✅ التقارير المالية والمخزنية
- ✅ نظام الصلاحيات المتعددة
- ✅ النسخ الاحتياطي والاستعادة
- ✅ دعم الباركود والطباعة

## التقنيات المستخدمة
- **Python 3.8+** - لغة البرمجة الأساسية
- **tkinter** - واجهة المستخدم الرسومية
- **SQLite** - قاعدة البيانات
- **Pillow** - معالجة الصور
- **ReportLab** - إنشاء ملفات PDF
- **openpyxl** - التعامل مع ملفات Excel

## هيكل المشروع
```
sales_inventory_system/
├── main.py                 # الملف الرئيسي لتشغيل البرنامج
├── database/              # ملفات قاعدة البيانات
├── screens/               # شاشات البرنامج
├── utils/                 # الأدوات المساعدة
├── images/                # الصور والأيقونات
├── backup/                # النسخ الاحتياطية
├── reports/               # التقارير المُنشأة
├── docs/                  # الوثائق والأدلة
└── config/                # ملفات التكوين

```

## طريقة التشغيل
1. تأكد من تثبيت Python 3.8 أو أحدث
2. قم بتثبيت المتطلبات: `pip install -r requirements.txt`
3. شغل البرنامج: `python main.py`

## المطور
تم تطوير هذا البرنامج باستخدام Augment Agent

## الترخيص
هذا المشروع مفتوح المصدر للاستخدام التجاري والشخصي
