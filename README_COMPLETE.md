# 🏪 برنامج محاسبة المبيعات والمخازن

برنامج شامل ومتكامل لإدارة المبيعات والمشتريات والمخازن باللغة العربية مع دعم كامل للاتجاه من اليمين إلى اليسار (RTL).

## ✨ المميزات الرئيسية

### 📦 إدارة المخزون
- ✅ إدارة المنتجات مع الصور والباركود
- ✅ إدارة فئات المنتجات
- ✅ تتبع حركات المخزون
- ✅ تنبيهات المخزون المنخفض
- ✅ جرد المخزون

### 👥 إدارة العلاقات التجارية
- ✅ إدارة العملاء مع حدود الائتمان
- ✅ إدارة الموردين
- ✅ كشوف حسابات العملاء والموردين
- ✅ تتبع المديونيات

### 💰 نظام المبيعات والمشتريات
- ✅ فواتير المبيعات مع حساب الضرائب والخصومات
- ✅ فواتير المشتريات
- ✅ إدارة المدفوعات
- ✅ تتبع حالات الدفع

### 📊 نظام التقارير الشامل
- ✅ تقارير المبيعات التفصيلية والإجمالية
- ✅ تقارير المشتريات
- ✅ تقارير المخزون وحركاته
- ✅ تقرير الأرباح والخسائر
- ✅ تقارير العملاء والموردين
- ✅ تقرير المديونيات

### 🔐 نظام الصلاحيات
- ✅ أربعة مستويات مستخدمين (مدير، محاسب، بائع، مراقب مخزون)
- ✅ صلاحيات مخصصة لكل مستوى
- ✅ تتبع عمليات المستخدمين

### 🌐 دعم اللغة العربية
- ✅ **واجهة كاملة باللغة العربية**
- ✅ **دعم الاتجاه من اليمين إلى اليسار (RTL)**
- ✅ **خطوط محسنة لعرض العربية**
- ✅ **تجربة مستخدم محسنة للمتحدثين بالعربية**

### 🛠️ ميزات إضافية
- ✅ النسخ الاحتياطي والاستعادة
- ✅ تصدير التقارير
- ✅ واجهة سهلة الاستخدام
- ✅ نظام اختبار شامل

## 🔧 التقنيات المستخدمة
- **Python 3.8+** - لغة البرمجة الأساسية
- **tkinter** - واجهة المستخدم الرسومية مع دعم RTL
- **SQLite** - قاعدة البيانات المدمجة
- **Pillow** - معالجة الصور
- **خط Tahoma** - لدعم أفضل للغة العربية

## 📁 هيكل المشروع
```
sales_inventory_system/
├── main.py                    # الملف الرئيسي
├── requirements.txt           # المتطلبات
├── run.bat                   # تشغيل سريع
├── install_requirements.bat  # تثبيت المتطلبات
├── test_system.py            # اختبار النظام
├── config.ini               # ملف التكوين
├── تعليمات_التشغيل.txt      # دليل سريع
├── database/                # قاعدة البيانات
│   └── sales_inventory.db
├── config/                  # إعدادات البرنامج
│   ├── __init__.py
│   └── settings.py
├── utils/                   # الأدوات المساعدة
│   ├── __init__.py
│   ├── database_manager.py
│   ├── helpers.py
│   └── arabic_support.py
├── screens/                 # شاشات البرنامج
│   ├── __init__.py
│   ├── login_screen.py
│   ├── main_interface.py
│   ├── users_management.py
│   ├── products_management.py
│   ├── customers_management.py
│   ├── suppliers_management.py
│   ├── sales_management.py
│   ├── purchases_management.py
│   └── reports_management.py
├── docs/                    # الوثائق
│   ├── user_guide.md
│   ├── database_structure.md
│   └── rtl_changes.md
├── images/                  # الصور والأيقونات
├── backup/                  # النسخ الاحتياطية
└── reports/                 # التقارير
```

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8 أو أحدث
- نظام التشغيل: Windows 10 أو أحدث
- ذاكرة: 4 جيجابايت RAM على الأقل
- مساحة القرص: 500 ميجابايت على الأقل

### طريقة التثبيت السريع
1. **تثبيت المتطلبات:**
   ```bash
   # انقر نقراً مزدوجاً على:
   install_requirements.bat
   ```

2. **تشغيل البرنامج:**
   ```bash
   # انقر نقراً مزدوجاً على:
   run.bat
   ```

### طريقة التثبيت اليدوي
1. **تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

2. **اختبار النظام:**
   ```bash
   python test_system.py
   ```

3. **تشغيل البرنامج:**
   ```bash
   python main.py
   ```

## 🔐 بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** يُنصح بتغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## 👥 أنواع المستخدمين والصلاحيات

### 🔑 المدير (Admin)
- صلاحية كاملة على جميع أجزاء البرنامج
- إدارة المستخدمين والصلاحيات
- الوصول إلى جميع التقارير
- إعدادات النظام والنسخ الاحتياطي

### 💼 المحاسب (Accountant)
- إدارة المبيعات والمشتريات
- إدارة العملاء والموردين
- الوصول إلى التقارير المالية
- لا يمكنه إدارة المستخدمين

### 🛒 البائع (Salesperson)
- إنشاء فواتير المبيعات فقط
- إدارة العملاء
- عرض تقارير المبيعات الخاصة به

### 📦 مراقب المخزون (Warehouse)
- إدارة المنتجات والمخزون
- إدارة المشتريات
- تقارير المخزون وحركاته

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **users** - المستخدمين
- **categories** - فئات المنتجات
- **products** - المنتجات
- **customers** - العملاء
- **suppliers** - الموردين
- **sales_invoices** - فواتير المبيعات
- **sales_invoice_items** - تفاصيل فواتير المبيعات
- **purchase_invoices** - فواتير المشتريات
- **purchase_invoice_items** - تفاصيل فواتير المشتريات
- **payments** - المدفوعات
- **inventory_movements** - حركات المخزون

## 🔄 النسخ الاحتياطي
- يُنصح بعمل نسخة احتياطية يومياً
- النسخ الاحتياطية تُحفظ في مجلد `backup`
- يمكن استعادة النسخة الاحتياطية من قائمة "ملف"

## 🆘 الدعم الفني
في حالة مواجهة أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات بشكل صحيح
2. شغل `test_system.py` للتحقق من سلامة النظام
3. تحقق من وجود ملف قاعدة البيانات في مجلد `database`
4. تأكد من وجود صلاحيات الكتابة في مجلد البرنامج

## 💡 نصائح مهمة
- قم بعمل نسخة احتياطية بانتظام
- لا تحذف ملفات قاعدة البيانات يدوياً
- استخدم أسماء مستخدمين وكلمات مرور قوية
- راجع التقارير بانتظام للتأكد من دقة البيانات
- استخدم ميزة البحث للعثور على البيانات بسرعة

## 🎯 الميزات القادمة
- تصدير التقارير إلى Excel و PDF
- نظام الإشعارات
- تطبيق ويب
- تطبيق موبايل
- تكامل مع أنظمة الدفع الإلكتروني

## 📝 الترخيص
هذا البرنامج مطور باستخدام Augment Agent ومتاح للاستخدام التجاري والشخصي.

## 🙏 شكر وتقدير
تم تطوير هذا البرنامج باستخدام **Augment Agent** - أفضل مساعد ذكي للبرمجة.

---
**© 2024 - برنامج محاسبة المبيعات والمخازن | تم التطوير باستخدام Augment Agent**
