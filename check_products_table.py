# -*- coding: utf-8 -*-
"""
فحص بنية جدول المنتجات
"""

import os
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager

def check_products_table():
    """فحص بنية جدول المنتجات"""
    db_manager = DatabaseManager()
    
    try:
        # فحص بنية جدول المنتجات
        print("🔍 فحص بنية جدول المنتجات...")
        
        # التحقق من وجود جدول المنتجات
        tables_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='products'"
        tables = db_manager.execute_query(tables_query)

        if not tables:
            print("❌ جدول المنتجات غير موجود")
            return

        check_query = "PRAGMA table_info(products)"
        columns = db_manager.execute_query(check_query)

        if columns:
            print("\n📋 أعمدة جدول المنتجات:")
            print("-" * 50)
            for col in columns:
                print(f"• {col['name']} - {col['type']} - {'NOT NULL' if col['notnull'] else 'NULL'}")
        else:
            print("❌ لا يمكن الحصول على معلومات الأعمدة")
        
        # فحص عدد المنتجات
        count_query = "SELECT COUNT(*) as count FROM products"
        result = db_manager.execute_query(count_query)
        if result:
            print(f"\n📊 عدد المنتجات: {result[0]['count']}")
        
        # فحص بعض المنتجات
        sample_query = "SELECT * FROM products LIMIT 5"
        products = db_manager.execute_query(sample_query)
        
        if products:
            print("\n📋 عينة من المنتجات:")
            print("-" * 50)
            for product in products:
                print(f"• {product['id']}: {product['name']}")
        
    except Exception as e:
        print(f"❌ حدث خطأ في فحص جدول المنتجات: {str(e)}")

if __name__ == "__main__":
    check_products_table()
