# -*- coding: utf-8 -*-
"""
إعدادات البرنامج الأساسية
"""

import os
from datetime import datetime

# مسارات المجلدات
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATABASE_DIR = os.path.join(BASE_DIR, 'database')
IMAGES_DIR = os.path.join(BASE_DIR, 'images')
BACKUP_DIR = os.path.join(BASE_DIR, 'backup')
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')

# إعدادات قاعدة البيانات
DATABASE_NAME = 'sales_inventory.db'
DATABASE_PATH = os.path.join(DATABASE_DIR, DATABASE_NAME)

# إعدادات الشركة الافتراضية
COMPANY_INFO = {
    'name': 'شركة المبيعات والمخازن',
    'address': 'العنوان',
    'phone': '+974-0000-0000',
    'email': '<EMAIL>',
    'tax_number': '*********',
    'currency': 'ريال قطري',
    'currency_symbol': 'ر.ق'
}

# إعدادات الفواتير
INVOICE_SETTINGS = {
    'auto_increment': True,
    'prefix': 'INV-',
    'tax_rate': 0.0,  # معدل الضريبة (0% افتراضياً)
    'show_logo': True,
    'show_signature': True
}

# إعدادات النظام
SYSTEM_SETTINGS = {
    'language': 'ar',
    'direction': 'rtl',  # اتجاه النص من اليمين إلى اليسار
    'date_format': '%Y-%m-%d',
    'time_format': '%H:%M:%S',
    'backup_auto': True,
    'backup_interval': 7,  # أيام
    'low_stock_alert': True,
    'low_stock_threshold': 10
}

# أنواع المستخدمين والصلاحيات
USER_ROLES = {
    'admin': {
        'name': 'مدير',
        'permissions': ['all']
    },
    'accountant': {
        'name': 'محاسب',
        'permissions': [
            'sales_management', 'sales_create', 'sales_view',
            'purchases_management', 'purchases_create',
            'customers_management', 'suppliers_management',
            'reports_view'
        ]
    },
    'salesperson': {
        'name': 'بائع',
        'permissions': [
            'sales_create', 'sales_view',
            'customers_management'
        ]
    },
    'warehouse': {
        'name': 'مراقب مخزون',
        'permissions': [
            'products_management', 'inventory_management',
            'purchases_create', 'purchases_management'
        ]
    }
}

# ألوان الواجهة
COLORS = {
    'primary': '#2E86AB',
    'secondary': '#A23B72',
    'success': '#F18F01',
    'danger': '#C73E1D',
    'warning': '#F4A261',
    'info': '#264653',
    'light': '#F8F9FA',
    'dark': '#212529',
    'background': '#FFFFFF',
    'text': '#000000'
}

# أحجام الخطوط - تم تحسينها للعربية
FONTS = {
    'title': ('Tahoma', 16, 'bold'),
    'heading': ('Tahoma', 14, 'bold'),
    'large': ('Tahoma', 13, 'bold'),
    'normal': ('Tahoma', 12),
    'small': ('Tahoma', 10),
    'button': ('Tahoma', 11, 'bold')
}

def get_current_date():
    """إرجاع التاريخ الحالي"""
    return datetime.now().strftime(SYSTEM_SETTINGS['date_format'])

def get_current_time():
    """إرجاع الوقت الحالي"""
    return datetime.now().strftime(SYSTEM_SETTINGS['time_format'])

def get_current_datetime():
    """إرجاع التاريخ والوقت الحالي"""
    return datetime.now().strftime(f"{SYSTEM_SETTINGS['date_format']} {SYSTEM_SETTINGS['time_format']}")

def load_program_settings():
    """تحميل إعدادات البرنامج المحفوظة"""
    import json

    settings_file = os.path.join(DATABASE_DIR, 'program_settings.json')

    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                saved_settings = json.load(f)

            # تحديث معلومات الشركة
            if 'company' in saved_settings:
                COMPANY_INFO.update(saved_settings['company'])

            # تحديث الألوان
            if 'appearance' in saved_settings:
                appearance = saved_settings['appearance']
                if 'primary_color' in appearance:
                    COLORS['primary'] = appearance['primary_color']
                if 'success_color' in appearance:
                    COLORS['success'] = appearance['success_color']
                if 'danger_color' in appearance:
                    COLORS['danger'] = appearance['danger_color']
                if 'warning_color' in appearance:
                    COLORS['warning'] = appearance['warning_color']
                if 'info_color' in appearance:
                    COLORS['info'] = appearance['info_color']

            # تحديث إعدادات النظام
            if 'system' in saved_settings:
                system = saved_settings['system']
                if 'currency' in system:
                    COMPANY_INFO['currency'] = system['currency']
                if 'date_format' in system:
                    if system['date_format'] == 'dd/mm/yyyy':
                        SYSTEM_SETTINGS['date_format'] = '%d/%m/%Y'
                    elif system['date_format'] == 'mm/dd/yyyy':
                        SYSTEM_SETTINGS['date_format'] = '%m/%d/%Y'
                    elif system['date_format'] == 'yyyy-mm-dd':
                        SYSTEM_SETTINGS['date_format'] = '%Y-%m-%d'

            print("✅ تم تحميل إعدادات البرنامج بنجاح")
            return saved_settings

        except Exception as e:
            print(f"❌ خطأ في تحميل إعدادات البرنامج: {str(e)}")

    return None

# تحميل الإعدادات عند استيراد الملف
load_program_settings()
