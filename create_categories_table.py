# -*- coding: utf-8 -*-
"""
إنشاء جدول فئات المنتجات في قاعدة البيانات
"""

import os
import sys
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager

def create_categories_table():
    """إنشاء جدول فئات المنتجات"""
    db_manager = DatabaseManager()
    
    try:
        # إنشاء جدول الفئات
        create_table_query = """
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                user_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """
        
        db_manager.execute_query(create_table_query)
        print("✅ تم إنشاء جدول فئات المنتجات بنجاح")
        
        # إنشاء فهارس لتحسين الأداء
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_categories_name ON categories (name)",
            "CREATE INDEX IF NOT EXISTS idx_categories_is_active ON categories (is_active)",
            "CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories (user_id)"
        ]
        
        for index_query in indexes:
            db_manager.execute_query(index_query)
        
        print("✅ تم إنشاء الفهارس بنجاح")
        
        # تحديث جدول المنتجات لإضافة عمود الفئة
        update_products_table(db_manager)
        
        # إنشاء فئات تجريبية
        create_sample_categories(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء جدول الفئات: {str(e)}")

def update_products_table(db_manager):
    """تحديث جدول المنتجات لإضافة عمود الفئة"""
    try:
        # التحقق من وجود عمود category_id
        check_column_query = "PRAGMA table_info(products)"
        columns = db_manager.execute_query(check_column_query)
        
        has_category_column = any(col['name'] == 'category_id' for col in columns)
        
        if not has_category_column:
            # إضافة عمود الفئة
            alter_query = "ALTER TABLE products ADD COLUMN category_id INTEGER REFERENCES categories(id)"
            db_manager.execute_query(alter_query)
            print("✅ تم إضافة عمود الفئة لجدول المنتجات")
            
            # إنشاء فهرس للعمود الجديد
            index_query = "CREATE INDEX IF NOT EXISTS idx_products_category_id ON products (category_id)"
            db_manager.execute_query(index_query)
            print("✅ تم إنشاء فهرس عمود الفئة")
        else:
            print("ℹ️ عمود الفئة موجود مسبقاً في جدول المنتجات")
            
    except Exception as e:
        print(f"❌ حدث خطأ في تحديث جدول المنتجات: {str(e)}")

def create_sample_categories(db_manager):
    """إنشاء فئات تجريبية"""
    try:
        print("\n🔄 إنشاء فئات تجريبية...")
        
        # فئات تجريبية
        sample_categories = [
            {
                'name': 'إلكترونيات',
                'description': 'أجهزة إلكترونية ومعدات تقنية'
            },
            {
                'name': 'ملابس',
                'description': 'ملابس رجالية ونسائية وأطفال'
            },
            {
                'name': 'أغذية ومشروبات',
                'description': 'مواد غذائية ومشروبات متنوعة'
            },
            {
                'name': 'أدوات منزلية',
                'description': 'أدوات وأجهزة منزلية'
            },
            {
                'name': 'كتب وقرطاسية',
                'description': 'كتب ومواد قرطاسية ومكتبية'
            },
            {
                'name': 'رياضة وترفيه',
                'description': 'معدات رياضية وألعاب ترفيهية'
            },
            {
                'name': 'صحة وجمال',
                'description': 'منتجات العناية الشخصية والصحة'
            },
            {
                'name': 'أثاث ومفروشات',
                'description': 'أثاث منزلي ومفروشات'
            },
            {
                'name': 'سيارات وقطع غيار',
                'description': 'قطع غيار ومعدات السيارات'
            },
            {
                'name': 'أدوات وعدد',
                'description': 'أدوات يدوية وعدد صناعية'
            }
        ]
        
        categories_created = 0
        
        for category_data in sample_categories:
            # التحقق من عدم وجود الفئة
            check_query = "SELECT id FROM categories WHERE name = ?"
            existing = db_manager.execute_query(check_query, [category_data['name']])
            
            if not existing:
                insert_query = """
                    INSERT INTO categories (name, description, is_active, user_id, created_at)
                    VALUES (?, ?, ?, ?, ?)
                """
                
                params = [
                    category_data['name'],
                    category_data['description'],
                    1,  # نشط
                    1,  # المدير
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ]
                
                db_manager.execute_query(insert_query, params)
                categories_created += 1
        
        print(f"✅ تم إنشاء {categories_created} فئة تجريبية")
        
        # ربط المنتجات الموجودة بالفئات
        assign_products_to_categories(db_manager)
        
        # عرض إحصائيات
        display_categories_statistics(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء الفئات التجريبية: {str(e)}")

def assign_products_to_categories(db_manager):
    """ربط المنتجات الموجودة بالفئات"""
    try:
        print("\n🔄 ربط المنتجات بالفئات...")
        
        # جلب جميع الفئات
        categories_query = "SELECT id, name FROM categories ORDER BY id"
        categories = db_manager.execute_query(categories_query)
        
        if not categories:
            print("❌ لا توجد فئات لربط المنتجات بها")
            return
        
        # جلب المنتجات التي لا تحتوي على فئة
        products_query = "SELECT id, name FROM products WHERE category_id IS NULL AND is_active = 1"
        products = db_manager.execute_query(products_query)
        
        if not products:
            print("ℹ️ جميع المنتجات مرتبطة بفئات مسبقاً")
            return
        
        import random
        
        products_updated = 0
        
        for product in products:
            # اختيار فئة عشوائية
            category = random.choice(categories)
            
            # تحديث المنتج
            update_query = "UPDATE products SET category_id = ? WHERE id = ?"
            db_manager.execute_query(update_query, [category['id'], product['id']])
            
            products_updated += 1
        
        print(f"✅ تم ربط {products_updated} منتج بالفئات")
        
    except Exception as e:
        print(f"❌ حدث خطأ في ربط المنتجات بالفئات: {str(e)}")

def display_categories_statistics(db_manager):
    """عرض إحصائيات الفئات"""
    try:
        print("\n📊 إحصائيات فئات المنتجات:")
        print("=" * 50)
        
        # إحصائيات عامة
        total_query = """
            SELECT 
                COUNT(*) as total_categories,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_categories,
                SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_categories
            FROM categories
        """
        
        result = db_manager.execute_query(total_query)
        if result:
            data = result[0]
            print(f"📋 إجمالي الفئات: {data['total_categories']}")
            print(f"✅ الفئات النشطة: {data['active_categories']}")
            print(f"❌ الفئات غير النشطة: {data['inactive_categories']}")
        
        # إحصائيات المنتجات حسب الفئة
        products_query = """
            SELECT c.name as category_name, COUNT(p.id) as products_count
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id
            GROUP BY c.id, c.name
            ORDER BY products_count DESC
        """
        
        result = db_manager.execute_query(products_query)
        if result:
            print(f"\n📈 توزيع المنتجات حسب الفئات:")
            print("-" * 40)
            
            for item in result:
                print(f"• {item['category_name']}: {item['products_count']} منتج")
        
        # المنتجات بدون فئة
        no_category_query = "SELECT COUNT(*) as count FROM products WHERE category_id IS NULL"
        result = db_manager.execute_query(no_category_query)
        if result:
            no_category_count = result[0]['count']
            if no_category_count > 0:
                print(f"\n⚠️ منتجات بدون فئة: {no_category_count}")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة المخزون → إدارة الفئات")
        print("2. جرب إنشاء فئة جديدة")
        print("3. جرب تعديل فئة موجودة")
        print("4. جرب تغيير حالة الفئة (تفعيل/إلغاء تفعيل)")
        print("5. استخدم البحث والفلاتر")
        print("6. لاحظ الإحصائيات في أعلى الشاشة")
        print("7. انقر نقرة مزدوجة على فئة لعرض التفاصيل")
        print("8. اذهب إلى إدارة المنتجات ولاحظ إمكانية اختيار الفئة")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    print("🚀 بدء إنشاء جدول فئات المنتجات...")
    create_categories_table()
    print("\n✅ جاهز لاختبار نظام إدارة الفئات!")
