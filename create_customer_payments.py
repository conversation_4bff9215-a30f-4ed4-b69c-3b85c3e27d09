# -*- coding: utf-8 -*-
"""
إنشاء مقبوضات تجريبية للعملاء لاختبار كشف حساب العميل
"""

import os
import sys
from datetime import datetime, timedelta
import random

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager
from utils.helpers import log_user_activity

def create_customer_payments():
    """إنشاء مقبوضات تجريبية للعملاء"""
    db_manager = DatabaseManager()
    
    try:
        # الحصول على فواتير المبيعات الموجودة
        invoices_query = """
            SELECT si.id, si.invoice_number, si.customer_id, si.total_amount, 
                   si.paid_amount, si.remaining_amount, c.name as customer_name
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE si.remaining_amount > 0
            ORDER BY si.invoice_date
        """
        
        invoices_result = db_manager.execute_query(invoices_query)
        invoices = [dict(invoice) for invoice in invoices_result] if invoices_result else []
        
        if not invoices:
            print("❌ لا توجد فواتير مبيعات مستحقة للتحصيل.")
            return
        
        print(f"💰 تم العثور على {len(invoices)} فاتورة مستحقة للتحصيل")
        
        payments_created = 0
        
        # إنشاء مقبوضات للفواتير
        for invoice in invoices:
            # تخطي الفواتير النقدية (بدون عميل)
            if not invoice['customer_id']:
                continue
                
            # احتمال إنشاء مقبوضات لهذه الفاتورة (75%)
            if random.random() < 0.75:
                # تحديد نوع المقبوضات
                payment_types = [
                    ('full', 'تحصيل كامل'),
                    ('partial', 'تحصيل جزئي'),
                    ('multiple', 'مقبوضات متعددة')
                ]
                
                payment_type, description = random.choice(payment_types)
                
                if payment_type == 'full':
                    # تحصيل كامل
                    amount = invoice['remaining_amount']
                    payment_date = datetime.now() - timedelta(days=random.randint(1, 30))
                    
                    payment_id = create_payment(
                        db_manager, invoice, amount, payment_date, 
                        f"تحصيل كامل لفاتورة {invoice['invoice_number']}"
                    )
                    
                    if payment_id:
                        payments_created += 1
                        
                elif payment_type == 'partial':
                    # تحصيل جزئي
                    amount = invoice['remaining_amount'] * random.uniform(0.3, 0.8)
                    payment_date = datetime.now() - timedelta(days=random.randint(1, 20))
                    
                    payment_id = create_payment(
                        db_manager, invoice, amount, payment_date,
                        f"تحصيل جزئي لفاتورة {invoice['invoice_number']}"
                    )
                    
                    if payment_id:
                        payments_created += 1
                        
                elif payment_type == 'multiple':
                    # مقبوضات متعددة
                    remaining = invoice['remaining_amount']
                    num_payments = random.randint(2, 4)
                    
                    for i in range(num_payments):
                        if remaining <= 0:
                            break
                            
                        if i == num_payments - 1:
                            # آخر مقبوضات - المبلغ المتبقي
                            amount = remaining
                        else:
                            # مقبوضات جزئية
                            amount = remaining * random.uniform(0.2, 0.5)
                            amount = min(amount, remaining)
                        
                        payment_date = datetime.now() - timedelta(days=random.randint(1, 45))
                        
                        payment_id = create_payment(
                            db_manager, invoice, amount, payment_date,
                            f"مقبوضات {i+1} لفاتورة {invoice['invoice_number']}"
                        )
                        
                        if payment_id:
                            payments_created += 1
                            remaining -= amount
                
                if payments_created % 10 == 0:
                    print(f"✅ تم إنشاء {payments_created} مقبوضات...")
        
        print(f"\n🎉 تم إنشاء {payments_created} مقبوضات تجريبية للعملاء بنجاح!")
        
        # تحديث حالات الدفع للفواتير
        update_payment_status(db_manager)
        
        # عرض إحصائيات
        display_payment_statistics(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء المقبوضات: {str(e)}")

def create_payment(db_manager, invoice, amount, payment_date, notes):
    """إنشاء مقبوضات واحدة"""
    try:
        # طرق التحصيل المختلفة
        payment_methods = ['نقدي', 'شيك', 'تحويل بنكي', 'بطاقة ائتمان', 'فيزا']
        payment_method = random.choice(payment_methods)
        
        # رقم مرجع للمقبوضات
        reference_number = f"REC-{random.randint(1000, 9999)}"
        
        # إدراج المقبوضات
        payment_query = """
            INSERT INTO payments (
                invoice_type, invoice_id, payment_date, amount, payment_method,
                reference_number, notes, user_id, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        payment_params = [
            'sales',
            invoice['id'],
            payment_date.strftime('%Y-%m-%d'),
            amount,
            payment_method,
            reference_number,
            notes,
            1,  # المدير
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ]
        
        db_manager.execute_query(payment_query, payment_params)
        
        # تحديث فاتورة المبيعات
        new_paid_amount = invoice['paid_amount'] + amount
        new_remaining_amount = invoice['total_amount'] - new_paid_amount
        
        update_invoice_query = """
            UPDATE sales_invoices 
            SET paid_amount = ?, remaining_amount = ?
            WHERE id = ?
        """
        
        db_manager.execute_query(update_invoice_query, [
            new_paid_amount, new_remaining_amount, invoice['id']
        ])
        
        # تحديث البيانات المحلية
        invoice['paid_amount'] = new_paid_amount
        invoice['remaining_amount'] = new_remaining_amount
        
        # تسجيل العملية
        customer_name = invoice['customer_name'] or 'عميل نقدي'
        log_user_activity(
            db_manager,
            1,  # المدير
            "إنشاء مقبوضات تجريبية من العميل",
            f"العميل: {customer_name}, المبلغ: {amount:.2f}, الطريقة: {payment_method}",
            "payments",
            invoice['id']
        )
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المقبوضات: {str(e)}")
        return False

def update_payment_status(db_manager):
    """تحديث حالات الدفع للفواتير"""
    try:
        print("\n🔄 تحديث حالات الدفع...")
        
        # تحديث حالة الدفع لجميع الفواتير
        update_query = """
            UPDATE sales_invoices 
            SET payment_status = CASE 
                WHEN remaining_amount = 0 THEN 'paid'
                WHEN paid_amount > 0 AND remaining_amount > 0 THEN 'partial'
                ELSE 'pending'
            END
        """
        
        db_manager.execute_query(update_query)
        print("✅ تم تحديث حالات الدفع")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث حالات الدفع: {str(e)}")

def display_payment_statistics(db_manager):
    """عرض إحصائيات المقبوضات"""
    try:
        print("\n📊 إحصائيات المقبوضات:")
        print("=" * 50)
        
        # إحصائيات عامة للمقبوضات
        payments_query = """
            SELECT 
                COUNT(*) as total_payments,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount
            FROM payments 
            WHERE invoice_type = 'sales'
        """
        
        result = db_manager.execute_query(payments_query)
        if result:
            data = result[0]
            print(f"💰 إجمالي المقبوضات: {data['total_payments']}")
            print(f"💵 إجمالي المبلغ: {data['total_amount']:,.2f}")
            print(f"📊 متوسط المقبوضات: {data['avg_amount']:,.2f}")
        
        # إحصائيات حالات الدفع
        status_query = """
            SELECT 
                payment_status,
                COUNT(*) as count,
                SUM(total_amount) as total_amount,
                SUM(remaining_amount) as remaining_amount
            FROM sales_invoices
            WHERE customer_id IS NOT NULL
            GROUP BY payment_status
        """
        
        result = db_manager.execute_query(status_query)
        if result:
            print(f"\n📋 حالات الدفع للعملاء:")
            print("-" * 30)
            status_names = {
                'pending': 'معلق',
                'partial': 'محصل جزئياً',
                'paid': 'محصل',
                'overdue': 'متأخر'
            }
            
            for status in result:
                status_name = status_names.get(status['payment_status'], status['payment_status'])
                print(f"• {status_name}: {status['count']} فاتورة - متبقي: {status['remaining_amount']:,.2f}")
        
        # إحصائيات المبيعات النقدية
        cash_query = """
            SELECT 
                COUNT(*) as count,
                SUM(total_amount) as total_amount
            FROM sales_invoices
            WHERE customer_id IS NULL
        """
        
        result = db_manager.execute_query(cash_query)
        if result:
            data = result[0]
            print(f"\n💵 المبيعات النقدية:")
            print("-" * 20)
            print(f"• عدد الفواتير: {data['count']}")
            print(f"• إجمالي المبلغ: {data['total_amount']:,.2f}")
        
        # أكثر العملاء تحصيلاً
        customers_query = """
            SELECT c.name, 
                   COUNT(p.id) as payment_count,
                   SUM(p.amount) as total_collected
            FROM customers c
            JOIN sales_invoices si ON c.id = si.customer_id
            JOIN payments p ON si.id = p.invoice_id AND p.invoice_type = 'sales'
            GROUP BY c.id, c.name
            ORDER BY SUM(p.amount) DESC
            LIMIT 5
        """
        
        result = db_manager.execute_query(customers_query)
        if result:
            print(f"\n🏆 أكثر 5 عملاء تحصيلاً:")
            print("-" * 30)
            for customer in result:
                print(f"• {customer['name']}: {customer['payment_count']} مقبوضات - {customer['total_collected']:,.2f}")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة العملاء → كشف حساب عميل")
        print("2. اختر عميل من القائمة")
        print("3. جرب الفترات المختلفة (هذا الشهر، الشهر الماضي، هذا العام)")
        print("4. لاحظ الرصيد الافتتاحي والختامي")
        print("5. تحقق من تفاصيل المعاملات (فواتير ومقبوضات)")
        print("6. جرب خيار 'العملاء النقديين' لرؤية المبيعات النقدية")
        print("7. راجع ملخص الحساب في أعلى الشاشة")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    print("🚀 بدء إنشاء مقبوضات العملاء التجريبية...")
    create_customer_payments()
    print("\n✅ جاهز لاختبار كشف حساب العميل!")
