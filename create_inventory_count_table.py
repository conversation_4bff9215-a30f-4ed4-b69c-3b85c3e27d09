# -*- coding: utf-8 -*-
"""
إنشاء جدول جرد المخزون في قاعدة البيانات
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import DATABASE_PATH

def create_inventory_count_table():
    """إنشاء جدول جرد المخزون"""
    try:
        print("🚀 بدء إنشاء جدول جرد المخزون...")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # إنشاء جدول جرد المخزون
        create_table_query = """
            CREATE TABLE IF NOT EXISTS inventory_counts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                count_date DATE NOT NULL,
                counted_quantity REAL,
                notes TEXT,
                status TEXT NOT NULL DEFAULT 'معلق' CHECK (status IN ('معلق', 'مكتمل')),
                user_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(product_id, count_date)
            )
        """
        
        cursor.execute(create_table_query)
        print("✅ تم إنشاء جدول جرد المخزون بنجاح")
        
        # إنشاء فهارس لتحسين الأداء
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_inventory_counts_product_id ON inventory_counts (product_id)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_counts_count_date ON inventory_counts (count_date)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_counts_status ON inventory_counts (status)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_counts_user_id ON inventory_counts (user_id)"
        ]
        
        for index_query in indexes:
            cursor.execute(index_query)
        
        print("✅ تم إنشاء الفهارس بنجاح")
        
        # إنشاء بيانات جرد تجريبية
        create_sample_inventory_counts(cursor)
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض الإحصائيات
        display_statistics(cursor)
        
        conn.close()
        print("\n✅ تم إعداد نظام جرد المخزون بنجاح!")
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء جدول جرد المخزون: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

def create_sample_inventory_counts(cursor):
    """إنشاء بيانات جرد تجريبية"""
    try:
        print("\n🔄 إنشاء بيانات جرد تجريبية...")
        
        # جلب المنتجات النشطة
        cursor.execute("SELECT id, name, stock_quantity FROM products WHERE is_active = 1")
        products = cursor.fetchall()
        
        if not products:
            print("❌ لا توجد منتجات لإنشاء بيانات جرد لها")
            return
        
        import random
        
        # تواريخ جرد تجريبية
        today = datetime.now()
        count_dates = [
            (today - timedelta(days=30)).strftime('%Y-%m-%d'),  # جرد الشهر الماضي
            (today - timedelta(days=7)).strftime('%Y-%m-%d'),   # جرد الأسبوع الماضي
            today.strftime('%Y-%m-%d')                          # جرد اليوم
        ]
        
        counts_created = 0
        
        for count_date in count_dates:
            # اختيار عينة عشوائية من المنتجات للجرد
            sample_products = random.sample(products, min(len(products), random.randint(2, 4)))
            
            for product in sample_products:
                # محاكاة كمية مجردة مع فروقات طبيعية
                current_qty = product['stock_quantity']
                
                # إنشاء فرق عشوائي (-5 إلى +5)
                variance = random.uniform(-5, 5)
                counted_qty = max(0, current_qty + variance)
                
                # حالة عشوائية
                status = random.choice(['معلق', 'مكتمل', 'مكتمل', 'مكتمل'])  # احتمالية أكبر للمكتمل
                
                # ملاحظات عشوائية
                notes_options = [
                    None,
                    "جرد دوري",
                    "تم العد بدقة",
                    "فرق بسيط في الكمية",
                    "تحتاج مراجعة",
                    "جرد مكتمل"
                ]
                notes = random.choice(notes_options)
                
                # التحقق من عدم وجود جرد سابق لنفس المنتج والتاريخ
                cursor.execute(
                    "SELECT id FROM inventory_counts WHERE product_id = ? AND count_date = ?",
                    (product['id'], count_date)
                )
                
                if not cursor.fetchone():
                    # إدراج بيانات الجرد
                    insert_query = """
                        INSERT INTO inventory_counts 
                        (product_id, count_date, counted_quantity, notes, status, user_id, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    cursor.execute(insert_query, (
                        product['id'],
                        count_date,
                        round(counted_qty, 2) if status == 'مكتمل' else None,
                        notes,
                        status,
                        1,  # المدير
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ))
                    
                    counts_created += 1
        
        print(f"✅ تم إنشاء {counts_created} سجل جرد تجريبي")
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء بيانات الجرد التجريبية: {str(e)}")

def display_statistics(cursor):
    """عرض إحصائيات جرد المخزون"""
    try:
        print("\n📊 إحصائيات جرد المخزون:")
        print("=" * 50)
        
        # إحصائيات عامة
        cursor.execute("""
            SELECT 
                COUNT(*) as total_counts,
                COUNT(DISTINCT product_id) as products_counted,
                COUNT(DISTINCT count_date) as count_dates,
                SUM(CASE WHEN status = 'مكتمل' THEN 1 ELSE 0 END) as completed_counts,
                SUM(CASE WHEN status = 'معلق' THEN 1 ELSE 0 END) as pending_counts
            FROM inventory_counts
        """)
        
        stats = cursor.fetchone()
        
        print(f"📋 إجمالي سجلات الجرد: {stats['total_counts']}")
        print(f"📦 المنتجات المجردة: {stats['products_counted']}")
        print(f"📅 تواريخ الجرد: {stats['count_dates']}")
        print(f"✅ الجرد المكتمل: {stats['completed_counts']}")
        print(f"⏳ الجرد المعلق: {stats['pending_counts']}")
        
        # إحصائيات حسب التاريخ
        cursor.execute("""
            SELECT count_date, 
                   COUNT(*) as total_products,
                   SUM(CASE WHEN status = 'مكتمل' THEN 1 ELSE 0 END) as completed,
                   SUM(CASE WHEN status = 'معلق' THEN 1 ELSE 0 END) as pending
            FROM inventory_counts
            GROUP BY count_date
            ORDER BY count_date DESC
        """)
        
        date_stats = cursor.fetchall()
        
        if date_stats:
            print(f"\n📈 إحصائيات حسب التاريخ:")
            print("-" * 40)
            
            for stat in date_stats:
                print(f"• {stat['count_date']}: {stat['total_products']} منتج "
                      f"({stat['completed']} مكتمل، {stat['pending']} معلق)")
        
        # الفروقات في الجرد
        cursor.execute("""
            SELECT ic.product_id, p.name, p.stock_quantity, ic.counted_quantity,
                   (ic.counted_quantity - p.stock_quantity) as difference
            FROM inventory_counts ic
            JOIN products p ON ic.product_id = p.id
            WHERE ic.counted_quantity IS NOT NULL
            AND ic.counted_quantity != p.stock_quantity
            ORDER BY ABS(ic.counted_quantity - p.stock_quantity) DESC
            LIMIT 5
        """)
        
        variances = cursor.fetchall()
        
        if variances:
            print(f"\n⚠️ أكبر الفروقات في الجرد:")
            print("-" * 40)
            
            for var in variances:
                print(f"• {var['name']}: الحالي {var['stock_quantity']:.2f} "
                      f"← المجرد {var['counted_quantity']:.2f} "
                      f"(فرق: {var['difference']:+.2f})")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة المخزون → جرد المخزون")
        print("2. لاحظ الإحصائيات في أعلى الشاشة")
        print("3. جرب فلاتر البحث والفئات")
        print("4. انقر نقرة مزدوجة على منتج لتعديل كمية الجرد")
        print("5. جرب بدء جرد جديد")
        print("6. أدخل كميات مجردة مختلفة ولاحظ الفروقات")
        print("7. احفظ نتائج الجرد لتحديث المخزون")
        print("8. راجع حركات المخزون لرؤية تسويات الجرد")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    create_inventory_count_table()
