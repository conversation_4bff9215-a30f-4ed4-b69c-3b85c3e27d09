# -*- coding: utf-8 -*-
"""
إنشاء حركات مخزون تجريبية لاختبار تقارير المخزون
"""

import os
import sys
from datetime import datetime, timedelta
import random

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager
from utils.helpers import log_user_activity

def create_inventory_movements():
    """إنشاء حركات مخزون تجريبية"""
    db_manager = DatabaseManager()
    
    try:
        # الحصول على المنتجات الموجودة وتحويلها لقواميس
        products_result = db_manager.execute_query("SELECT id, name, stock_quantity FROM products WHERE is_active = 1")
        products = [dict(p) for p in products_result] if products_result else []
        
        if not products:
            print("❌ لا توجد منتجات في النظام. يرجى إضافة منتجات أولاً.")
            return
        
        print(f"📦 تم العثور على {len(products)} منتج")
        
        # إنشاء حركات مخزون للشهر الماضي
        movements_created = 0
        
        for days_ago in range(30, 0, -1):  # آخر 30 يوم
            # عدد الحركات في اليوم (0-8 حركات)
            daily_movements = random.randint(0, 8)
            
            for movement_num in range(daily_movements):
                movement_date = (datetime.now() - timedelta(days=days_ago)).strftime('%Y-%m-%d')
                
                # اختيار منتج عشوائي
                product = random.choice(products)
                
                # تحديد نوع الحركة
                movement_types = [
                    ('in', 'purchase', 'دخول مشتريات'),
                    ('out', 'sales', 'خروج مبيعات'),
                    ('in', 'adjustment', 'تعديل إضافة'),
                    ('out', 'adjustment', 'تعديل نقص'),
                    ('in', 'return', 'مرتجع مبيعات'),
                    ('out', 'return', 'مرتجع مشتريات')
                ]
                
                movement_type, reference_type, description = random.choice(movement_types)
                
                # تحديد الكمية بناءً على نوع الحركة
                if movement_type == 'in':
                    quantity = random.uniform(1, 20)  # دخول: 1-20
                else:
                    # خروج: لا يتجاوز المخزون الحالي
                    max_quantity = min(product['stock_quantity'], 15)
                    if max_quantity <= 0:
                        continue  # تخطي إذا لم يكن هناك مخزون
                    quantity = random.uniform(0.5, max_quantity)
                
                # رقم مرجع عشوائي
                reference_id = random.randint(1, 1000) if reference_type in ['purchase', 'sales'] else None
                
                # ملاحظات
                notes = f"{description} - كمية: {quantity:.2f}"
                
                # إدراج حركة المخزون
                movement_query = """
                    INSERT INTO inventory_movements (
                        product_id, movement_type, quantity, reference_type,
                        reference_id, notes, user_id, movement_date, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                movement_params = [
                    product['id'],
                    movement_type,
                    quantity,
                    reference_type,
                    reference_id,
                    notes,
                    1,  # المدير
                    movement_date,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ]
                
                db_manager.execute_query(movement_query, movement_params)
                
                # تحديث مخزون المنتج
                if movement_type == 'in':
                    new_quantity = product['stock_quantity'] + quantity
                else:
                    new_quantity = max(0, product['stock_quantity'] - quantity)
                
                update_query = "UPDATE products SET stock_quantity = ? WHERE id = ?"
                db_manager.execute_query(update_query, [new_quantity, product['id']])
                
                # تحديث الكمية في القائمة المحلية
                product['stock_quantity'] = new_quantity
                
                movements_created += 1
                
                if movements_created % 20 == 0:
                    print(f"✅ تم إنشاء {movements_created} حركة مخزون...")
        
        print(f"\n🎉 تم إنشاء {movements_created} حركة مخزون تجريبية بنجاح!")
        
        # تحديث الحد الأدنى للمنتجات
        update_min_stock_levels(db_manager)
        
        # عرض إحصائيات
        display_inventory_statistics(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء حركات المخزون: {str(e)}")

def update_min_stock_levels(db_manager):
    """تحديث الحد الأدنى للمنتجات بناءً على المخزون الحالي"""
    try:
        print("\n🔄 تحديث الحد الأدنى للمنتجات...")
        
        # تحديث الحد الأدنى لكل منتج
        products = db_manager.execute_query("SELECT id, stock_quantity FROM products WHERE is_active = 1")
        
        for product in products:
            # تعيين الحد الأدنى كنسبة من المخزون الحالي (20-40%)
            min_level = max(1, int(product['stock_quantity'] * random.uniform(0.2, 0.4)))
            
            update_query = "UPDATE products SET min_stock_level = ? WHERE id = ?"
            db_manager.execute_query(update_query, [min_level, product['id']])
        
        print(f"✅ تم تحديث الحد الأدنى لـ {len(products)} منتج")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الحد الأدنى: {str(e)}")

def display_inventory_statistics(db_manager):
    """عرض إحصائيات المخزون"""
    try:
        print("\n📊 إحصائيات المخزون:")
        print("=" * 50)
        
        # إجمالي المنتجات
        total_query = "SELECT COUNT(*) as count FROM products WHERE is_active = 1"
        result = db_manager.execute_query(total_query)
        total_products = result[0]['count'] if result else 0
        print(f"📦 إجمالي المنتجات النشطة: {total_products}")
        
        # المنتجات المتوفرة
        available_query = """
            SELECT COUNT(*) as count FROM products 
            WHERE is_active = 1 AND stock_quantity > min_stock_level
        """
        result = db_manager.execute_query(available_query)
        available_products = result[0]['count'] if result else 0
        print(f"✅ المنتجات المتوفرة: {available_products}")
        
        # المنتجات الناقصة
        low_stock_query = """
            SELECT COUNT(*) as count FROM products 
            WHERE is_active = 1 AND stock_quantity <= min_stock_level AND stock_quantity > 0
        """
        result = db_manager.execute_query(low_stock_query)
        low_stock = result[0]['count'] if result else 0
        print(f"⚠️ المنتجات الناقصة: {low_stock}")
        
        # المنتجات المنتهية
        out_of_stock_query = """
            SELECT COUNT(*) as count FROM products 
            WHERE is_active = 1 AND stock_quantity = 0
        """
        result = db_manager.execute_query(out_of_stock_query)
        out_of_stock = result[0]['count'] if result else 0
        print(f"❌ المنتجات المنتهية: {out_of_stock}")
        
        # قيمة المخزون
        value_query = """
            SELECT 
                COALESCE(SUM(stock_quantity * cost_price), 0) as cost_value,
                COALESCE(SUM(stock_quantity * selling_price), 0) as selling_value
            FROM products WHERE is_active = 1
        """
        result = db_manager.execute_query(value_query)
        if result:
            cost_value = result[0]['cost_value']
            selling_value = result[0]['selling_value']
            potential_profit = selling_value - cost_value
            
            print(f"💰 قيمة المخزون (تكلفة): {cost_value:,.2f}")
            print(f"💵 قيمة المخزون (بيع): {selling_value:,.2f}")
            print(f"📈 الربح المحتمل: {potential_profit:,.2f}")
        
        # إحصائيات حركة المخزون
        movements_query = """
            SELECT 
                COUNT(*) as total_movements,
                COUNT(CASE WHEN movement_type = 'in' THEN 1 END) as in_movements,
                COUNT(CASE WHEN movement_type = 'out' THEN 1 END) as out_movements,
                COALESCE(SUM(CASE WHEN movement_type = 'in' THEN quantity ELSE 0 END), 0) as total_in,
                COALESCE(SUM(CASE WHEN movement_type = 'out' THEN quantity ELSE 0 END), 0) as total_out
            FROM inventory_movements
        """
        result = db_manager.execute_query(movements_query)
        if result:
            data = result[0]
            print(f"\n📋 إجمالي حركات المخزون: {data['total_movements']}")
            print(f"📥 حركات الدخول: {data['in_movements']} ({data['total_in']:.2f} وحدة)")
            print(f"📤 حركات الخروج: {data['out_movements']} ({data['total_out']:.2f} وحدة)")
        
        # أكثر المنتجات حركة
        active_products_query = """
            SELECT p.name, COUNT(im.id) as movement_count
            FROM products p
            JOIN inventory_movements im ON p.id = im.product_id
            GROUP BY p.id, p.name
            ORDER BY COUNT(im.id) DESC
            LIMIT 5
        """
        result = db_manager.execute_query(active_products_query)
        if result:
            print(f"\n🏆 أكثر 5 منتجات حركة:")
            print("-" * 30)
            for product in result:
                print(f"• {product['name']}: {product['movement_count']} حركة")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة التقارير → تقرير المخزون")
        print("2. جرب جميع أنواع التقارير الستة")
        print("3. لاحظ الإحصائيات السريعة في أعلى الشاشة")
        print("4. تحقق من تقرير حركة المخزون لرؤية الحركات المنشأة")
        print("5. راجع تقرير المنتجات الناقصة والمنتهية")
        print("6. اختبر تقرير تقييم المخزون لرؤية الأرباح المحتملة")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    print("🚀 بدء إنشاء حركات المخزون التجريبية...")
    create_inventory_movements()
    print("\n✅ جاهز لاختبار تقارير المخزون!")
