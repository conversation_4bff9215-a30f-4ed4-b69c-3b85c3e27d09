# -*- coding: utf-8 -*-
"""
إنشاء جدول حركات المخزون في قاعدة البيانات
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import DATABASE_PATH

def create_inventory_movements_table():
    """إنشاء جدول حركات المخزون"""
    try:
        print("🚀 بدء إنشاء جدول حركات المخزون...")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_movements'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("ℹ️ جدول حركات المخزون موجود مسبقاً")
        else:
            # إنشاء جدول حركات المخزون
            create_table_query = """
                CREATE TABLE inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment')),
                    quantity REAL NOT NULL,
                    reference_type TEXT,
                    reference_id TEXT,
                    notes TEXT,
                    user_id INTEGER NOT NULL,
                    movement_date DATE NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """
            
            cursor.execute(create_table_query)
            print("✅ تم إنشاء جدول حركات المخزون بنجاح")
        
        # إنشاء فهارس لتحسين الأداء
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_product_id ON inventory_movements (product_id)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_movement_date ON inventory_movements (movement_date)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_movement_type ON inventory_movements (movement_type)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_reference_type ON inventory_movements (reference_type)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_user_id ON inventory_movements (user_id)"
        ]
        
        for index_query in indexes:
            cursor.execute(index_query)
        
        print("✅ تم إنشاء الفهارس بنجاح")
        
        # إنشاء حركات مخزون تجريبية
        create_sample_movements(cursor)
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض الإحصائيات
        display_statistics(cursor)
        
        conn.close()
        print("\n✅ تم إعداد نظام حركات المخزون بنجاح!")
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء جدول حركات المخزون: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

def create_sample_movements(cursor):
    """إنشاء حركات مخزون تجريبية"""
    try:
        print("\n🔄 إنشاء حركات مخزون تجريبية...")
        
        # جلب المنتجات النشطة
        cursor.execute("SELECT id, name, stock_quantity FROM products WHERE is_active = 1")
        products = cursor.fetchall()
        
        if not products:
            print("❌ لا توجد منتجات لإنشاء حركات مخزون لها")
            return
        
        import random
        
        # تواريخ حركات تجريبية
        today = datetime.now()
        movement_dates = []
        
        # إنشاء تواريخ للشهر الماضي
        for i in range(30):
            date = today - timedelta(days=i)
            movement_dates.append(date.strftime('%Y-%m-%d'))
        
        movements_created = 0
        
        # أنواع الحركات والمراجع
        movement_types = ['in', 'out', 'adjustment']
        reference_types = ['sales_invoice', 'purchase_invoice', 'inventory_count', 'adjustment', 'manual']
        
        # إنشاء حركات متنوعة
        for _ in range(50):  # إنشاء 50 حركة تجريبية
            product = random.choice(products)
            movement_type = random.choice(movement_types)
            reference_type = random.choice(reference_types)
            movement_date = random.choice(movement_dates)
            
            # تحديد الكمية حسب نوع الحركة
            if movement_type == 'in':
                quantity = random.uniform(1, 20)
            elif movement_type == 'out':
                quantity = random.uniform(1, min(10, product['stock_quantity']))
            else:  # adjustment
                quantity = random.uniform(-5, 5)
            
            # تحديد رقم المرجع
            if reference_type in ['sales_invoice', 'purchase_invoice']:
                reference_id = f"INV-{random.randint(1000, 9999)}"
            elif reference_type == 'inventory_count':
                reference_id = f"COUNT-{random.randint(100, 999)}"
            else:
                reference_id = None
            
            # ملاحظات عشوائية
            notes_options = [
                None,
                "حركة تلقائية",
                "تسوية مخزون",
                "تحديث الكمية",
                "حركة يدوية",
                "تصحيح خطأ",
                "جرد دوري"
            ]
            notes = random.choice(notes_options)
            
            # إدراج الحركة
            insert_query = """
                INSERT INTO inventory_movements 
                (product_id, movement_type, quantity, reference_type, reference_id, 
                 notes, user_id, movement_date, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor.execute(insert_query, (
                product['id'],
                movement_type,
                round(quantity, 2),
                reference_type,
                reference_id,
                notes,
                1,  # المدير
                movement_date,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            movements_created += 1
        
        print(f"✅ تم إنشاء {movements_created} حركة مخزون تجريبية")
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء حركات المخزون التجريبية: {str(e)}")

def display_statistics(cursor):
    """عرض إحصائيات حركات المخزون"""
    try:
        print("\n📊 إحصائيات حركات المخزون:")
        print("=" * 50)
        
        # إحصائيات عامة
        cursor.execute("""
            SELECT 
                COUNT(*) as total_movements,
                COUNT(DISTINCT product_id) as products_with_movements,
                SUM(CASE WHEN movement_type = 'in' THEN 1 ELSE 0 END) as in_movements,
                SUM(CASE WHEN movement_type = 'out' THEN 1 ELSE 0 END) as out_movements,
                SUM(CASE WHEN movement_type = 'adjustment' THEN 1 ELSE 0 END) as adjustment_movements
            FROM inventory_movements
        """)
        
        stats = cursor.fetchone()
        
        print(f"📋 إجمالي الحركات: {stats['total_movements']}")
        print(f"📦 المنتجات ذات الحركات: {stats['products_with_movements']}")
        print(f"📈 الحركات الواردة: {stats['in_movements']}")
        print(f"📉 الحركات الصادرة: {stats['out_movements']}")
        print(f"⚖️ حركات التسوية: {stats['adjustment_movements']}")
        
        # إحصائيات حسب نوع المرجع
        cursor.execute("""
            SELECT reference_type, COUNT(*) as count
            FROM inventory_movements
            GROUP BY reference_type
            ORDER BY count DESC
        """)
        
        reference_stats = cursor.fetchall()
        
        if reference_stats:
            print(f"\n📈 إحصائيات حسب نوع المرجع:")
            print("-" * 40)
            
            reference_names = {
                'sales_invoice': 'فواتير المبيعات',
                'purchase_invoice': 'فواتير المشتريات',
                'inventory_count': 'جرد المخزون',
                'adjustment': 'تسويات',
                'manual': 'حركات يدوية'
            }
            
            for stat in reference_stats:
                ref_name = reference_names.get(stat['reference_type'], stat['reference_type'])
                print(f"• {ref_name}: {stat['count']} حركة")
        
        # أحدث الحركات
        cursor.execute("""
            SELECT im.movement_date, p.name as product_name, im.movement_type, 
                   im.quantity, im.reference_type
            FROM inventory_movements im
            JOIN products p ON im.product_id = p.id
            ORDER BY im.movement_date DESC, im.id DESC
            LIMIT 10
        """)
        
        recent_movements = cursor.fetchall()
        
        if recent_movements:
            print(f"\n📅 أحدث الحركات:")
            print("-" * 40)
            
            movement_names = {
                'in': 'وارد',
                'out': 'صادر',
                'adjustment': 'تسوية'
            }
            
            for movement in recent_movements:
                movement_type_ar = movement_names.get(movement['movement_type'], movement['movement_type'])
                print(f"• {movement['movement_date']}: {movement['product_name']} - "
                      f"{movement_type_ar} {movement['quantity']:.2f}")
        
        # إحصائيات الكميات
        cursor.execute("""
            SELECT 
                SUM(CASE WHEN movement_type = 'in' THEN quantity ELSE 0 END) as total_in,
                SUM(CASE WHEN movement_type = 'out' THEN quantity ELSE 0 END) as total_out,
                SUM(CASE WHEN movement_type = 'adjustment' AND quantity > 0 THEN quantity ELSE 0 END) as positive_adjustments,
                SUM(CASE WHEN movement_type = 'adjustment' AND quantity < 0 THEN ABS(quantity) ELSE 0 END) as negative_adjustments
            FROM inventory_movements
        """)
        
        quantity_stats = cursor.fetchone()
        
        if quantity_stats:
            print(f"\n📊 إحصائيات الكميات:")
            print("-" * 40)
            print(f"📈 إجمالي الوارد: {quantity_stats['total_in']:.2f}")
            print(f"📉 إجمالي الصادر: {quantity_stats['total_out']:.2f}")
            print(f"⬆️ تسويات موجبة: {quantity_stats['positive_adjustments']:.2f}")
            print(f"⬇️ تسويات سالبة: {quantity_stats['negative_adjustments']:.2f}")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة المخزون → حركات المخزون")
        print("2. لاحظ الإحصائيات في أعلى الشاشة")
        print("3. جرب فلاتر البحث المختلفة:")
        print("   - فلتر نوع الحركة (وارد/صادر/تسوية)")
        print("   - فلتر نوع المرجع (مبيعات/مشتريات/جرد/تسوية)")
        print("   - البحث النصي في اسم المنتج أو الملاحظات")
        print("4. استخدم الفترات السريعة للتصفية")
        print("5. انقر نقرة مزدوجة على حركة لعرض التفاصيل")
        print("6. جرب إنشاء حركة جديدة")
        print("7. لاحظ الألوان المختلفة للحركات:")
        print("   - أخضر للحركات الواردة")
        print("   - أحمر للحركات الصادرة")
        print("   - أصفر لحركات التسوية")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    create_inventory_movements_table()
