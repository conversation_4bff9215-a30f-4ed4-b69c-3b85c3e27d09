# -*- coding: utf-8 -*-
"""
إنشاء جدول المرتجعات في قاعدة البيانات
"""

import os
import sys
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager

def create_returns_table():
    """إنشاء جدول المرتجعات"""
    db_manager = DatabaseManager()
    
    try:
        # إنشاء جدول المرتجعات
        create_table_query = """
            CREATE TABLE IF NOT EXISTS returns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_number TEXT UNIQUE NOT NULL,
                return_date DATE NOT NULL,
                return_type TEXT NOT NULL CHECK (return_type IN ('sales', 'purchase')),
                invoice_number TEXT NOT NULL,
                customer_id INTEGER,
                supplier_id INTEGER,
                product_id INTEGER,
                quantity REAL NOT NULL DEFAULT 0,
                reason TEXT,
                amount REAL NOT NULL DEFAULT 0,
                status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed')),
                notes TEXT,
                user_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """
        
        db_manager.execute_query(create_table_query)
        print("✅ تم إنشاء جدول المرتجعات بنجاح")
        
        # إنشاء فهارس لتحسين الأداء
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_returns_return_number ON returns (return_number)",
            "CREATE INDEX IF NOT EXISTS idx_returns_return_date ON returns (return_date)",
            "CREATE INDEX IF NOT EXISTS idx_returns_return_type ON returns (return_type)",
            "CREATE INDEX IF NOT EXISTS idx_returns_invoice_number ON returns (invoice_number)",
            "CREATE INDEX IF NOT EXISTS idx_returns_customer_id ON returns (customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_returns_supplier_id ON returns (supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_returns_product_id ON returns (product_id)",
            "CREATE INDEX IF NOT EXISTS idx_returns_status ON returns (status)",
            "CREATE INDEX IF NOT EXISTS idx_returns_user_id ON returns (user_id)"
        ]
        
        for index_query in indexes:
            db_manager.execute_query(index_query)
        
        print("✅ تم إنشاء الفهارس بنجاح")
        
        # إنشاء بيانات تجريبية
        create_sample_returns(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء جدول المرتجعات: {str(e)}")

def create_sample_returns(db_manager):
    """إنشاء مرتجعات تجريبية"""
    try:
        print("\n🔄 إنشاء مرتجعات تجريبية...")
        
        # التحقق من وجود فواتير مبيعات ومشتريات
        sales_invoices = db_manager.execute_query("SELECT id, invoice_number, customer_id FROM sales_invoices LIMIT 10")
        purchase_invoices = db_manager.execute_query("SELECT id, invoice_number, supplier_id FROM purchase_invoices LIMIT 10")
        products = db_manager.execute_query("SELECT id, name FROM products LIMIT 20")
        
        if not sales_invoices or not purchase_invoices or not products:
            print("❌ لا توجد بيانات كافية لإنشاء مرتجعات تجريبية")
            return
        
        returns_created = 0
        
        # إنشاء مرتجعات مبيعات
        for i, invoice in enumerate(sales_invoices[:5]):
            product = products[i % len(products)]
            
            return_number = f"RET-{(returns_created + 1):06d}"
            return_date = datetime.now().strftime('%Y-%m-%d')
            quantity = round(random.uniform(1, 5), 2)
            amount = round(random.uniform(50, 500), 2)
            
            reasons = ['منتج معيب', 'خطأ في الطلب', 'تلف أثناء النقل', 'عدم مطابقة المواصفات']
            reason = random.choice(reasons)
            
            statuses = ['pending', 'approved', 'rejected', 'completed']
            status = random.choice(statuses)
            
            insert_query = """
                INSERT INTO returns (
                    return_number, return_date, return_type, invoice_number,
                    customer_id, product_id, quantity, reason, amount, status,
                    notes, user_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = [
                return_number,
                return_date,
                'sales',
                invoice['invoice_number'],
                invoice['customer_id'],
                product['id'],
                quantity,
                reason,
                amount,
                status,
                f"مرتجع تجريبي - {reason}",
                1,  # المدير
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ]
            
            db_manager.execute_query(insert_query, params)
            returns_created += 1
        
        # إنشاء مرتجعات مشتريات
        for i, invoice in enumerate(purchase_invoices[:5]):
            product = products[(i + 5) % len(products)]
            
            return_number = f"RET-{(returns_created + 1):06d}"
            return_date = datetime.now().strftime('%Y-%m-%d')
            quantity = round(random.uniform(1, 10), 2)
            amount = round(random.uniform(100, 1000), 2)
            
            reasons = ['منتج معيب', 'خطأ في الطلب', 'تلف أثناء النقل', 'عدم مطابقة المواصفات']
            reason = random.choice(reasons)
            
            statuses = ['pending', 'approved', 'rejected', 'completed']
            status = random.choice(statuses)
            
            insert_query = """
                INSERT INTO returns (
                    return_number, return_date, return_type, invoice_number,
                    supplier_id, product_id, quantity, reason, amount, status,
                    notes, user_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = [
                return_number,
                return_date,
                'purchase',
                invoice['invoice_number'],
                invoice['supplier_id'],
                product['id'],
                quantity,
                reason,
                amount,
                status,
                f"مرتجع تجريبي - {reason}",
                1,  # المدير
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ]
            
            db_manager.execute_query(insert_query, params)
            returns_created += 1
        
        print(f"✅ تم إنشاء {returns_created} مرتجع تجريبي")
        
        # عرض إحصائيات
        display_returns_statistics(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء المرتجعات التجريبية: {str(e)}")

def display_returns_statistics(db_manager):
    """عرض إحصائيات المرتجعات"""
    try:
        print("\n📊 إحصائيات المرتجعات:")
        print("=" * 50)
        
        # إحصائيات عامة
        total_query = """
            SELECT 
                COUNT(*) as total_returns,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount
            FROM returns
        """
        
        result = db_manager.execute_query(total_query)
        if result:
            data = result[0]
            print(f"📋 إجمالي المرتجعات: {data['total_returns']}")
            print(f"💰 إجمالي المبلغ: {data['total_amount']:,.2f}")
            print(f"📊 متوسط المرتجع: {data['avg_amount']:,.2f}")
        
        # إحصائيات حسب النوع
        type_query = """
            SELECT 
                return_type,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM returns
            GROUP BY return_type
        """
        
        result = db_manager.execute_query(type_query)
        if result:
            print(f"\n📈 إحصائيات حسب النوع:")
            print("-" * 30)
            type_names = {
                'sales': 'مرتجعات المبيعات',
                'purchase': 'مرتجعات المشتريات'
            }
            
            for item in result:
                type_name = type_names.get(item['return_type'], item['return_type'])
                print(f"• {type_name}: {item['count']} مرتجع - {item['total_amount']:,.2f}")
        
        # إحصائيات حسب الحالة
        status_query = """
            SELECT 
                status,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM returns
            GROUP BY status
        """
        
        result = db_manager.execute_query(status_query)
        if result:
            print(f"\n📋 إحصائيات حسب الحالة:")
            print("-" * 30)
            status_names = {
                'pending': 'معلق',
                'approved': 'مقبول',
                'rejected': 'مرفوض',
                'completed': 'مكتمل'
            }
            
            for item in result:
                status_name = status_names.get(item['status'], item['status'])
                print(f"• {status_name}: {item['count']} مرتجع - {item['total_amount']:,.2f}")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة المبيعات → مرتجعات المبيعات")
        print("2. اذهب إلى قائمة المشتريات → مرتجعات المشتريات")
        print("3. جرب إنشاء مرتجع جديد")
        print("4. جرب تعديل حالة المرتجعات")
        print("5. استخدم الفلاتر المختلفة للبحث")
        print("6. لاحظ الإحصائيات في أعلى الشاشة")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    import random
    print("🚀 بدء إنشاء جدول المرتجعات...")
    create_returns_table()
    print("\n✅ جاهز لاختبار نظام المرتجعات!")
