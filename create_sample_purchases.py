# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية للمشتريات لاختبار التقارير
"""

import os
import sys
from datetime import datetime, timedelta
import random

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager
from utils.helpers import log_user_activity

def create_sample_purchases():
    """إنشاء فواتير مشتريات تجريبية"""
    db_manager = DatabaseManager()
    
    try:
        # الحصول على الموردين والمنتجات الموجودين
        suppliers = db_manager.execute_query("SELECT id, name FROM suppliers WHERE is_active = 1")
        products = db_manager.execute_query("SELECT id, name, selling_price FROM products")
        
        if not suppliers:
            print("❌ لا توجد موردين في النظام. يرجى إضافة موردين أولاً.")
            return
            
        if not products:
            print("❌ لا توجد منتجات في النظام. يرجى إضافة منتجات أولاً.")
            return
        
        # الحصول على آخر رقم فاتورة
        last_invoice_query = "SELECT invoice_number FROM purchase_invoices ORDER BY id DESC LIMIT 1"
        last_invoice_result = db_manager.execute_query(last_invoice_query)

        if last_invoice_result and last_invoice_result[0]['invoice_number']:
            last_number = int(last_invoice_result[0]['invoice_number'].split('-')[1])
        else:
            last_number = 0

        # إنشاء فواتير مشتريات للشهر الماضي والشهر الحالي
        invoices_created = 0
        
        for days_ago in range(60, 0, -1):  # آخر 60 يوم
            # عدد الفواتير في اليوم (0-3 فواتير)
            daily_invoices = random.randint(0, 3)
            
            for invoice_num in range(daily_invoices):
                invoice_date = (datetime.now() - timedelta(days=days_ago)).strftime('%Y-%m-%d')
                
                # اختيار مورد عشوائي
                supplier = random.choice(suppliers)
                
                # إنشاء رقم فاتورة
                invoice_number = f"PUR-{(last_number + invoices_created + 1):06d}"
                
                # حساب المبالغ
                subtotal = 0
                invoice_items = []
                
                # إضافة منتجات عشوائية (1-5 منتجات)
                num_products = random.randint(1, 5)
                selected_products = random.sample(products, min(num_products, len(products)))
                
                for product in selected_products:
                    quantity = random.uniform(1, 20)  # كمية عشوائية
                    unit_price = product['selling_price'] * random.uniform(0.6, 0.9)  # سعر شراء أقل من البيع
                    total_amount = quantity * unit_price
                    subtotal += total_amount
                    
                    invoice_items.append({
                        'product_id': product['id'],
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'total_amount': total_amount
                    })
                
                # حساب الخصم والضريبة
                discount_rate = random.uniform(0, 0.1)  # خصم 0-10%
                tax_rate = 0.15  # ضريبة 15%
                
                discount_amount = subtotal * discount_rate
                tax_amount = (subtotal - discount_amount) * tax_rate
                total_amount = subtotal - discount_amount + tax_amount
                
                # حساب المدفوع والمتبقي
                payment_ratio = random.uniform(0.5, 1.0)  # دفع 50-100%
                paid_amount = total_amount * payment_ratio
                remaining_amount = total_amount - paid_amount
                
                # إدراج فاتورة الشراء
                invoice_query = """
                    INSERT INTO purchase_invoices (
                        invoice_number, supplier_id, invoice_date, subtotal,
                        discount_amount, tax_amount, total_amount, paid_amount,
                        remaining_amount, notes, user_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                invoice_params = [
                    invoice_number,
                    supplier['id'],
                    invoice_date,
                    subtotal,
                    discount_amount,
                    tax_amount,
                    total_amount,
                    paid_amount,
                    remaining_amount,
                    f"فاتورة شراء تجريبية من {supplier['name']}",
                    1,  # المدير
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ]
                
                db_manager.execute_query(invoice_query, invoice_params)
                
                # الحصول على معرف الفاتورة
                invoice_id_query = "SELECT id FROM purchase_invoices WHERE invoice_number = ?"
                invoice_result = db_manager.execute_query(invoice_id_query, [invoice_number])
                invoice_id = invoice_result[0]['id'] if invoice_result else None
                
                if invoice_id:
                    # إدراج عناصر الفاتورة
                    for item in invoice_items:
                        item_query = """
                            INSERT INTO purchase_invoice_items (
                                invoice_id, product_id, quantity, unit_price, total_amount
                            ) VALUES (?, ?, ?, ?, ?)
                        """
                        
                        item_params = [
                            invoice_id,
                            item['product_id'],
                            item['quantity'],
                            item['unit_price'],
                            item['total_amount']
                        ]
                        
                        db_manager.execute_query(item_query, item_params)
                    
                    # تسجيل العملية
                    details = f"فاتورة تجريبية رقم: {invoice_number}, المورد: {supplier['name']}, المبلغ: {total_amount:.2f}"
                    log_user_activity(
                        db_manager,
                        1,  # المدير
                        "إنشاء فاتورة شراء تجريبية",
                        details,
                        "purchase_invoices",
                        invoice_id
                    )
                    
                    invoices_created += 1
                    
                    if invoices_created % 10 == 0:
                        print(f"✅ تم إنشاء {invoices_created} فاتورة...")
        
        print(f"\n🎉 تم إنشاء {invoices_created} فاتورة شراء تجريبية بنجاح!")
        
        # عرض إحصائيات
        display_statistics(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء البيانات التجريبية: {str(e)}")

def display_statistics(db_manager):
    """عرض إحصائيات البيانات التجريبية"""
    try:
        print("\n📊 إحصائيات البيانات التجريبية:")
        print("=" * 50)
        
        # إجمالي فواتير المشتريات
        total_query = """
            SELECT COUNT(*) as total_invoices,
                   SUM(total_amount) as total_amount,
                   SUM(paid_amount) as total_paid,
                   SUM(remaining_amount) as total_remaining
            FROM purchase_invoices
        """
        
        result = db_manager.execute_query(total_query)
        if result:
            data = result[0]
            print(f"📋 إجمالي الفواتير: {data['total_invoices']}")
            print(f"💰 إجمالي المبلغ: {data['total_amount']:.2f}")
            print(f"✅ إجمالي المدفوع: {data['total_paid']:.2f}")
            print(f"⏳ إجمالي المتبقي: {data['total_remaining']:.2f}")
        
        # إحصائيات الموردين
        suppliers_query = """
            SELECT s.name,
                   COUNT(pi.id) as invoice_count,
                   SUM(pi.total_amount) as total_amount
            FROM suppliers s
            LEFT JOIN purchase_invoices pi ON s.id = pi.supplier_id
            GROUP BY s.id, s.name
            HAVING COUNT(pi.id) > 0
            ORDER BY SUM(pi.total_amount) DESC
            LIMIT 5
        """
        
        suppliers_result = db_manager.execute_query(suppliers_query)
        if suppliers_result:
            print("\n🏢 أكثر 5 موردين شراءً:")
            print("-" * 30)
            for supplier in suppliers_result:
                print(f"• {supplier['name']}: {supplier['invoice_count']} فاتورة - {supplier['total_amount']:.2f}")
        
        # إحصائيات المنتجات
        products_query = """
            SELECT p.name,
                   SUM(pii.quantity) as total_quantity,
                   SUM(pii.total_amount) as total_amount
            FROM products p
            JOIN purchase_invoice_items pii ON p.id = pii.product_id
            GROUP BY p.id, p.name
            ORDER BY SUM(pii.total_amount) DESC
            LIMIT 5
        """
        
        products_result = db_manager.execute_query(products_query)
        if products_result:
            print("\n📦 أكثر 5 منتجات شراءً:")
            print("-" * 30)
            for product in products_result:
                print(f"• {product['name']}: {product['total_quantity']:.2f} - {product['total_amount']:.2f}")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة التقارير → تقرير المشتريات")
        print("2. جرب الفترات المختلفة (اليوم، هذا الأسبوع، هذا الشهر)")
        print("3. اختبر جميع أنواع التقارير الستة")
        print("4. لاحظ الإحصائيات الفورية في أعلى الشاشة")
        print("5. راجع سجل النشاط لرؤية العمليات المسجلة")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    print("🚀 بدء إنشاء بيانات المشتريات التجريبية...")
    create_sample_purchases()
    print("\n✅ جاهز لاختبار تقارير المشتريات!")
