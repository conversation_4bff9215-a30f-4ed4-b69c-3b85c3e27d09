# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية للمبيعات لاختبار تقرير الأرباح والخسائر
"""

import os
import sys
from datetime import datetime, timedelta
import random

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager
from utils.helpers import log_user_activity

def create_sample_sales():
    """إنشاء فواتير مبيعات تجريبية"""
    db_manager = DatabaseManager()
    
    try:
        # الحصول على العملاء والمنتجات الموجودين
        customers = db_manager.execute_query("SELECT id, name FROM customers WHERE is_active = 1")
        products = db_manager.execute_query("SELECT id, name, selling_price FROM products")
        
        if not products:
            print("❌ لا توجد منتجات في النظام. يرجى إضافة منتجات أولاً.")
            return
        
        # الحصول على آخر رقم فاتورة
        last_invoice_query = "SELECT invoice_number FROM sales_invoices ORDER BY id DESC LIMIT 1"
        last_invoice_result = db_manager.execute_query(last_invoice_query)
        
        if last_invoice_result and last_invoice_result[0]['invoice_number']:
            last_number = int(last_invoice_result[0]['invoice_number'].split('-')[1])
        else:
            last_number = 0
        
        # إنشاء فواتير مبيعات للشهر الماضي والشهر الحالي
        invoices_created = 0
        
        for days_ago in range(60, 0, -1):  # آخر 60 يوم
            # عدد الفواتير في اليوم (0-5 فواتير)
            daily_invoices = random.randint(0, 5)
            
            for invoice_num in range(daily_invoices):
                invoice_date = (datetime.now() - timedelta(days=days_ago)).strftime('%Y-%m-%d')
                
                # اختيار عميل عشوائي (أو عميل نقدي)
                customer_id = None
                if customers and random.choice([True, False]):  # 50% احتمال عميل مسجل
                    customer = random.choice(customers)
                    customer_id = customer['id']
                
                # إنشاء رقم فاتورة
                invoice_number = f"INV-{(last_number + invoices_created + 1):06d}"
                
                # حساب المبالغ
                subtotal = 0
                invoice_items = []
                
                # إضافة منتجات عشوائية (1-6 منتجات)
                num_products = random.randint(1, 6)
                selected_products = random.sample(products, min(num_products, len(products)))
                
                for product in selected_products:
                    quantity = random.uniform(1, 10)  # كمية عشوائية
                    unit_price = product['selling_price'] * random.uniform(0.9, 1.1)  # سعر متغير قليلاً
                    total_amount = quantity * unit_price
                    subtotal += total_amount
                    
                    invoice_items.append({
                        'product_id': product['id'],
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'total_amount': total_amount
                    })
                
                # حساب الخصم والضريبة
                discount_rate = random.uniform(0, 0.15)  # خصم 0-15%
                tax_rate = 0.15  # ضريبة 15%
                
                discount_amount = subtotal * discount_rate
                tax_amount = (subtotal - discount_amount) * tax_rate
                total_amount = subtotal - discount_amount + tax_amount
                
                # حساب المدفوع والمتبقي
                payment_ratio = random.uniform(0.7, 1.0)  # دفع 70-100%
                paid_amount = total_amount * payment_ratio
                remaining_amount = total_amount - paid_amount
                
                # إدراج فاتورة المبيعات
                invoice_query = """
                    INSERT INTO sales_invoices (
                        invoice_number, customer_id, invoice_date, subtotal,
                        discount_amount, tax_amount, total_amount, paid_amount,
                        remaining_amount, notes, user_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                customer_name = "عميل نقدي"
                if customer_id and customers:
                    customer_name = next((c['name'] for c in customers if c['id'] == customer_id), "عميل نقدي")
                
                invoice_params = [
                    invoice_number,
                    customer_id,
                    invoice_date,
                    subtotal,
                    discount_amount,
                    tax_amount,
                    total_amount,
                    paid_amount,
                    remaining_amount,
                    f"فاتورة مبيعات تجريبية لـ {customer_name}",
                    1,  # المدير
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ]
                
                db_manager.execute_query(invoice_query, invoice_params)
                
                # الحصول على معرف الفاتورة
                invoice_id_query = "SELECT id FROM sales_invoices WHERE invoice_number = ?"
                invoice_result = db_manager.execute_query(invoice_id_query, [invoice_number])
                invoice_id = invoice_result[0]['id'] if invoice_result else None
                
                if invoice_id:
                    # إدراج عناصر الفاتورة
                    for item in invoice_items:
                        item_query = """
                            INSERT INTO sales_invoice_items (
                                invoice_id, product_id, quantity, unit_price, total_amount
                            ) VALUES (?, ?, ?, ?, ?)
                        """
                        
                        item_params = [
                            invoice_id,
                            item['product_id'],
                            item['quantity'],
                            item['unit_price'],
                            item['total_amount']
                        ]
                        
                        db_manager.execute_query(item_query, item_params)
                    
                    # تسجيل العملية
                    details = f"فاتورة تجريبية رقم: {invoice_number}, العميل: {customer_name}, المبلغ: {total_amount:.2f}"
                    log_user_activity(
                        db_manager,
                        1,  # المدير
                        "إنشاء فاتورة مبيعات تجريبية",
                        details,
                        "sales_invoices",
                        invoice_id
                    )
                    
                    invoices_created += 1
                    
                    if invoices_created % 10 == 0:
                        print(f"✅ تم إنشاء {invoices_created} فاتورة مبيعات...")
        
        print(f"\n🎉 تم إنشاء {invoices_created} فاتورة مبيعات تجريبية بنجاح!")
        
        # عرض إحصائيات
        display_statistics(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء البيانات التجريبية: {str(e)}")

def display_statistics(db_manager):
    """عرض إحصائيات البيانات التجريبية"""
    try:
        print("\n📊 إحصائيات البيانات التجريبية:")
        print("=" * 50)
        
        # إحصائيات المبيعات
        sales_query = """
            SELECT COUNT(*) as total_invoices,
                   SUM(total_amount) as total_amount,
                   SUM(paid_amount) as total_paid,
                   SUM(remaining_amount) as total_remaining,
                   AVG(total_amount) as avg_invoice
            FROM sales_invoices
        """
        
        sales_result = db_manager.execute_query(sales_query)
        if sales_result:
            data = sales_result[0]
            print(f"📋 إجمالي فواتير المبيعات: {data['total_invoices']}")
            print(f"💰 إجمالي مبلغ المبيعات: {data['total_amount']:.2f}")
            print(f"✅ إجمالي المدفوع: {data['total_paid']:.2f}")
            print(f"⏳ إجمالي المتبقي: {data['total_remaining']:.2f}")
            print(f"📊 متوسط الفاتورة: {data['avg_invoice']:.2f}")
        
        # إحصائيات المشتريات
        purchases_query = """
            SELECT COUNT(*) as total_invoices,
                   SUM(total_amount) as total_amount
            FROM purchase_invoices
        """
        
        purchases_result = db_manager.execute_query(purchases_query)
        if purchases_result:
            data = purchases_result[0]
            print(f"\n📦 إجمالي فواتير المشتريات: {data['total_invoices']}")
            print(f"💸 إجمالي مبلغ المشتريات: {data['total_amount']:.2f}")
        
        # حساب الربح التقديري
        if sales_result and purchases_result:
            sales_total = sales_result[0]['total_amount'] or 0
            purchases_total = purchases_result[0]['total_amount'] or 0
            estimated_profit = sales_total - (purchases_total * 0.8)  # تقدير
            
            print(f"\n💡 تقدير الربح الإجمالي: {estimated_profit:.2f}")
            if sales_total > 0:
                profit_margin = (estimated_profit / sales_total) * 100
                print(f"📈 هامش الربح التقديري: {profit_margin:.2f}%")
        
        # أكثر المنتجات مبيعاً
        top_products_query = """
            SELECT p.name,
                   SUM(sii.quantity) as total_quantity,
                   SUM(sii.total_amount) as total_amount
            FROM products p
            JOIN sales_invoice_items sii ON p.id = sii.product_id
            GROUP BY p.id, p.name
            ORDER BY SUM(sii.total_amount) DESC
            LIMIT 5
        """
        
        top_products_result = db_manager.execute_query(top_products_query)
        if top_products_result:
            print("\n🏆 أكثر 5 منتجات مبيعاً:")
            print("-" * 30)
            for product in top_products_result:
                print(f"• {product['name']}: {product['total_quantity']:.2f} - {product['total_amount']:.2f}")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة التقارير → تقرير الأرباح والخسائر")
        print("2. جرب الفترات المختلفة (اليوم، هذا الأسبوع، هذا الشهر، هذا العام)")
        print("3. لاحظ حساب الإيرادات وتكلفة البضاعة المباعة")
        print("4. تحقق من النسب المالية في نهاية التقرير")
        print("5. راجع سجل النشاط لرؤية العمليات المسجلة")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    print("🚀 بدء إنشاء بيانات المبيعات التجريبية...")
    create_sample_sales()
    print("\n✅ جاهز لاختبار تقرير الأرباح والخسائر!")
