# -*- coding: utf-8 -*-
"""
إنشاء مدفوعات تجريبية للموردين لاختبار كشف حساب المورد
"""

import os
import sys
from datetime import datetime, timedelta
import random

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager
from utils.helpers import log_user_activity

def create_supplier_payments():
    """إنشاء مدفوعات تجريبية للموردين"""
    db_manager = DatabaseManager()
    
    try:
        # الحصول على فواتير المشتريات الموجودة
        invoices_query = """
            SELECT pi.id, pi.invoice_number, pi.supplier_id, pi.total_amount, 
                   pi.paid_amount, pi.remaining_amount, s.name as supplier_name
            FROM purchase_invoices pi
            JOIN suppliers s ON pi.supplier_id = s.id
            WHERE pi.remaining_amount > 0
            ORDER BY pi.invoice_date
        """
        
        invoices_result = db_manager.execute_query(invoices_query)
        invoices = [dict(invoice) for invoice in invoices_result] if invoices_result else []
        
        if not invoices:
            print("❌ لا توجد فواتير مشتريات مستحقة للدفع.")
            return
        
        print(f"💰 تم العثور على {len(invoices)} فاتورة مستحقة للدفع")
        
        payments_created = 0
        
        # إنشاء مدفوعات للفواتير
        for invoice in invoices:
            # احتمال إنشاء دفعة لهذه الفاتورة (70%)
            if random.random() < 0.7:
                # تحديد نوع الدفعة
                payment_types = [
                    ('full', 'دفع كامل'),
                    ('partial', 'دفع جزئي'),
                    ('multiple', 'دفعات متعددة')
                ]
                
                payment_type, description = random.choice(payment_types)
                
                if payment_type == 'full':
                    # دفع كامل
                    amount = invoice['remaining_amount']
                    payment_date = datetime.now() - timedelta(days=random.randint(1, 30))
                    
                    payment_id = create_payment(
                        db_manager, invoice, amount, payment_date, 
                        f"دفع كامل لفاتورة {invoice['invoice_number']}"
                    )
                    
                    if payment_id:
                        payments_created += 1
                        
                elif payment_type == 'partial':
                    # دفع جزئي
                    amount = invoice['remaining_amount'] * random.uniform(0.3, 0.8)
                    payment_date = datetime.now() - timedelta(days=random.randint(1, 20))
                    
                    payment_id = create_payment(
                        db_manager, invoice, amount, payment_date,
                        f"دفع جزئي لفاتورة {invoice['invoice_number']}"
                    )
                    
                    if payment_id:
                        payments_created += 1
                        
                elif payment_type == 'multiple':
                    # دفعات متعددة
                    remaining = invoice['remaining_amount']
                    num_payments = random.randint(2, 4)
                    
                    for i in range(num_payments):
                        if remaining <= 0:
                            break
                            
                        if i == num_payments - 1:
                            # آخر دفعة - المبلغ المتبقي
                            amount = remaining
                        else:
                            # دفعة جزئية
                            amount = remaining * random.uniform(0.2, 0.5)
                            amount = min(amount, remaining)
                        
                        payment_date = datetime.now() - timedelta(days=random.randint(1, 45))
                        
                        payment_id = create_payment(
                            db_manager, invoice, amount, payment_date,
                            f"دفعة {i+1} لفاتورة {invoice['invoice_number']}"
                        )
                        
                        if payment_id:
                            payments_created += 1
                            remaining -= amount
                
                if payments_created % 10 == 0:
                    print(f"✅ تم إنشاء {payments_created} دفعة...")
        
        print(f"\n🎉 تم إنشاء {payments_created} دفعة تجريبية للموردين بنجاح!")
        
        # تحديث حالات الدفع للفواتير
        update_payment_status(db_manager)
        
        # عرض إحصائيات
        display_payment_statistics(db_manager)
        
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء المدفوعات: {str(e)}")

def create_payment(db_manager, invoice, amount, payment_date, notes):
    """إنشاء دفعة واحدة"""
    try:
        # طرق الدفع المختلفة
        payment_methods = ['نقدي', 'شيك', 'تحويل بنكي', 'بطاقة ائتمان']
        payment_method = random.choice(payment_methods)
        
        # رقم مرجع للدفعة
        reference_number = f"PAY-{random.randint(1000, 9999)}"
        
        # إدراج الدفعة
        payment_query = """
            INSERT INTO payments (
                invoice_type, invoice_id, payment_date, amount, payment_method,
                reference_number, notes, user_id, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        payment_params = [
            'purchase',
            invoice['id'],
            payment_date.strftime('%Y-%m-%d'),
            amount,
            payment_method,
            reference_number,
            notes,
            1,  # المدير
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ]
        
        db_manager.execute_query(payment_query, payment_params)
        
        # تحديث فاتورة الشراء
        new_paid_amount = invoice['paid_amount'] + amount
        new_remaining_amount = invoice['total_amount'] - new_paid_amount
        
        update_invoice_query = """
            UPDATE purchase_invoices 
            SET paid_amount = ?, remaining_amount = ?
            WHERE id = ?
        """
        
        db_manager.execute_query(update_invoice_query, [
            new_paid_amount, new_remaining_amount, invoice['id']
        ])
        
        # تحديث البيانات المحلية
        invoice['paid_amount'] = new_paid_amount
        invoice['remaining_amount'] = new_remaining_amount
        
        # تسجيل العملية
        log_user_activity(
            db_manager,
            1,  # المدير
            "إنشاء دفعة تجريبية للمورد",
            f"المورد: {invoice['supplier_name']}, المبلغ: {amount:.2f}, الطريقة: {payment_method}",
            "payments",
            invoice['id']
        )
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الدفعة: {str(e)}")
        return False

def update_payment_status(db_manager):
    """تحديث حالات الدفع للفواتير"""
    try:
        print("\n🔄 تحديث حالات الدفع...")
        
        # تحديث حالة الدفع لجميع الفواتير
        update_query = """
            UPDATE purchase_invoices 
            SET payment_status = CASE 
                WHEN remaining_amount = 0 THEN 'paid'
                WHEN paid_amount > 0 AND remaining_amount > 0 THEN 'partial'
                ELSE 'pending'
            END
        """
        
        db_manager.execute_query(update_query)
        print("✅ تم تحديث حالات الدفع")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث حالات الدفع: {str(e)}")

def display_payment_statistics(db_manager):
    """عرض إحصائيات المدفوعات"""
    try:
        print("\n📊 إحصائيات المدفوعات:")
        print("=" * 50)
        
        # إحصائيات عامة للمدفوعات
        payments_query = """
            SELECT 
                COUNT(*) as total_payments,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount
            FROM payments 
            WHERE invoice_type = 'purchase'
        """
        
        result = db_manager.execute_query(payments_query)
        if result:
            data = result[0]
            print(f"💰 إجمالي المدفوعات: {data['total_payments']}")
            print(f"💵 إجمالي المبلغ: {data['total_amount']:,.2f}")
            print(f"📊 متوسط الدفعة: {data['avg_amount']:,.2f}")
        
        # إحصائيات حالات الدفع
        status_query = """
            SELECT 
                payment_status,
                COUNT(*) as count,
                SUM(total_amount) as total_amount,
                SUM(remaining_amount) as remaining_amount
            FROM purchase_invoices
            GROUP BY payment_status
        """
        
        result = db_manager.execute_query(status_query)
        if result:
            print(f"\n📋 حالات الدفع:")
            print("-" * 30)
            status_names = {
                'pending': 'معلق',
                'partial': 'مدفوع جزئياً',
                'paid': 'مدفوع',
                'overdue': 'متأخر'
            }
            
            for status in result:
                status_name = status_names.get(status['payment_status'], status['payment_status'])
                print(f"• {status_name}: {status['count']} فاتورة - متبقي: {status['remaining_amount']:,.2f}")
        
        # أكثر الموردين دفعاً
        suppliers_query = """
            SELECT s.name, 
                   COUNT(p.id) as payment_count,
                   SUM(p.amount) as total_paid
            FROM suppliers s
            JOIN purchase_invoices pi ON s.id = pi.supplier_id
            JOIN payments p ON pi.id = p.invoice_id AND p.invoice_type = 'purchase'
            GROUP BY s.id, s.name
            ORDER BY SUM(p.amount) DESC
            LIMIT 5
        """
        
        result = db_manager.execute_query(suppliers_query)
        if result:
            print(f"\n🏆 أكثر 5 موردين دفعاً:")
            print("-" * 30)
            for supplier in result:
                print(f"• {supplier['name']}: {supplier['payment_count']} دفعة - {supplier['total_paid']:,.2f}")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة الموردين → كشف حساب مورد")
        print("2. اختر مورد من القائمة")
        print("3. جرب الفترات المختلفة (هذا الشهر، الشهر الماضي، هذا العام)")
        print("4. لاحظ الرصيد الافتتاحي والختامي")
        print("5. تحقق من تفاصيل المعاملات (فواتير ومدفوعات)")
        print("6. راجع ملخص الحساب في أعلى الشاشة")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    print("🚀 بدء إنشاء مدفوعات الموردين التجريبية...")
    create_supplier_payments()
    print("\n✅ جاهز لاختبار كشف حساب المورد!")
