# -*- coding: utf-8 -*-
"""
إنشاء مستخدمين تجريبيين لاختبار نظام الصلاحيات
"""

import os
import sys
import hashlib

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager
from utils.helpers import log_user_activity

def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_test_users():
    """إنشاء مستخدمين تجريبيين"""
    db_manager = DatabaseManager()
    
    # قائمة المستخدمين التجريبيين
    test_users = [
        {
            'name': 'أحمد البائع',
            'username': 'salesperson',
            'password': 'sales123',
            'role': 'salesperson',
            'email': '<EMAIL>',
            'phone': '**********'
        },
        {
            'name': 'فاطمة المحاسبة',
            'username': 'accountant',
            'password': 'account123',
            'role': 'accountant',
            'email': '<EMAIL>',
            'phone': '**********'
        },
        {
            'name': 'محمد المخزن',
            'username': 'warehouse',
            'password': 'warehouse123',
            'role': 'warehouse',
            'email': '<EMAIL>',
            'phone': '**********'
        }
    ]
    
    try:
        for user_data in test_users:
            # التحقق من وجود المستخدم
            check_query = "SELECT id FROM users WHERE username = ?"
            existing_user = db_manager.execute_query(check_query, [user_data['username']])
            
            if existing_user:
                print(f"المستخدم {user_data['username']} موجود بالفعل")
                continue
            
            # إنشاء المستخدم الجديد
            hashed_password = hash_password(user_data['password'])
            
            insert_query = """
                INSERT INTO users (name, username, password_hash, role, email, phone, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, ?, 1, datetime('now'))
            """
            
            params = [
                user_data['name'],
                user_data['username'],
                hashed_password,
                user_data['role'],
                user_data['email'],
                user_data['phone']
            ]
            
            db_manager.execute_query(insert_query, params)

            # الحصول على معرف المستخدم الجديد
            user_id_query = "SELECT id FROM users WHERE username = ?"
            user_result = db_manager.execute_query(user_id_query, [user_data['username']])
            user_id = user_result[0]['id'] if user_result else None
            
            # تسجيل العملية
            details = f"إنشاء مستخدم تجريبي: {user_data['name']} ({user_data['username']}) - الدور: {user_data['role']}"
            log_user_activity(
                db_manager,
                1,  # المدير
                "إضافة مستخدم جديد",
                details,
                "users",
                user_id
            )
            
            print(f"✅ تم إنشاء المستخدم: {user_data['name']} ({user_data['username']})")
            
        print("\n🎉 تم إنشاء جميع المستخدمين التجريبيين بنجاح!")
        print("\n📋 بيانات تسجيل الدخول:")
        print("=" * 50)
        
        for user_data in test_users:
            print(f"👤 {user_data['name']}")
            print(f"   اسم المستخدم: {user_data['username']}")
            print(f"   كلمة المرور: {user_data['password']}")
            print(f"   الدور: {user_data['role']}")
            print("-" * 30)
            
    except Exception as e:
        print(f"❌ حدث خطأ في إنشاء المستخدمين: {str(e)}")

def display_permissions_info():
    """عرض معلومات الصلاحيات"""
    print("\n🔐 معلومات الصلاحيات:")
    print("=" * 50)
    
    permissions_info = {
        'admin': {
            'name': 'المدير',
            'permissions': 'جميع الصلاحيات'
        },
        'accountant': {
            'name': 'المحاسب',
            'permissions': 'المبيعات، المشتريات، العملاء، الموردين، التقارير'
        },
        'salesperson': {
            'name': 'البائع',
            'permissions': 'إنشاء فواتير المبيعات، إدارة العملاء'
        },
        'warehouse': {
            'name': 'مراقب المخزون',
            'permissions': 'إدارة المنتجات، المخزون، المشتريات'
        }
    }
    
    for role, info in permissions_info.items():
        print(f"🎯 {info['name']} ({role}):")
        print(f"   الصلاحيات: {info['permissions']}")
        print("-" * 30)

if __name__ == "__main__":
    print("🚀 بدء إنشاء المستخدمين التجريبيين...")
    create_test_users()
    display_permissions_info()
    
    print("\n💡 نصائح للاختبار:")
    print("1. سجل الدخول بكل مستخدم لاختبار الصلاحيات")
    print("2. لاحظ الأزرار والقوائم المتاحة لكل دور")
    print("3. جرب الوصول للوظائف غير المسموحة")
    print("4. راجع سجل النشاط من حساب المدير")
    print("\n✅ جاهز للاختبار!")
