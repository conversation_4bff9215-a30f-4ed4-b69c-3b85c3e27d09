# هيكل قاعدة البيانات - برنامج محاسبة المبيعات والمخازن

## نظرة عامة
يستخدم البرنامج قاعدة بيانات SQLite لتخزين جميع البيانات. الملف الرئيسي لقاعدة البيانات هو `sales_inventory.db` ويقع في مجلد `database`.

## الجداول الرئيسية

### 1. جدول المستخدمين (users)
يحتوي على بيانات المستخدمين وصلاحياتهم.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد (مفتاح أساسي) |
| username | TEXT | اسم المستخدم (فريد) |
| password_hash | TEXT | كلمة المرور المشفرة |
| name | TEXT | الاسم الكامل |
| role | TEXT | الدور (admin, accountant, salesperson, warehouse) |
| email | TEXT | البريد الإلكتروني |
| phone | TEXT | رقم الهاتف |
| is_active | INTEGER | حالة النشاط (1=نشط, 0=غير نشط) |
| created_at | TEXT | تاريخ الإنشاء |
| updated_at | TEXT | تاريخ آخر تحديث |

### 2. جدول فئات المنتجات (categories)
يحتوي على تصنيفات المنتجات.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| name | TEXT | اسم الفئة |
| description | TEXT | وصف الفئة |
| created_at | TEXT | تاريخ الإنشاء |

### 3. جدول المنتجات (products)
يحتوي على بيانات المنتجات والمخزون.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| name | TEXT | اسم المنتج |
| barcode | TEXT | الباركود (فريد) |
| category_id | INTEGER | معرف الفئة |
| description | TEXT | وصف المنتج |
| unit | TEXT | وحدة القياس |
| cost_price | REAL | سعر التكلفة |
| selling_price | REAL | سعر البيع |
| stock_quantity | INTEGER | الكمية المتوفرة |
| min_stock_level | INTEGER | الحد الأدنى للمخزون |
| image_path | TEXT | مسار صورة المنتج |
| is_active | INTEGER | حالة النشاط |
| created_at | TEXT | تاريخ الإنشاء |
| updated_at | TEXT | تاريخ آخر تحديث |

### 4. جدول العملاء (customers)
يحتوي على بيانات العملاء.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| name | TEXT | اسم العميل |
| phone | TEXT | رقم الهاتف |
| email | TEXT | البريد الإلكتروني |
| address | TEXT | العنوان |
| tax_number | TEXT | الرقم الضريبي |
| credit_limit | REAL | حد الائتمان |
| current_balance | REAL | الرصيد الحالي |
| notes | TEXT | ملاحظات |
| is_active | INTEGER | حالة النشاط |
| created_at | TEXT | تاريخ الإنشاء |
| updated_at | TEXT | تاريخ آخر تحديث |

### 5. جدول الموردين (suppliers)
يحتوي على بيانات الموردين.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| name | TEXT | اسم المورد |
| phone | TEXT | رقم الهاتف |
| email | TEXT | البريد الإلكتروني |
| address | TEXT | العنوان |
| tax_number | TEXT | الرقم الضريبي |
| current_balance | REAL | الرصيد الحالي |
| notes | TEXT | ملاحظات |
| is_active | INTEGER | حالة النشاط |
| created_at | TEXT | تاريخ الإنشاء |
| updated_at | TEXT | تاريخ آخر تحديث |

### 6. جدول فواتير المبيعات (sales_invoices)
يحتوي على رؤوس فواتير المبيعات.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| invoice_number | TEXT | رقم الفاتورة (فريد) |
| customer_id | INTEGER | معرف العميل |
| user_id | INTEGER | معرف المستخدم |
| invoice_date | TEXT | تاريخ الفاتورة |
| due_date | TEXT | تاريخ الاستحقاق |
| subtotal | REAL | المجموع الفرعي |
| discount_amount | REAL | مبلغ الخصم |
| tax_amount | REAL | مبلغ الضريبة |
| total_amount | REAL | المجموع الكلي |
| paid_amount | REAL | المبلغ المدفوع |
| remaining_amount | REAL | المبلغ المتبقي |
| payment_status | TEXT | حالة الدفع |
| notes | TEXT | ملاحظات |
| created_at | TEXT | تاريخ الإنشاء |
| updated_at | TEXT | تاريخ آخر تحديث |

### 7. جدول تفاصيل فواتير المبيعات (sales_invoice_items)
يحتوي على تفاصيل أصناف فواتير المبيعات.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| invoice_id | INTEGER | معرف الفاتورة |
| product_id | INTEGER | معرف المنتج |
| quantity | REAL | الكمية |
| unit_price | REAL | سعر الوحدة |
| discount_amount | REAL | مبلغ الخصم |
| total_amount | REAL | المجموع |

### 8. جدول فواتير المشتريات (purchase_invoices)
يحتوي على رؤوس فواتير المشتريات.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| invoice_number | TEXT | رقم الفاتورة (فريد) |
| supplier_id | INTEGER | معرف المورد |
| user_id | INTEGER | معرف المستخدم |
| invoice_date | TEXT | تاريخ الفاتورة |
| due_date | TEXT | تاريخ الاستحقاق |
| subtotal | REAL | المجموع الفرعي |
| discount_amount | REAL | مبلغ الخصم |
| tax_amount | REAL | مبلغ الضريبة |
| total_amount | REAL | المجموع الكلي |
| paid_amount | REAL | المبلغ المدفوع |
| remaining_amount | REAL | المبلغ المتبقي |
| payment_status | TEXT | حالة الدفع |
| notes | TEXT | ملاحظات |
| created_at | TEXT | تاريخ الإنشاء |
| updated_at | TEXT | تاريخ آخر تحديث |

### 9. جدول تفاصيل فواتير المشتريات (purchase_invoice_items)
يحتوي على تفاصيل أصناف فواتير المشتريات.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| invoice_id | INTEGER | معرف الفاتورة |
| product_id | INTEGER | معرف المنتج |
| quantity | REAL | الكمية |
| unit_price | REAL | سعر الوحدة |
| discount_amount | REAL | مبلغ الخصم |
| total_amount | REAL | المجموع |

### 10. جدول المدفوعات (payments)
يحتوي على سجل المدفوعات.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| invoice_type | TEXT | نوع الفاتورة (sales/purchase) |
| invoice_id | INTEGER | معرف الفاتورة |
| payment_date | TEXT | تاريخ الدفع |
| amount | REAL | المبلغ |
| payment_method | TEXT | طريقة الدفع |
| reference_number | TEXT | رقم المرجع |
| notes | TEXT | ملاحظات |
| user_id | INTEGER | معرف المستخدم |
| created_at | TEXT | تاريخ الإنشاء |

### 11. جدول حركات المخزون (inventory_movements)
يحتوي على سجل حركات المخزون.

| العمود | النوع | الوصف |
|--------|-------|--------|
| id | INTEGER | المعرف الفريد |
| product_id | INTEGER | معرف المنتج |
| movement_type | TEXT | نوع الحركة (in/out/adjustment) |
| quantity | REAL | الكمية |
| reference_type | TEXT | نوع المرجع |
| reference_id | INTEGER | معرف المرجع |
| notes | TEXT | ملاحظات |
| user_id | INTEGER | معرف المستخدم |
| movement_date | TEXT | تاريخ الحركة |
| created_at | TEXT | تاريخ الإنشاء |

## العلاقات بين الجداول

### العلاقات الأساسية:
- `products.category_id` → `categories.id`
- `sales_invoices.customer_id` → `customers.id`
- `sales_invoices.user_id` → `users.id`
- `sales_invoice_items.invoice_id` → `sales_invoices.id`
- `sales_invoice_items.product_id` → `products.id`
- `purchase_invoices.supplier_id` → `suppliers.id`
- `purchase_invoices.user_id` → `users.id`
- `purchase_invoice_items.invoice_id` → `purchase_invoices.id`
- `purchase_invoice_items.product_id` → `products.id`
- `payments.user_id` → `users.id`
- `inventory_movements.product_id` → `products.id`
- `inventory_movements.user_id` → `users.id`

## الفهارس والقيود

### الفهارس الموصى بها:
- فهرس على `products.barcode`
- فهرس على `sales_invoices.invoice_number`
- فهرس على `purchase_invoices.invoice_number`
- فهرس على `sales_invoices.invoice_date`
- فهرس على `purchase_invoices.invoice_date`

### القيود:
- `users.username` فريد
- `products.barcode` فريد
- `sales_invoices.invoice_number` فريد
- `purchase_invoices.invoice_number` فريد

---
**ملاحظة:** يتم إنشاء قاعدة البيانات تلقائياً عند تشغيل البرنامج لأول مرة.
