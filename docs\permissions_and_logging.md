# 🔐 نظام الصلاحيات وسجل العمليات

## نظام الصلاحيات المتقدم

تم تطوير نظام صلاحيات شامل ومتقدم يضمن الأمان والتحكم الدقيق في الوصول لوظائف النظام.

### 🎯 أنواع المستخدمين والصلاحيات

#### 🔑 المدير (Admin)
- **الصلاحيات:** جميع الصلاحيات (`all`)
- **الوصول إلى:**
  - إدارة المستخدمين والصلاحيات
  - جميع وظائف المبيعات والمشتريات
  - إدارة المنتجات والمخزون
  - إدارة العملاء والموردين
  - جميع التقارير
  - إعدادات النظام
  - سجل نشاط المستخدمين
  - النسخ الاحتياطي والاستعادة

#### 💼 المحاسب (Accountant)
- **الصلاحيات المحددة:**
  - `sales_management` - إدارة المبيعات
  - `sales_create` - إنشاء فواتير مبيعات
  - `sales_view` - عرض فواتير المبيعات
  - `purchases_management` - إدارة المشتريات
  - `purchases_create` - إنشاء فواتير مشتريات
  - `customers_management` - إدارة العملاء
  - `suppliers_management` - إدارة الموردين
  - `reports_view` - عرض التقارير

- **لا يمكنه الوصول إلى:**
  - إدارة المستخدمين
  - إعدادات النظام
  - سجل النشاط

#### 🛒 البائع (Salesperson)
- **الصلاحيات المحددة:**
  - `sales_create` - إنشاء فواتير مبيعات
  - `sales_view` - عرض فواتير المبيعات
  - `customers_management` - إدارة العملاء

- **لا يمكنه الوصول إلى:**
  - المشتريات والموردين
  - إدارة المنتجات
  - إدارة المستخدمين
  - التقارير المالية الشاملة

#### 📦 مراقب المخزون (Warehouse)
- **الصلاحيات المحددة:**
  - `products_management` - إدارة المنتجات
  - `inventory_management` - إدارة المخزون
  - `purchases_create` - إنشاء فواتير مشتريات
  - `purchases_management` - إدارة المشتريات

- **لا يمكنه الوصول إلى:**
  - المبيعات والعملاء
  - التقارير المالية
  - إدارة المستخدمين

### 🛡️ آلية عمل نظام الصلاحيات

#### 1. التحقق من الصلاحيات
```python
def check_user_permission(user_role, required_permission):
    """التحقق من صلاحية المستخدم"""
    user_permissions = USER_ROLES.get(user_role, {}).get('permissions', [])
    return 'all' in user_permissions or required_permission in user_permissions
```

#### 2. حماية الوظائف
```python
# مثال على حماية دالة إضافة مستخدم
def add_user_dialog(self):
    if not check_user_permission(self.current_user['role'], 'users_management'):
        show_permission_error('إدارة المستخدمين')
        return
    # باقي الكود...
```

#### 3. إخفاء الأزرار غير المسموحة
```python
# إظهار الأزرار حسب الصلاحيات
if check_user_permission(self.current_user['role'], 'sales_create'):
    sales_btn = tk.Button(...)  # إظهار زر المبيعات
```

### 🔒 الصلاحيات المتاحة

| الصلاحية | الوصف | المدير | المحاسب | البائع | المخزون |
|----------|-------|--------|---------|--------|---------|
| `users_management` | إدارة المستخدمين | ✅ | ❌ | ❌ | ❌ |
| `sales_create` | إنشاء فواتير مبيعات | ✅ | ✅ | ✅ | ❌ |
| `sales_view` | عرض فواتير المبيعات | ✅ | ✅ | ✅ | ❌ |
| `sales_management` | إدارة المبيعات | ✅ | ✅ | ❌ | ❌ |
| `purchases_create` | إنشاء فواتير مشتريات | ✅ | ✅ | ❌ | ✅ |
| `purchases_management` | إدارة المشتريات | ✅ | ✅ | ❌ | ✅ |
| `products_management` | إدارة المنتجات | ✅ | ❌ | ❌ | ✅ |
| `inventory_management` | إدارة المخزون | ✅ | ❌ | ❌ | ✅ |
| `customers_management` | إدارة العملاء | ✅ | ✅ | ✅ | ❌ |
| `suppliers_management` | إدارة الموردين | ✅ | ✅ | ❌ | ❌ |
| `reports_view` | عرض التقارير | ✅ | ✅ | ❌ | ❌ |
| `system_settings` | إعدادات النظام | ✅ | ❌ | ❌ | ❌ |

## 📊 نظام سجل العمليات

### 🎯 الهدف من سجل العمليات
- **المراقبة:** تتبع جميع العمليات المهمة في النظام
- **الأمان:** كشف أي أنشطة مشبوهة أو غير مصرح بها
- **المساءلة:** معرفة من قام بأي عملية ومتى
- **التدقيق:** مراجعة العمليات للتأكد من صحتها

### 🗃️ هيكل جدول سجل العمليات

```sql
CREATE TABLE user_activity_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,           -- معرف المستخدم
    action TEXT NOT NULL,               -- نوع العملية
    details TEXT,                       -- تفاصيل العملية
    table_name TEXT,                    -- اسم الجدول المتأثر
    record_id INTEGER,                  -- معرف السجل المتأثر
    timestamp TEXT NOT NULL,            -- وقت العملية
    ip_address TEXT,                    -- عنوان IP (للمستقبل)
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### 📝 العمليات المسجلة

#### عمليات المستخدمين
- إضافة مستخدم جديد
- تعديل بيانات مستخدم
- حذف مستخدم
- تغيير صلاحيات مستخدم

#### عمليات المبيعات
- إنشاء فاتورة مبيعات
- تعديل فاتورة مبيعات
- حذف فاتورة مبيعات
- إضافة دفعة للعميل

#### عمليات المشتريات
- إنشاء فاتورة شراء
- تعديل فاتورة شراء
- حذف فاتورة شراء
- إضافة دفعة للمورد

#### عمليات المنتجات
- إضافة منتج جديد
- تعديل بيانات منتج
- حذف منتج
- تعديل المخزون

#### عمليات العملاء والموردين
- إضافة عميل/مورد جديد
- تعديل بيانات عميل/مورد
- حذف عميل/مورد

### 🔧 كيفية استخدام سجل العمليات

#### 1. تسجيل عملية جديدة
```python
from utils.helpers import log_user_activity

# تسجيل إنشاء فاتورة مبيعات
log_user_activity(
    db_manager=self.db_manager,
    user_id=self.current_user['id'],
    action="إنشاء فاتورة مبيعات",
    details=f"فاتورة رقم: {invoice_number}, العميل: {customer_name}, المبلغ: {total_amount}",
    table_name="sales_invoices",
    record_id=invoice_id
)
```

#### 2. عرض سجل النشاط
- من قائمة "الإدارة" → "سجل العمليات" (للمدير فقط)
- فلترة حسب التاريخ، المستخدم، نوع العملية
- إحصائيات سريعة عن النشاط

### 📈 الإحصائيات المتاحة

#### إحصائيات يومية
- إجمالي العمليات اليوم
- عدد المستخدمين النشطين
- آخر نشاط في النظام

#### إحصائيات الفترة
- إجمالي العمليات في فترة محددة
- توزيع العمليات حسب النوع
- نشاط كل مستخدم

### 🛡️ الأمان والخصوصية

#### حماية البيانات الحساسة
- لا يتم تسجيل كلمات المرور
- تشفير البيانات الحساسة
- حذف السجلات القديمة تلقائياً (بعد سنة)

#### التحكم في الوصول
- فقط المدير يمكنه عرض سجل النشاط
- لا يمكن تعديل أو حذف السجلات
- نسخ احتياطية منتظمة للسجل

### 🔍 البحث والفلترة

#### فلاتر متاحة
- **التاريخ:** من تاريخ إلى تاريخ
- **المستخدم:** اختيار مستخدم محدد
- **نوع العملية:** فلترة حسب نوع العملية
- **الجدول:** فلترة حسب الجدول المتأثر

#### فترات سريعة
- اليوم
- أمس
- هذا الأسبوع
- الأسبوع الماضي
- هذا الشهر

### 📊 تقارير سجل النشاط

#### تقرير النشاط اليومي
- عدد العمليات لكل مستخدم
- أكثر العمليات تكراراً
- أوقات الذروة

#### تقرير الأمان
- محاولات الوصول غير المصرح بها
- العمليات المشبوهة
- تغييرات الصلاحيات

### 🚀 الميزات المستقبلية

#### تحسينات مخططة
- تسجيل عنوان IP للمستخدم
- تنبيهات فورية للعمليات المهمة
- تصدير سجل النشاط
- تحليل سلوك المستخدمين
- تكامل مع أنظمة الأمان الخارجية

#### تقارير متقدمة
- تحليل الأداء حسب المستخدم
- إحصائيات الاستخدام
- تقارير الامتثال والمراجعة

---

## 🔧 إعداد وتكوين النظام

### تخصيص الصلاحيات
يمكن تعديل الصلاحيات من ملف `config/settings.py`:

```python
USER_ROLES = {
    'custom_role': {
        'name': 'دور مخصص',
        'permissions': ['sales_create', 'customers_management']
    }
}
```

### إعدادات سجل العمليات
- **الاحتفاظ بالسجلات:** 365 يوم (افتراضي)
- **الحد الأقصى للسجلات:** 100,000 سجل
- **النسخ الاحتياطي:** تلقائي مع النسخة الاحتياطية للنظام

---
**© 2024 - نظام الصلاحيات وسجل العمليات | تم التطوير باستخدام Augment Agent**
