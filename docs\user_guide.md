# دليل المستخدم - برنامج محاسبة المبيعات والمخازن

## مقدمة
مرحباً بك في برنامج محاسبة المبيعات والمخازن، البرنامج الشامل لإدارة عمليات البيع والشراء والمخزون.

## متطلبات التشغيل
- نظام التشغيل: Windows 10 أو أحدث
- Python 3.8 أو أحدث
- ذاكرة: 4 جيجابايت RAM على الأقل
- مساحة القرص الصلب: 500 ميجابايت على الأقل

## التثبيت والتشغيل

### الطريقة الأولى: التشغيل المباشر
1. تأكد من تثبيت Python على جهازك
2. قم بتشغيل ملف `install_requirements.bat` لتثبيت المتطلبات
3. قم بتشغيل ملف `run.bat` لبدء البرنامج

### الطريقة الثانية: التشغيل اليدوي
1. افتح موجه الأوامر (Command Prompt)
2. انتقل إلى مجلد البرنامج
3. قم بتشغيل الأمر: `pip install -r requirements.txt`
4. قم بتشغيل الأمر: `python main.py`

## تسجيل الدخول الأول
عند تشغيل البرنامج لأول مرة، استخدم البيانات التالية:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** يُنصح بتغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## الواجهة الرئيسية

### شريط القوائم
يحتوي على القوائم التالية:
- **ملف:** النسخ الاحتياطي، الإعدادات، تسجيل الخروج
- **المبيعات:** إدارة فواتير المبيعات والمرتجعات
- **المشتريات:** إدارة فواتير المشتريات والمرتجعات
- **المخزون:** إدارة المنتجات والفئات وحركات المخزون
- **العملاء:** إدارة بيانات العملاء وكشوف الحسابات
- **الموردين:** إدارة بيانات الموردين وكشوف الحسابات
- **التقارير:** جميع التقارير المالية والمخزنية
- **الإدارة:** إدارة المستخدمين والصلاحيات (للمدير فقط)
- **مساعدة:** دليل المستخدم ومعلومات البرنامج

### الأزرار السريعة
توفر وصولاً سريعاً للعمليات الأكثر استخداماً:
- فاتورة مبيعات جديدة
- فاتورة شراء جديدة
- إدارة المنتجات
- إدارة العملاء
- إدارة الموردين
- التقارير

### شريط الحالة
يعرض معلومات المستخدم الحالي ومعلومات الشركة.

## أنواع المستخدمين والصلاحيات

### المدير (Admin)
- صلاحية كاملة على جميع أجزاء البرنامج
- إدارة المستخدمين والصلاحيات
- الوصول إلى جميع التقارير
- إعدادات النظام والنسخ الاحتياطي

### المحاسب (Accountant)
- إدارة المبيعات والمشتريات
- إدارة العملاء والموردين
- الوصول إلى التقارير المالية
- لا يمكنه إدارة المستخدمين

### البائع (Salesperson)
- إنشاء فواتير المبيعات فقط
- إدارة العملاء
- عرض تقارير المبيعات الخاصة به

### مراقب المخزون (Warehouse)
- إدارة المنتجات والمخزون
- إدارة المشتريات
- تقارير المخزون وحركاته

## النسخ الاحتياطي
- يُنصح بعمل نسخة احتياطية يومياً
- النسخ الاحتياطية تُحفظ في مجلد `backup`
- يمكن استعادة النسخة الاحتياطية من قائمة "ملف"

## الدعم الفني
في حالة مواجهة أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات بشكل صحيح
2. تحقق من وجود ملف قاعدة البيانات في مجلد `database`
3. تأكد من وجود صلاحيات الكتابة في مجلد البرنامج

## نصائح مهمة
- قم بعمل نسخة احتياطية بانتظام
- لا تحذف ملفات قاعدة البيانات يدوياً
- استخدم أسماء مستخدمين وكلمات مرور قوية
- راجع التقارير بانتظام للتأكد من دقة البيانات

---
**تم تطوير هذا البرنامج باستخدام Augment Agent**
