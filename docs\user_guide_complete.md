# 📖 دليل المستخدم الشامل - برنامج محاسبة المبيعات والمخازن

## 🚀 البدء السريع

### تسجيل الدخول
1. شغل البرنامج بالنقر المزدوج على `run.bat` أو تشغيل `python main.py`
2. أدخل بيانات تسجيل الدخول:
   - **اسم المستخدم:** admin
   - **كلمة المرور:** admin123
3. انقر على "تسجيل الدخول"

⚠️ **مهم:** غير كلمة المرور الافتراضية فور تسجيل الدخول الأول!

### الواجهة الرئيسية
بعد تسجيل الدخول ستظهر الواجهة الرئيسية التي تحتوي على:
- **شريط القوائم العلوي:** للوصول السريع للوظائف
- **الأزرار الرئيسية:** للوظائف الأكثر استخداماً
- **شريط الحالة:** يعرض معلومات المستخدم والوقت

## 👥 إدارة المستخدمين

### إضافة مستخدم جديد
1. من القائمة الرئيسية اختر "إدارة المستخدمين"
2. انقر على "مستخدم جديد"
3. املأ البيانات المطلوبة:
   - الاسم الكامل
   - اسم المستخدم (يجب أن يكون فريد)
   - كلمة المرور (8 أحرف على الأقل)
   - نوع المستخدم
4. انقر على "حفظ"

### أنواع المستخدمين والصلاحيات

#### 🔑 المدير (Admin)
- ✅ إدارة المستخدمين والصلاحيات
- ✅ جميع وظائف المبيعات والمشتريات
- ✅ إدارة المنتجات والمخزون
- ✅ إدارة العملاء والموردين
- ✅ جميع التقارير
- ✅ إعدادات النظام والنسخ الاحتياطي

#### 💼 المحاسب (Accountant)
- ✅ فواتير المبيعات والمشتريات
- ✅ إدارة العملاء والموردين
- ✅ التقارير المالية
- ❌ إدارة المستخدمين
- ❌ إعدادات النظام

#### 🛒 البائع (Salesperson)
- ✅ فواتير المبيعات فقط
- ✅ إدارة العملاء
- ✅ تقارير المبيعات الخاصة به
- ❌ المشتريات والموردين
- ❌ إدارة المنتجات

#### 📦 مراقب المخزون (Warehouse)
- ✅ إدارة المنتجات والمخزون
- ✅ فواتير المشتريات
- ✅ تقارير المخزون
- ❌ المبيعات والعملاء
- ❌ التقارير المالية

## 📦 إدارة المنتجات

### إضافة منتج جديد
1. اختر "إدارة المنتجات" من القائمة الرئيسية
2. انقر على "منتج جديد"
3. املأ البيانات الأساسية:
   - **اسم المنتج:** (مطلوب)
   - **الفئة:** اختر من القائمة أو أضف فئة جديدة
   - **الوحدة:** قطعة، كيلو، متر، لتر، إلخ
   - **الباركود:** (اختياري)
4. املأ البيانات المالية:
   - **سعر التكلفة:** سعر الشراء
   - **سعر البيع:** سعر البيع للعملاء
   - **الكمية الحالية:** المخزون الحالي
   - **الحد الأدنى:** للتنبيه عند النقص
5. يمكن إضافة صورة للمنتج (اختياري)
6. انقر على "حفظ"

### إدارة فئات المنتجات
1. من شاشة إدارة المنتجات انقر على "إدارة الفئات"
2. يمكن إضافة/تعديل/حذف الفئات
3. كل فئة لها اسم ووصف
4. الفئات تساعد في تنظيم المنتجات والتقارير

### تحديث المخزون
- **تلقائياً:** يتم تحديث المخزون عند إنشاء فواتير المبيعات والمشتريات
- **يدوياً:** يمكن تعديل الكمية من شاشة المنتجات
- **تتبع الحركات:** جميع التغييرات تُسجل في تقرير حركات المخزون

## 👥 إدارة العملاء

### إضافة عميل جديد
1. اختر "إدارة العملاء" من القائمة
2. انقر على "عميل جديد"
3. املأ البيانات:
   - **الاسم الكامل:** (مطلوب)
   - **رقم الهاتف:** (مطلوب)
   - **البريد الإلكتروني:** (اختياري)
   - **العنوان:** (اختياري)
   - **حد الائتمان:** الحد الأقصى للدين المسموح
4. انقر على "حفظ"

### متابعة حساب العميل
- **سجل الفواتير:** عرض جميع فواتير العميل
- **المبالغ المستحقة:** متابعة الديون
- **تاريخ المدفوعات:** سجل جميع المدفوعات
- **حد الائتمان:** مراقبة عدم تجاوز الحد المسموح

## 🏭 إدارة الموردين

### إضافة مورد جديد
1. اختر "إدارة الموردين" من القائمة
2. انقر على "مورد جديد"
3. املأ البيانات المطلوبة (مشابهة للعملاء)
4. انقر على "حفظ"

### متابعة حساب المورد
- سجل فواتير الشراء
- المبالغ المستحقة للمورد
- تاريخ المدفوعات

## 💰 فواتير المبيعات

### إنشاء فاتورة مبيعات جديدة
1. اختر "فاتورة مبيعات جديدة" من القائمة الرئيسية
2. **معلومات الفاتورة:**
   - رقم الفاتورة (يُنشأ تلقائياً)
   - التاريخ (اليوم افتراضياً)
   - العميل (اختر من القائمة أو "عميل نقدي")

3. **إضافة المنتجات:**
   - اختر المنتج من القائمة المنسدلة
   - أدخل الكمية المطلوبة
   - تأكد من السعر (يمكن تعديله)
   - انقر على "إضافة"
   - كرر العملية لجميع المنتجات

4. **الحسابات:**
   - المجموع الفرعي (يُحسب تلقائياً)
   - نسبة الخصم (اختياري)
   - نسبة الضريبة (افتراضياً من الإعدادات)
   - المجموع الكلي (يُحسب تلقائياً)

5. انقر على "حفظ الفاتورة"

### إدارة فواتير المبيعات
- **البحث:** بالرقم أو اسم العميل
- **الفلترة:** حسب حالة الدفع (معلق، مدفوع جزئياً، مدفوع، متأخر)
- **العرض:** تفاصيل أي فاتورة
- **الطباعة:** طباعة الفواتير
- **الحذف:** حذف الفواتير (بصلاحية)

## 🛒 فواتير المشتريات

### إنشاء فاتورة شراء جديدة
1. اختر "فاتورة شراء جديدة" من القائمة
2. اختر المورد من القائمة
3. أضف المنتجات مع أسعار الشراء
4. احفظ الفاتورة

**ملاحظات مهمة:**
- فواتير الشراء تزيد المخزون تلقائياً
- تحدث أسعار التكلفة للمنتجات
- تُسجل حركة دخول في سجل المخزون

## 📊 نظام التقارير الشامل

### تقارير المبيعات
1. اختر "التقارير" من القائمة الرئيسية
2. حدد الفترة الزمنية:
   - استخدم الفترات السريعة (اليوم، الأسبوع، الشهر)
   - أو حدد تواريخ مخصصة
3. اختر نوع التقرير:

#### تقرير المبيعات التفصيلي
- يعرض كل فاتورة مع تفاصيلها
- رقم الفاتورة، العميل، التاريخ، المنتجات، الكميات، الأسعار

#### تقرير المبيعات الإجمالي
- مجاميع يومية للمبيعات
- عدد الفواتير، المجموع الفرعي، الخصم، الضريبة، المجموع الكلي

#### تقرير العملاء
- مبيعات كل عميل في الفترة المحددة
- المبالغ المستحقة وحدود الائتمان

### تقارير المشتريات
- **تقرير المشتريات التفصيلي:** كل فاتورة شراء بالتفصيل
- **تقرير المشتريات الإجمالي:** مجاميع يومية للمشتريات
- **تقرير الموردين:** مشتريات كل مورد والمبالغ المستحقة

### تقارير المخزون
#### تقرير المخزون الحالي
- جميع المنتجات مع كمياتها الحالية
- أسعار التكلفة والبيع
- قيمة المخزون الإجمالية

#### تقرير حركات المخزون
- جميع حركات الدخول والخروج
- مرجع كل حركة (مبيعات، مشتريات، تعديل)
- التواريخ والمستخدمين

#### تقرير المخزون المنخفض
- المنتجات التي وصلت للحد الأدنى
- الكمية المطلوبة لإعادة التخزين
- قيمة النقص المالية

### التقارير المالية
#### تقرير الأرباح والخسائر
- إجمالي المبيعات والمشتريات
- حساب الأرباح الصافية
- نسبة الربح

#### تقرير المديونيات
- مديونيات العملاء (لنا)
- مديونيات الموردين (علينا)
- أرقام الهواتف للمتابعة

### تصدير التقارير
- **الطباعة:** طباعة مباشرة
- **Excel:** تصدير إلى ملف Excel (قريباً)
- **PDF:** حفظ كملف PDF (قريباً)

## ⚙️ إعدادات النظام

### إعدادات الشركة
- اسم الشركة
- العنوان الكامل
- أرقام الهواتف
- البريد الإلكتروني
- الموقع الإلكتروني

### إعدادات الفواتير
- نسبة الضريبة الافتراضية
- تنسيق أرقام الفواتير
- العملة المستخدمة
- شروط الدفع

### النسخ الاحتياطي والاستعادة
#### إنشاء نسخة احتياطية
1. من قائمة "ملف" اختر "نسخة احتياطية"
2. اختر مكان حفظ النسخة
3. انقر على "إنشاء نسخة احتياطية"
4. احفظ النسخة في مكان آمن

#### استعادة النسخة الاحتياطية
1. من قائمة "ملف" اختر "استعادة نسخة احتياطية"
2. اختر ملف النسخة الاحتياطية (.db)
3. انقر على "استعادة"
4. أعد تشغيل البرنامج

## 🔧 نصائح مهمة للاستخدام الأمثل

### الأمان والحماية
- **غير كلمة المرور الافتراضية فوراً**
- استخدم كلمات مرور قوية (8 أحرف على الأقل)
- لا تشارك بيانات تسجيل الدخول
- أنشئ مستخدمين منفصلين لكل موظف
- راجع صلاحيات المستخدمين بانتظام

### النسخ الاحتياطي
- **اعمل نسخة احتياطية يومياً**
- احفظ النسخ في أماكن متعددة
- اختبر استعادة النسخة شهرياً
- احتفظ بنسخ لمدة 3 أشهر على الأقل

### الأداء والصيانة
- لا تترك البرنامج مفتوحاً لفترات طويلة
- أغلق التقارير الكبيرة بعد الانتهاء
- نظف قاعدة البيانات شهرياً
- راقب مساحة القرص الصلب

### دقة البيانات
- راجع الفواتير قبل الحفظ
- تأكد من أسعار المنتجات بانتظام
- راجع أرصدة العملاء والموردين
- اعمل جرد دوري للمخزون

## 🚨 استكشاف الأخطاء وحلولها

### مشاكل التشغيل
**المشكلة:** البرنامج لا يفتح
- **الحل:** تأكد من تثبيت Python 3.8+
- **الحل:** شغل `test_system.py` للتشقق من النظام
- **الحل:** تأكد من وجود جميع الملفات

**المشكلة:** رسائل خطأ عند الحفظ
- **الحل:** تأكد من صلاحيات الكتابة في مجلد البرنامج
- **الحل:** أغلق البرنامج وأعد تشغيله
- **الحل:** تأكد من عدم فتح قاعدة البيانات في برنامج آخر

### مشاكل البيانات
**المشكلة:** فقدان البيانات
- **الحل:** استعد النسخة الاحتياطية فوراً
- **الحل:** تحقق من مجلد `database`
- **الحل:** لا تحذف أي ملفات يدوياً

**المشكلة:** أرقام خاطئة في التقارير
- **الحل:** تأكد من صحة أسعار المنتجات
- **الحل:** راجع فواتير المبيعات والمشتريات
- **الحل:** تحقق من إعدادات الضريبة والخصم

### مشاكل الأداء
**المشكلة:** البرنامج بطيء
- **الحل:** أغلق التقارير غير المستخدمة
- **الحل:** أعد تشغيل البرنامج
- **الحل:** تحقق من مساحة القرص الصلب

## 📞 الدعم الفني والمساعدة

### التحقق من سلامة النظام
```bash
python test_system.py
```

### ملفات مهمة للمراجعة
- `README_COMPLETE.md` - دليل التثبيت والتشغيل
- `docs/database_structure.md` - هيكل قاعدة البيانات
- `docs/rtl_changes.md` - تحسينات اللغة العربية

### في حالة الطوارئ
1. **لا تحذف أي ملفات**
2. **اعمل نسخة من مجلد البرنامج كاملاً**
3. **استعد آخر نسخة احتياطية**
4. **راجع ملف `config.ini` للإعدادات**

---
**© 2024 - برنامج محاسبة المبيعات والمخازن | تم التطوير باستخدام Augment Agent**
