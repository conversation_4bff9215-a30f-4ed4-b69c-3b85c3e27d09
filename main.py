# -*- coding: utf-8 -*-
"""
🏢 برنامج محاسبة المبيعات والمخازن المتطور
📋 نظام إدارة شامل للمبيعات والمشتريات والمخزون
🚀 الإصدار 2.0 - محسن ومطور بالكامل

المطور: Augment Agent
التاريخ: 2024
الترخيص: مفتوح المصدر
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os
import threading
import time
from datetime import datetime
import json
import logging

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات الأساسية
from config.settings import *
from utils.database_manager import DatabaseManager
from utils.helpers import log_user_activity, check_user_permission
from screens.login_screen import LoginScreen
from screens.main_interface import MainInterface

class SalesInventoryApp:
    """
    🏢 الكلاس الرئيسي لبرنامج محاسبة المبيعات والمخازن المتطور

    يحتوي على جميع الوظائف الأساسية لإدارة النظام:
    - إدارة النوافذ والواجهات
    - إدارة قاعدة البيانات
    - إدارة المستخدمين والصلاحيات
    - النسخ الاحتياطي التلقائي
    - تسجيل الأنشطة والأحداث
    - إدارة الإعدادات والتكوين
    """

    def __init__(self):
        """تهيئة البرنامج الرئيسي"""
        # إعداد نظام التسجيل
        self.setup_logging()

        # متغيرات النظام الأساسية
        self.root = tk.Tk()
        self.db_manager = None
        self.current_user = None
        self.app_version = "3.0"
        self.app_build = "2024.12.21"

        # متغيرات نظام الكاشير المتطور
        self.cashier_mode = False
        self.current_sale = None
        self.cash_drawer_open = False
        self.receipt_printer = None
        self.pos_features = {
            'barcode_scanner': True,
            'receipt_printer': True,
            'cash_drawer': True,
            'customer_display': False,
            'scale_integration': False
        }

        # إعداد النافذة الرئيسية
        self.setup_main_window()

        # تهيئة قاعدة البيانات
        self.initialize_database()

        # تحميل الإعدادات
        self.load_app_settings()

        # إعداد معالجات الأحداث
        self.setup_event_handlers()

        self.log_info("تم تهيئة البرنامج بنجاح")
        
    def setup_logging(self):
        """إعداد نظام التسجيل المتقدم"""
        try:
            # إنشاء مجلد السجلات إذا لم يكن موجوداً
            logs_dir = os.path.join(BASE_DIR, 'logs')
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)

            # إعداد ملف السجل
            log_file = os.path.join(logs_dir, f'app_{datetime.now().strftime("%Y%m%d")}.log')

            # تكوين نظام التسجيل
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(log_file, encoding='utf-8'),
                    logging.StreamHandler()
                ]
            )

            self.logger = logging.getLogger(__name__)

        except Exception as e:
            print(f"خطأ في إعداد نظام التسجيل: {str(e)}")
            self.logger = None

    def log_info(self, message):
        """تسجيل رسالة معلومات"""
        if self.logger:
            self.logger.info(message)
        print(f"ℹ️ {message}")

    def log_error(self, message):
        """تسجيل رسالة خطأ"""
        if self.logger:
            self.logger.error(message)
        print(f"❌ {message}")

    def log_warning(self, message):
        """تسجيل رسالة تحذير"""
        if self.logger:
            self.logger.warning(message)
        print(f"⚠️ {message}")

    def setup_main_window(self):
        """إعداد النافذة الرئيسية المحسنة"""
        # عنوان النافذة مع معلومات الإصدار
        title = f"🏢 برنامج محاسبة المبيعات والمخازن - الإصدار {self.app_version}"
        self.root.title(title)

        # حجم النافذة المحسن
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)

        # توسيط النافذة على الشاشة
        self.center_window()

        # إعداد الأيقونة المحسنة
        self.setup_window_icon()

        # إعداد الخطوط والألوان
        self.setup_fonts_and_colors()

        # إعداد شريط الحالة
        self.setup_status_bar()

        # إعداد معالج إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.log_info("تم إعداد النافذة الرئيسية بنجاح")

    def setup_window_icon(self):
        """إعداد أيقونة النافذة"""
        try:
            icon_path = os.path.join(IMAGES_DIR, 'app_icon.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
                self.log_info("تم تحميل أيقونة البرنامج بنجاح")
            else:
                self.log_warning("لم يتم العثور على أيقونة البرنامج")
        except Exception as e:
            self.log_error(f"خطأ في تحميل أيقونة البرنامج: {str(e)}")

    def setup_fonts_and_colors(self):
        """إعداد الخطوط والألوان"""
        try:
            # إعداد الخط الافتراضي
            self.root.option_add('*Font', FONTS['normal'])

            # إعداد ألوان النظام
            style = ttk.Style()
            style.theme_use('clam')

            # تخصيص الألوان
            style.configure('Title.TLabel', font=FONTS['title'], foreground=COLORS['primary'])
            style.configure('Header.TLabel', font=FONTS['header'], foreground=COLORS['secondary'])

            self.log_info("تم إعداد الخطوط والألوان بنجاح")

        except Exception as e:
            self.log_error(f"خطأ في إعداد الخطوط والألوان: {str(e)}")

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        try:
            self.status_bar = tk.Frame(self.root, relief=tk.SUNKEN, bd=1)
            self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

            # معلومات الحالة
            self.status_label = tk.Label(
                self.status_bar,
                text=f"جاهز - الإصدار {self.app_version} ({self.app_build})",
                font=FONTS['small']
            )
            self.status_label.pack(side=tk.LEFT, padx=5)

            # ساعة النظام
            self.clock_label = tk.Label(self.status_bar, font=FONTS['small'])
            self.clock_label.pack(side=tk.RIGHT, padx=5)

            # تحديث الساعة
            self.update_clock()

            self.log_info("تم إعداد شريط الحالة بنجاح")

        except Exception as e:
            self.log_error(f"خطأ في إعداد شريط الحالة: {str(e)}")

    def update_clock(self):
        """تحديث ساعة النظام"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'clock_label'):
                self.clock_label.config(text=current_time)
            self.root.after(1000, self.update_clock)
        except:
            pass
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        try:
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
            self.log_info("تم توسيط النافذة على الشاشة")
        except Exception as e:
            self.log_error(f"خطأ في توسيط النافذة: {str(e)}")

    def load_app_settings(self):
        """تحميل إعدادات البرنامج"""
        try:
            settings_file = os.path.join(DATABASE_DIR, 'program_settings.json')
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    self.app_settings = json.load(f)
                self.log_info("تم تحميل إعدادات البرنامج بنجاح")
            else:
                self.app_settings = self.get_default_settings()
                self.save_app_settings()
                self.log_info("تم إنشاء إعدادات افتراضية للبرنامج")
        except Exception as e:
            self.log_error(f"خطأ في تحميل إعدادات البرنامج: {str(e)}")
            self.app_settings = self.get_default_settings()

    def get_default_settings(self):
        """الحصول على الإعدادات الافتراضية"""
        return {
            "app_info": {
                "version": self.app_version,
                "build": self.app_build,
                "last_updated": datetime.now().isoformat()
            },
            "ui_settings": {
                "theme": "default",
                "language": "ar",
                "font_size": "normal",
                "auto_save": True
            },
            "security": {
                "session_timeout": 60,
                "auto_lock": False,
                "require_password_change": False
            }
        }

    def save_app_settings(self):
        """حفظ إعدادات البرنامج"""
        try:
            settings_file = os.path.join(DATABASE_DIR, 'program_settings.json')
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.app_settings, f, ensure_ascii=False, indent=2)
            self.log_info("تم حفظ إعدادات البرنامج بنجاح")
        except Exception as e:
            self.log_error(f"خطأ في حفظ إعدادات البرنامج: {str(e)}")

    def setup_event_handlers(self):
        """إعداد معالجات الأحداث"""
        try:
            # معالج إغلاق النافذة
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # معالج تغيير حجم النافذة
            self.root.bind('<Configure>', self.on_window_resize)

            # معالج اختصارات لوحة المفاتيح
            self.setup_keyboard_shortcuts()

            self.log_info("تم إعداد معالجات الأحداث بنجاح")

        except Exception as e:
            self.log_error(f"خطأ في إعداد معالجات الأحداث: {str(e)}")

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        try:
            # Ctrl+Q للخروج
            self.root.bind('<Control-q>', lambda e: self.on_closing())

            # F11 للشاشة الكاملة
            self.root.bind('<F11>', self.toggle_fullscreen)

            # F5 لتحديث البيانات
            self.root.bind('<F5>', self.refresh_data)

            self.log_info("تم إعداد اختصارات لوحة المفاتيح")

        except Exception as e:
            self.log_error(f"خطأ في إعداد اختصارات لوحة المفاتيح: {str(e)}")

    def on_window_resize(self, event):
        """معالج تغيير حجم النافذة"""
        if event.widget == self.root:
            self.update_status(f"حجم النافذة: {self.root.winfo_width()}x{self.root.winfo_height()}")

    def toggle_fullscreen(self, event=None):
        """تبديل وضع الشاشة الكاملة"""
        try:
            current_state = self.root.attributes('-fullscreen')
            self.root.attributes('-fullscreen', not current_state)
            self.log_info(f"تم تبديل وضع الشاشة الكاملة: {not current_state}")
        except Exception as e:
            self.log_error(f"خطأ في تبديل وضع الشاشة الكاملة: {str(e)}")

    def refresh_data(self, event=None):
        """تحديث البيانات"""
        try:
            self.update_status("جاري تحديث البيانات...")
            # سيتم تنفيذ تحديث البيانات هنا
            self.root.after(1000, lambda: self.update_status("تم تحديث البيانات بنجاح"))
            self.log_info("تم تحديث البيانات")
        except Exception as e:
            self.log_error(f"خطأ في تحديث البيانات: {str(e)}")

    def update_status(self, message):
        """تحديث رسالة شريط الحالة"""
        try:
            if hasattr(self, 'status_label'):
                self.status_label.config(text=message)
        except:
            pass

    def on_closing(self):
        """معالج إغلاق البرنامج"""
        try:
            if messagebox.askokcancel("إغلاق البرنامج", "هل تريد إغلاق البرنامج؟"):
                self.log_info("جاري إغلاق البرنامج...")

                # حفظ الإعدادات قبل الإغلاق
                if hasattr(self, 'app_settings'):
                    self.save_app_settings()

                # تسجيل عملية الخروج
                if self.current_user and self.db_manager:
                    try:
                        log_user_activity(
                            self.db_manager,
                            self.current_user['id'],
                            "إغلاق البرنامج",
                            f"تم إغلاق البرنامج في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                        )
                    except:
                        pass

                self.log_info("تم إغلاق البرنامج بنجاح")
                self.root.destroy()
        except Exception as e:
            self.log_error(f"خطأ في إغلاق البرنامج: {str(e)}")
            self.root.destroy()

    def initialize_database(self):
        """تهيئة قاعدة البيانات المحسنة"""
        try:
            self.update_status("جاري تهيئة قاعدة البيانات...")

            # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
            if not os.path.exists(DATABASE_DIR):
                os.makedirs(DATABASE_DIR)
                self.log_info("تم إنشاء مجلد قاعدة البيانات")

            # تهيئة مدير قاعدة البيانات
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()

            # فحص سلامة قاعدة البيانات
            self.check_database_integrity()

            self.update_status("تم تهيئة قاعدة البيانات بنجاح")
            self.log_info("تم تهيئة قاعدة البيانات بنجاح")

        except Exception as e:
            error_msg = f"حدث خطأ في تهيئة قاعدة البيانات:\n{str(e)}"
            self.log_error(error_msg)
            messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
            sys.exit(1)

    def check_database_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        try:
            # فحص الجداول الأساسية
            required_tables = ['users', 'products', 'customers', 'suppliers',
                             'sales_invoices', 'purchase_invoices', 'user_activity_log']

            for table in required_tables:
                result = self.db_manager.execute_query(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    (table,)
                )
                if not result:
                    self.log_warning(f"الجدول {table} غير موجود")

            self.log_info("تم فحص سلامة قاعدة البيانات")

        except Exception as e:
            self.log_error(f"خطأ في فحص سلامة قاعدة البيانات: {str(e)}")
            
    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول المحسنة"""
        try:
            self.update_status("جاري تحميل شاشة تسجيل الدخول...")
            self.log_info("عرض شاشة تسجيل الدخول")

            # إخفاء شريط الحالة أثناء تسجيل الدخول
            if hasattr(self, 'status_bar'):
                self.status_bar.pack_forget()

            # عرض شاشة تسجيل الدخول
            self.login_screen = LoginScreen(self.root, self.on_login_success)

        except Exception as e:
            self.log_error(f"خطأ في عرض شاشة تسجيل الدخول: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ في عرض شاشة تسجيل الدخول:\n{str(e)}")

    def on_login_success(self, user_data):
        """معالج نجاح تسجيل الدخول المحسن"""
        try:
            self.current_user = user_data
            self.log_info(f"تم تسجيل دخول المستخدم: {user_data.get('username', 'غير محدد')}")

            # إعادة عرض شريط الحالة
            if hasattr(self, 'status_bar'):
                self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

            # تحديث شريط الحالة
            self.update_status(f"مرحباً {user_data.get('full_name', user_data.get('username', 'المستخدم'))}")

            # تسجيل عملية تسجيل الدخول
            self.log_user_login(user_data)

            # فحص النسخ الاحتياطي التلقائي
            self.check_auto_backup()

            # فحص التحديثات والإشعارات
            self.check_notifications()

            # عرض الواجهة الرئيسية
            self.show_main_interface()

        except Exception as e:
            self.log_error(f"خطأ في معالجة تسجيل الدخول: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ في تسجيل الدخول:\n{str(e)}")

    def log_user_login(self, user_data):
        """تسجيل عملية تسجيل الدخول"""
        try:
            login_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            details = f"تسجيل دخول ناجح في {login_time} - الإصدار {self.app_version}"

            log_user_activity(
                self.db_manager,
                user_data['id'],
                "تسجيل الدخول",
                details
            )

            self.log_info(f"تم تسجيل عملية دخول المستخدم {user_data.get('username')}")

        except Exception as e:
            self.log_error(f"خطأ في تسجيل عملية الدخول: {str(e)}")

    def check_notifications(self):
        """فحص الإشعارات والتنبيهات"""
        try:
            notifications = []

            # فحص المنتجات المنخفضة
            low_stock_query = """
                SELECT COUNT(*) as count FROM products
                WHERE is_active = 1 AND stock_quantity <= min_stock_level
            """
            result = self.db_manager.execute_query(low_stock_query)
            if result and result[0]['count'] > 0:
                notifications.append(f"⚠️ يوجد {result[0]['count']} منتج بمخزون منخفض")

            # فحص الفواتير المستحقة
            overdue_query = """
                SELECT COUNT(*) as count FROM sales_invoices
                WHERE remaining_amount > 0 AND due_date < date('now')
            """
            result = self.db_manager.execute_query(overdue_query)
            if result and result[0]['count'] > 0:
                notifications.append(f"💳 يوجد {result[0]['count']} فاتورة مستحقة")

            # عرض الإشعارات
            if notifications:
                notification_msg = "\n".join(notifications)
                self.log_info(f"إشعارات النظام: {notification_msg}")

                # عرض الإشعارات في شريط الحالة
                self.root.after(2000, lambda: self.update_status(notifications[0]))

        except Exception as e:
            self.log_error(f"خطأ في فحص الإشعارات: {str(e)}")

    def check_auto_backup(self):
        """فحص الحاجة للنسخ الاحتياطي التلقائي المحسن"""
        try:
            self.update_status("جاري فحص النسخ الاحتياطي...")

            from utils.backup_manager import BackupManager
            backup_manager = BackupManager(self.db_manager)

            # فحص الحاجة للنسخ الاحتياطي
            need_backup, reason = backup_manager.auto_backup_check(self.current_user['id'])

            if need_backup:
                self.log_info("بدء عملية النسخ الاحتياطي التلقائي")
                self.update_status("جاري إنشاء نسخة احتياطية...")

                # تنفيذ النسخ الاحتياطي في خيط منفصل لتجنب تجميد الواجهة
                backup_thread = threading.Thread(
                    target=self.perform_background_backup,
                    args=(backup_manager,),
                    daemon=True
                )
                backup_thread.start()
            else:
                self.log_info(f"النسخ الاحتياطي التلقائي: {reason}")
                self.update_status("النسخ الاحتياطي محدث")

        except Exception as e:
            self.log_error(f"خطأ في فحص النسخ الاحتياطي التلقائي: {str(e)}")
            self.update_status("خطأ في فحص النسخ الاحتياطي")

    def perform_background_backup(self, backup_manager):
        """تنفيذ النسخ الاحتياطي في الخلفية"""
        try:
            success, result = backup_manager.perform_auto_backup(self.current_user['id'])

            if success:
                self.log_info(f"تم إنشاء نسخة احتياطية تلقائية: {result}")
                self.root.after(0, lambda: self.update_status("تم إنشاء نسخة احتياطية بنجاح"))
            else:
                self.log_error(f"فشل في النسخ الاحتياطي التلقائي: {result}")
                self.root.after(0, lambda: self.update_status("فشل في النسخ الاحتياطي"))

        except Exception as e:
            self.log_error(f"خطأ في النسخ الاحتياطي: {str(e)}")
            self.root.after(0, lambda: self.update_status("خطأ في النسخ الاحتياطي"))

    def show_main_interface(self):
        """عرض الواجهة الرئيسية المحسنة"""
        try:
            self.update_status("جاري تحميل الواجهة الرئيسية...")
            self.log_info("عرض الواجهة الرئيسية")

            # تحديث عنوان النافذة مع اسم المستخدم
            user_name = self.current_user.get('full_name', self.current_user.get('username', 'المستخدم'))
            title = f"🏢 برنامج محاسبة المبيعات والمخازن - {user_name} - الإصدار {self.app_version}"
            self.root.title(title)

            # عرض الواجهة الرئيسية
            self.main_interface = MainInterface(self.root, self.current_user)

            self.update_status(f"مرحباً {user_name} - جاهز للعمل")
            self.log_info("تم تحميل الواجهة الرئيسية بنجاح")

        except Exception as e:
            self.log_error(f"خطأ في عرض الواجهة الرئيسية: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ في عرض الواجهة الرئيسية:\n{str(e)}")

    def run(self):
        """تشغيل البرنامج المحسن"""
        try:
            self.log_info(f"بدء تشغيل البرنامج - الإصدار {self.app_version}")
            self.update_status("جاري بدء التشغيل...")

            # عرض شاشة تسجيل الدخول
            self.show_login_screen()

            # بدء حلقة الأحداث الرئيسية
            self.root.mainloop()

        except Exception as e:
            self.log_error(f"خطأ في تشغيل البرنامج: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ في تشغيل البرنامج:\n{str(e)}")
        finally:
            self.log_info("تم إنهاء البرنامج")

def check_system_requirements():
    """فحص متطلبات النظام"""
    try:
        import sys
        import tkinter

        # فحص إصدار Python
        if sys.version_info < (3, 8):
            raise Exception("يتطلب البرنامج Python 3.8 أو أحدث")

        # فحص توفر tkinter
        root = tkinter.Tk()
        root.withdraw()
        root.destroy()

        print("✅ تم فحص متطلبات النظام بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في متطلبات النظام: {str(e)}")
        return False

def setup_environment():
    """إعداد بيئة التشغيل"""
    try:
        # إنشاء المجلدات المطلوبة
        required_dirs = [
            'database', 'images', 'backup', 'reports', 'logs'
        ]

        for dir_name in required_dirs:
            dir_path = os.path.join(os.path.dirname(__file__), dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"✅ تم إنشاء مجلد: {dir_name}")

        print("✅ تم إعداد بيئة التشغيل بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في إعداد بيئة التشغيل: {str(e)}")
        return False

def show_startup_info():
    """عرض معلومات بدء التشغيل"""
    print("=" * 70)
    print("🏢 برنامج محاسبة المبيعات والمخازن المتطور")
    print("📋 نظام إدارة شامل للمبيعات والمشتريات والمخزون")
    print("🏪 مع نظام الكاشير المتطور (POS System)")
    print("🚀 الإصدار 3.0 - مع نظام نقاط البيع الكامل")
    print("-" * 70)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version.split()[0]}")
    print(f"💻 النظام: {os.name}")
    print("-" * 70)
    print("🆕 الميزات الجديدة في الإصدار 3.0:")
    print("   🏪 نظام كاشير متطور مع قارئ باركود")
    print("   💳 دعم طرق دفع متعددة (نقدي، بطاقة، محفظة)")
    print("   🧾 طباعة الفواتير التلقائية")
    print("   💰 إدارة الدرج النقدي")
    print("   📊 تقارير مبيعات فورية")
    print("   ⌨️ اختصارات لوحة المفاتيح")
    print("   🔊 تنبيهات صوتية")
    print("=" * 70)

def main():
    """الدالة الرئيسية المحسنة"""
    try:
        # عرض معلومات بدء التشغيل
        show_startup_info()

        # فحص متطلبات النظام
        if not check_system_requirements():
            input("اضغط Enter للخروج...")
            sys.exit(1)

        # إعداد بيئة التشغيل
        if not setup_environment():
            input("اضغط Enter للخروج...")
            sys.exit(1)

        print("🚀 جاري بدء تشغيل البرنامج...")
        print("-" * 60)

        # إنشاء وتشغيل التطبيق
        app = SalesInventoryApp()
        app.run()

    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف البرنامج بواسطة المستخدم")
        sys.exit(0)

    except Exception as e:
        error_msg = f"حدث خطأ في تشغيل البرنامج:\n{str(e)}"
        print(f"❌ {error_msg}")

        try:
            messagebox.showerror("خطأ في البرنامج", error_msg)
        except:
            pass

        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()

# ==========================================
# 🎉 نهاية الملف الرئيسي المحسن
# ==========================================
#
# الميزات الجديدة في الإصدار 2.0:
# ✅ نظام تسجيل متقدم مع ملفات السجلات
# ✅ شريط حالة تفاعلي مع الساعة
# ✅ اختصارات لوحة المفاتيح
# ✅ وضع الشاشة الكاملة
# ✅ فحص الإشعارات والتنبيهات
# ✅ نسخ احتياطي في الخلفية
# ✅ فحص متطلبات النظام
# ✅ إعداد بيئة التشغيل التلقائي
# ✅ معالجة أخطاء شاملة
# ✅ واجهة محسنة ومطورة
# ✅ إدارة الإعدادات المتقدمة
#
# المطور: Augment Agent
# التاريخ: 2024-12-21
# الإصدار: 2.0
# ==========================================
