# -*- coding: utf-8 -*-
"""
برنامج محاسبة المبيعات والمخازن
الملف الرئيسي لتشغيل البرنامج
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import *
from utils.database_manager import DatabaseManager
from screens.login_screen import LoginScreen
from screens.main_interface import MainInterface

class SalesInventoryApp:
    """الكلاس الرئيسي للبرنامج"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_main_window()
        self.db_manager = None
        self.current_user = None
        self.initialize_database()
        
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("برنامج محاسبة المبيعات والمخازن")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # توسيط النافذة على الشاشة
        self.center_window()
        
        # إعداد الأيقونة (إذا كانت متوفرة)
        try:
            icon_path = os.path.join(IMAGES_DIR, 'app_icon.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass
            
        # إعداد الخط الافتراضي
        self.root.option_add('*Font', FONTS['normal'])
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
            if not os.path.exists(DATABASE_DIR):
                os.makedirs(DATABASE_DIR)
                
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()
            
        except Exception as e:
            messagebox.showerror("خطأ في قاعدة البيانات", 
                               f"حدث خطأ في تهيئة قاعدة البيانات:\n{str(e)}")
            sys.exit(1)
            
    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول"""
        login_screen = LoginScreen(self.root, self.on_login_success)
        
    def on_login_success(self, user_data):
        """معالج نجاح تسجيل الدخول"""
        self.current_user = user_data
        self.show_main_interface()
        
    def show_main_interface(self):
        """عرض الواجهة الرئيسية للبرنامج"""
        MainInterface(self.root, self.current_user)
        
    def run(self):
        """تشغيل البرنامج"""
        self.show_login_screen()
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = SalesInventoryApp()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل البرنامج:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
