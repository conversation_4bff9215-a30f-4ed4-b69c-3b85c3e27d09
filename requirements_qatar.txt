# متطلبات نظام الكاشير المتطور - دولة قطر
# Qatar Advanced Cashier System Requirements

# مكتبات PDF والطباعة
reportlab>=3.6.0
arabic-reshaper>=2.1.3
python-bidi>=0.4.2

# مكتبات QR Code
qrcode[pil]>=7.3.1
Pillow>=9.0.0

# مكتبات قاعدة البيانات (مدمجة مع Python)
# sqlite3 - مدمجة

# مكتبات الواجهة الرسومية (مدمجة مع Python)
# tkinter - مدمجة

# مكتبات إضافية للنظام
python-dateutil>=2.8.2

# ملاحظات التثبيت:
# pip install -r requirements_qatar.txt

# للتثبيت اليدوي:
# pip install reportlab arabic-reshaper python-bidi qrcode[pil] Pillow python-dateutil
