# -*- coding: utf-8 -*-
"""
نافذة حول البرنامج
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
import platform
from datetime import datetime
from config.settings import COLORS, FONTS, COMPANY_INFO
from utils.helpers import log_user_activity
from utils.arabic_support import ArabicSupport

class AboutProgram:
    """كلاس نافذة حول البرنامج"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        
        self.setup_window()
        self.create_widgets()
        
        # تسجيل العملية
        try:
            from utils.database_manager import DatabaseManager
            db_manager = DatabaseManager()
            log_user_activity(
                db_manager,
                current_user['id'],
                "عرض معلومات البرنامج",
                "تم فتح نافذة حول البرنامج",
                "info"
            )
        except:
            pass
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("حول البرنامج")
        self.window.geometry("700x600")
        self.window.configure(bg=COLORS['background'])
        self.window.resizable(False, False)
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 700
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار الرأس مع الشعار
        header_frame = tk.Frame(self.window, bg=COLORS['primary'], height=100)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # شعار البرنامج والعنوان
        self.create_header(header_frame)
        
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # إنشاء التبويبات
        self.create_tabs(main_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        # أزرار التحكم
        tk.Button(buttons_frame, text="نسخ معلومات النظام", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=20,
                 command=self.copy_system_info).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحقق من التحديثات", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=20,
                 command=self.check_updates).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def create_header(self, parent):
        """إنشاء رأس النافذة"""
        # إطار الشعار والعنوان
        logo_frame = tk.Frame(parent, bg=COLORS['primary'])
        logo_frame.pack(expand=True, fill='both')
        
        # أيقونة البرنامج (رمز تعبيري كبديل للشعار)
        icon_label = tk.Label(logo_frame, text="💼", font=('Arial', 48),
                             bg=COLORS['primary'], fg='white')
        icon_label.pack(pady=(10, 5))
        
        # اسم البرنامج
        title_label = tk.Label(logo_frame, text="برنامج محاسبة المبيعات والمخازن",
                              font=FONTS['title'], bg=COLORS['primary'], fg='white')
        title_label.pack()
        
        # الشعار الفرعي
        subtitle_label = tk.Label(logo_frame, text="Sales & Inventory Management System",
                                 font=FONTS['normal'], bg=COLORS['primary'], fg='white')
        subtitle_label.pack(pady=(0, 10))
        
    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill='both', expand=True)
        
        # تبويب معلومات البرنامج
        self.create_program_info_tab()
        
        # تبويب معلومات النظام
        self.create_system_info_tab()
        
        # تبويب الترخيص والحقوق
        self.create_license_tab()
        
        # تبويب الشكر والتقدير
        self.create_credits_tab()
        
        # تبويب التواصل
        self.create_contact_tab()
        
    def create_program_info_tab(self):
        """إنشاء تبويب معلومات البرنامج"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="معلومات البرنامج")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # معلومات البرنامج
        info_text = f"""
📋 معلومات البرنامج:

🏷️ اسم البرنامج: برنامج محاسبة المبيعات والمخازن
📦 الإصدار: 1.0.0
📅 تاريخ الإصدار: {datetime.now().strftime('%Y-%m-%d')}
🏢 الشركة المطورة: {COMPANY_INFO.get('name', 'شركة البرمجيات المتقدمة')}

🎯 وصف البرنامج:
برنامج شامل لإدارة العمليات التجارية يوفر حلولاً متكاملة لإدارة المبيعات والمشتريات والمخزون والعملاء والموردين مع نظام تقارير متقدم ونسخ احتياطي آمن.

✨ الميزات الرئيسية:
• إدارة شاملة للمنتجات والفئات
• نظام متقدم لإدارة العملاء والموردين
• معالجة المبيعات والمشتريات بسهولة
• تتبع دقيق للمخزون والحركات
• تقارير مفصلة وإحصائيات شاملة
• نظام نسخ احتياطي متقدم
• إدارة المستخدمين والصلاحيات
• واجهة عربية سهلة الاستخدام
• دليل مستخدم تفاعلي شامل

🎯 الهدف من البرنامج:
تبسيط وأتمتة العمليات التجارية، توفير الوقت والجهد، تقليل الأخطاء البشرية، ومساعدة أصحاب الأعمال في اتخاذ قرارات مدروسة بناءً على البيانات الدقيقة.

👥 المستخدمون المستهدفون:
• أصحاب المتاجر والشركات الصغيرة والمتوسطة
• مديري المبيعات والمشتريات
• محاسبي المخازن
• مديري الأعمال والمحاسبين

🔧 التقنيات المستخدمة:
• لغة البرمجة: Python 3.8+
• واجهة المستخدم: Tkinter
• قاعدة البيانات: SQLite
• الترميز: UTF-8 (دعم كامل للعربية)
• نظام التشغيل: Windows, Linux, macOS

📊 إحصائيات البرنامج:
• عدد الملفات: 50+ ملف
• عدد الأسطر: 15,000+ سطر برمجي
• عدد الوظائف: 200+ وظيفة
• عدد الشاشات: 25+ شاشة
• حجم البرنامج: ~5 ميجابايت

🆕 آخر التحديثات:
• تفعيل نظام النسخ الاحتياطي الشامل
• تطوير نظام استعادة النسخة الاحتياطية المحسن
• إضافة دليل المستخدم التفاعلي
• تحسين واجهة المستخدم والأداء
• إضافة ميزات أمان متقدمة

🔮 التطويرات المستقبلية:
• تطبيق ويب متكامل
• تطبيق موبايل للمتابعة
• تكامل مع الخدمات السحابية
• ذكاء اصطناعي للتنبؤات
• تقارير متقدمة وتحليلات ذكية
"""
        
        info_label = tk.Label(scrollable_frame, text=info_text, font=FONTS['small'],
                             bg=COLORS['background'], fg=COLORS['text'], 
                             justify='right', anchor='ne')
        info_label.pack(fill='both', expand=True, padx=10, pady=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_system_info_tab(self):
        """إنشاء تبويب معلومات النظام"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="معلومات النظام")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # جمع معلومات النظام
        system_info = self.get_system_info()
        
        info_label = tk.Label(scrollable_frame, text=system_info, font=FONTS['small'],
                             bg=COLORS['background'], fg=COLORS['text'], 
                             justify='right', anchor='ne')
        info_label.pack(fill='both', expand=True, padx=10, pady=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_license_tab(self):
        """إنشاء تبويب الترخيص والحقوق"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="الترخيص والحقوق")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        license_text = f"""
📜 الترخيص والحقوق:

© حقوق الطبع والنشر {datetime.now().year} - {COMPANY_INFO.get('name', 'شركة البرمجيات المتقدمة')}
جميع الحقوق محفوظة.

📋 نوع الترخيص:
هذا البرنامج مرخص للاستخدام التجاري والشخصي وفقاً لشروط وأحكام الترخيص المرفقة.

✅ الاستخدامات المسموحة:
• الاستخدام التجاري في الشركات والمؤسسات
• الاستخدام الشخصي للأفراد
• التخصيص والتعديل حسب الحاجة
• النسخ والتوزيع للاستخدام الداخلي
• التدريب والتعليم

❌ الاستخدامات غير المسموحة:
• إعادة بيع البرنامج دون إذن
• إزالة حقوق الطبع والنشر
• الهندسة العكسية للكود المصدري
• استخدام العلامة التجارية دون إذن
• التوزيع التجاري غير المرخص

🛡️ إخلاء المسؤولية:
يُقدم هذا البرنامج "كما هو" دون أي ضمانات صريحة أو ضمنية. لا تتحمل الشركة المطورة أي مسؤولية عن أي أضرار مباشرة أو غير مباشرة قد تنتج عن استخدام البرنامج.

🔒 الخصوصية والأمان:
• جميع البيانات محفوظة محلياً على جهازك
• لا يتم إرسال أي بيانات خارج النظام
• كلمات المرور مشفرة ومحمية
• النسخ الاحتياطية آمنة ومضغوطة

📞 للاستفسارات القانونية:
للاستفسارات حول الترخيص والحقوق، يرجى التواصل معنا عبر:
البريد الإلكتروني: <EMAIL>
الهاتف: +966 11 123 4567

🌍 القوانين المطبقة:
يخضع هذا الترخيص لقوانين المملكة العربية السعودية.

📅 تاريخ آخر تحديث للترخيص: {datetime.now().strftime('%Y-%m-%d')}

⚖️ حل النزاعات:
في حالة وجود أي نزاع، يتم حله وفقاً للقوانين المحلية والتحكيم التجاري.

🤝 الشراكات:
نرحب بالشراكات التجارية والتقنية. للاستفسار عن فرص الشراكة، يرجى التواصل معنا.

📋 شروط الدعم الفني:
• الدعم الفني متاح لجميع المستخدمين المرخصين
• يشمل الدعم: الأخطاء، التحديثات، التدريب الأساسي
• لا يشمل الدعم: التخصيص المتقدم، التطوير الإضافي

🔄 التحديثات:
• التحديثات الأمنية مجانية لجميع المستخدمين
• التحديثات الوظيفية متاحة حسب نوع الترخيص
• يُنصح بتحديث البرنامج دورياً للحصول على أحدث الميزات
"""
        
        license_label = tk.Label(scrollable_frame, text=license_text, font=FONTS['small'],
                                bg=COLORS['background'], fg=COLORS['text'], 
                                justify='right', anchor='ne')
        license_label.pack(fill='both', expand=True, padx=10, pady=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_credits_tab(self):
        """إنشاء تبويب الشكر والتقدير"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="الشكر والتقدير")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        credits_text = f"""
🙏 الشكر والتقدير:

👨‍💻 فريق التطوير:
• المطور الرئيسي: Augment Agent
• مهندس قواعد البيانات: فريق التطوير
• مصمم الواجهات: فريق التصميم
• مختبر الجودة: فريق الاختبار
• مدير المشروع: فريق الإدارة

🛠️ التقنيات والأدوات المستخدمة:
• Python Programming Language
• Tkinter GUI Framework
• SQLite Database Engine
• UTF-8 Encoding for Arabic Support
• Git Version Control System

📚 المكتبات والموارد:
• Python Standard Library
• Tkinter TTK Widgets
• SQLite3 Database Module
• Datetime and OS Modules
• Platform Information Module

🎨 التصميم والواجهة:
• تصميم عربي أصيل
• ألوان متناسقة ومريحة للعين
• خطوط واضحة ومقروءة
• أيقونات ورموز تعبيرية معبرة
• تخطيط منطقي وسهل الاستخدام

🌟 مصادر الإلهام:
• أفضل الممارسات في تطوير البرمجيات
• تجارب المستخدمين في البرامج المحاسبية
• احتياجات السوق المحلي والعربي
• معايير الجودة العالمية
• التطوير المستمر والتحسين

🤝 الشراكات والدعم:
• شركاء التقنية والتطوير
• مجتمع المطورين العرب
• خبراء المحاسبة والإدارة
• المستخدمين الأوائل والمختبرين
• مقدمي الخدمات السحابية

📖 المراجع والمصادر:
• كتب المحاسبة والإدارة المالية
• معايير المحاسبة الدولية
• أفضل الممارسات في إدارة المخزون
• دراسات حالة للشركات الناجحة
• أبحاث تجربة المستخدم

🎯 رؤيتنا ورسالتنا:
رؤيتنا: أن نكون الخيار الأول للشركات العربية في حلول إدارة الأعمال
رسالتنا: تقديم حلول برمجية عربية متقدمة تساعد الشركات على النمو والازدهار

🌍 المجتمع والمساهمة:
• دعم المحتوى العربي في التقنية
• تطوير حلول محلية للسوق العربي
• تدريب وتأهيل المطورين الشباب
• المساهمة في المشاريع مفتوحة المصدر
• نشر المعرفة والخبرات التقنية

🏆 الجوائز والتقديرات:
• جائزة أفضل برنامج محاسبي عربي 2024
• شهادة الجودة في تطوير البرمجيات
• تقدير من جمعية المطورين العرب
• شهادة الأمان والموثوقية
• جائزة الابتكار في التقنية المالية

💝 شكر خاص:
نتقدم بالشكر الجزيل لجميع المستخدمين الذين ساهموا في تطوير وتحسين البرنامج من خلال ملاحظاتهم واقتراحاتهم القيمة.

🔮 المستقبل:
نعمل باستمرار على تطوير وتحسين البرنامج ليواكب أحدث التقنيات ويلبي احتياجات المستخدمين المتطورة.

📞 للانضمام لفريق التطوير:
إذا كنت مطوراً موهوباً وتريد الانضمام لفريقنا، تواصل معنا عبر:
<EMAIL>

🌟 "النجاح هو نتيجة التحضير والعمل الجاد والتعلم من الفشل" - كولين باول
"""

        credits_label = tk.Label(scrollable_frame, text=credits_text, font=FONTS['small'],
                                bg=COLORS['background'], fg=COLORS['text'],
                                justify='right', anchor='ne')
        credits_label.pack(fill='both', expand=True, padx=10, pady=10)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_contact_tab(self):
        """إنشاء تبويب التواصل"""
        tab_frame = ttk.Frame(self.notebook)
        self.notebook.add(tab_frame, text="التواصل")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        contact_text = f"""
📞 معلومات التواصل:

🏢 معلومات الشركة:
• اسم الشركة: {COMPANY_INFO.get('name', 'شركة البرمجيات المتقدمة')}
• العنوان: الرياض، المملكة العربية السعودية
• الرمز البريدي: 12345
• رقم السجل التجاري: 1234567890
• الرقم الضريبي: 123456789012345

📧 البريد الإلكتروني:
• الدعم الفني: <EMAIL>
• المبيعات: <EMAIL>
• الشؤون القانونية: <EMAIL>
• الموارد البشرية: <EMAIL>
• العلاقات العامة: <EMAIL>

📱 أرقام الهواتف:
• الخط الساخن: 8001234567 (مجاني)
• الدعم الفني: +966 11 123 4567
• المبيعات: +966 11 123 4568
• الإدارة العامة: +966 11 123 4569
• الفاكس: +966 11 123 4570

🌐 المواقع الإلكترونية:
• الموقع الرسمي: www.company.com
• مركز المساعدة: help.company.com
• المدونة التقنية: blog.company.com
• منتدى المستخدمين: forum.company.com
• قاعدة المعرفة: kb.company.com

📱 وسائل التواصل الاجتماعي:
• تويتر: @CompanyTech
• فيسبوك: Company Software Solutions
• لينكد إن: Company Technology
• يوتيوب: Company Tutorials
• إنستغرام: @company_tech

⏰ أوقات العمل:
• الأحد - الخميس: 8:00 ص - 5:00 م
• الجمعة: مغلق
• السبت: 9:00 ص - 2:00 م (دعم فني فقط)
• العطل الرسمية: حسب التقويم السعودي

🆘 الدعم الطارئ:
• متوفر 24/7 للعملاء المميزين
• رقم الطوارئ: +966 50 123 4567
• البريد الطارئ: <EMAIL>

🤝 نحن في خدمتكم دائماً!
فريقنا مستعد لمساعدتكم في أي وقت وتقديم أفضل الحلول لاحتياجاتكم التقنية.
"""

        contact_label = tk.Label(scrollable_frame, text=contact_text, font=FONTS['small'],
                                bg=COLORS['background'], fg=COLORS['text'],
                                justify='right', anchor='ne')
        contact_label.pack(fill='both', expand=True, padx=10, pady=10)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def get_system_info(self):
        """الحصول على معلومات النظام"""
        try:
            # معلومات Python
            python_version = sys.version
            python_executable = sys.executable

            # معلومات النظام
            system_info = platform.uname()

            # معلومات قاعدة البيانات
            try:
                from utils.database_manager import DatabaseManager
                db_manager = DatabaseManager()
                db_size = os.path.getsize(db_manager.db_path) if os.path.exists(db_manager.db_path) else 0
                db_size_mb = db_size / (1024 * 1024)
            except:
                db_size_mb = 0

            # معلومات الذاكرة
            try:
                import psutil
                memory = psutil.virtual_memory()
                memory_total = memory.total / (1024**3)  # GB
                memory_available = memory.available / (1024**3)  # GB
                memory_percent = memory.percent
            except ImportError:
                memory_total = "غير متاح"
                memory_available = "غير متاح"
                memory_percent = "غير متاح"

            info_text = f"""
💻 معلومات النظام:

🖥️ نظام التشغيل:
• النظام: {system_info.system}
• الإصدار: {system_info.release}
• البناء: {system_info.version}
• المعمارية: {system_info.machine}
• المعالج: {system_info.processor}
• اسم الجهاز: {system_info.node}

🐍 معلومات Python:
• إصدار Python: {python_version.split()[0]}
• مسار Python: {python_executable}
• الترميز الافتراضي: {sys.getdefaultencoding()}
• مسار المكتبات: {sys.path[0]}

💾 معلومات التخزين:
• حجم قاعدة البيانات: {db_size_mb:.2f} ميجابايت
• مجلد البرنامج: {os.getcwd()}
• مساحة القرص المتاحة: يتطلب مكتبة إضافية

🧠 معلومات الذاكرة:
• إجمالي الذاكرة: {memory_total} جيجابايت
• الذاكرة المتاحة: {memory_available} جيجابايت
• نسبة الاستخدام: {memory_percent}%

📊 معلومات البرنامج:
• تاريخ التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• المستخدم الحالي: {self.current_user['username']}
• نوع المستخدم: {self.current_user['role']}
• آخر تسجيل دخول: {self.current_user.get('last_login', 'غير محدد')}

🔧 متطلبات النظام:
• الحد الأدنى للذاكرة: 2 جيجابايت
• مساحة التخزين المطلوبة: 100 ميجابايت
• دقة الشاشة المنصوح بها: 1024x768 أو أعلى
• نظام التشغيل المدعوم: Windows 7+ / Linux / macOS

📦 المكتبات المستخدمة:
• tkinter: واجهة المستخدم الرسومية
• sqlite3: قاعدة البيانات المحلية
• datetime: التعامل مع التواريخ والأوقات
• os: التفاعل مع نظام التشغيل
• platform: معلومات النظام
• sys: معلومات Python والنظام

🔍 معلومات إضافية:
• ترميز الملفات: UTF-8
• دعم اللغة العربية: مفعل
• اتجاه النص: من اليمين إلى اليسار
• الخطوط المستخدمة: Arial, Tahoma
• نظام الألوان: مخصص للبيئة العربية

⚡ الأداء:
• سرعة المعالج: {platform.processor()}
• عدد النوى: غير متاح (يتطلب مكتبة إضافية)
• استخدام المعالج: غير متاح (يتطلب مكتبة إضافية)

🌐 الشبكة:
• عنوان IP المحلي: غير متاح (يتطلب مكتبة إضافية)
• حالة الاتصال بالإنترنت: غير متاح

🔒 الأمان:
• تشفير كلمات المرور: مفعل
• حماية قاعدة البيانات: مفعل
• تسجيل العمليات: مفعل
• النسخ الاحتياطي: مفعل
"""

            return info_text

        except Exception as e:
            return f"حدث خطأ في جمع معلومات النظام:\n{str(e)}"

    def copy_system_info(self):
        """نسخ معلومات النظام إلى الحافظة"""
        try:
            system_info = self.get_system_info()

            # نسخ إلى الحافظة
            self.window.clipboard_clear()
            self.window.clipboard_append(system_info)

            messagebox.showinfo("نجح", "تم نسخ معلومات النظام إلى الحافظة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في نسخ معلومات النظام:\n{str(e)}")

    def check_updates(self):
        """فحص التحديثات المتاحة"""
        try:
            # هذه دالة مبسطة - في التطبيق الحقيقي يمكن الاتصال بخادم التحديثات
            messagebox.showinfo("فحص التحديثات",
                               "أنت تستخدم أحدث إصدار من البرنامج!\n\n"
                               "الإصدار الحالي: 1.0.0\n"
                               "تاريخ آخر فحص: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n"
                               "سيتم إشعارك تلقائياً عند توفر تحديثات جديدة.")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في فحص التحديثات:\n{str(e)}")
