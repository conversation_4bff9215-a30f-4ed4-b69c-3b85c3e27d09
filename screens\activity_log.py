# -*- coding: utf-8 -*-
"""
شاشة سجل نشاط المستخدمين
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import check_user_permission, show_permission_error, get_date_range
from utils.arabic_support import ArabicSupport

class ActivityLog:
    """كلاس سجل نشاط المستخدمين"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'users_management'):
            show_permission_error('عرض سجل النشاط')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_activity_log()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("سجل نشاط المستخدمين")
        self.window.geometry("1200x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1200
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="سجل نشاط المستخدمين",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الفلاتر
        filter_frame = tk.LabelFrame(self.window, text="فلاتر البحث", 
                                    font=FONTS['heading'], bg=COLORS['background'])
        filter_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول من الفلاتر
        row1_frame = tk.Frame(filter_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # من تاريخ
        tk.Label(row1_frame, text="من تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.from_date_var = tk.StringVar(value=get_current_date())
        from_date_entry = tk.Entry(row1_frame, textvariable=self.from_date_var, 
                                  font=FONTS['normal'], width=12, justify='right')
        from_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # إلى تاريخ
        tk.Label(row1_frame, text="إلى تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.to_date_var = tk.StringVar(value=get_current_date())
        to_date_entry = tk.Entry(row1_frame, textvariable=self.to_date_var, 
                                font=FONTS['normal'], width=12, justify='right')
        to_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني من الفلاتر
        row2_frame = tk.Frame(filter_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        # المستخدم
        tk.Label(row2_frame, text="المستخدم:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.user_var = tk.StringVar(value='الكل')
        self.user_combo = ttk.Combobox(row2_frame, textvariable=self.user_var, 
                                      font=FONTS['normal'], width=20, state='readonly')
        self.user_combo.pack(side=tk.RIGHT, padx=5)
        
        # نوع العملية
        tk.Label(row2_frame, text="نوع العملية:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.action_var = tk.StringVar(value='الكل')
        action_combo = ttk.Combobox(row2_frame, textvariable=self.action_var, 
                                   font=FONTS['normal'], width=20, state='readonly')
        action_combo['values'] = ['الكل', 'إضافة مستخدم جديد', 'تعديل مستخدم', 'حذف مستخدم',
                                 'إنشاء فاتورة مبيعات', 'إنشاء فاتورة شراء', 'إضافة منتج', 'تعديل منتج']
        action_combo.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثالث - أزرار الفترات السريعة
        row3_frame = tk.Frame(filter_frame, bg=COLORS['background'])
        row3_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row3_frame, text="فترات سريعة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        quick_periods = [
            ('اليوم', 'today'),
            ('أمس', 'yesterday'),
            ('هذا الأسبوع', 'this_week'),
            ('الأسبوع الماضي', 'last_week'),
            ('هذا الشهر', 'this_month')
        ]
        
        for text, period in quick_periods:
            tk.Button(row3_frame, text=text, font=FONTS['small'],
                     bg=COLORS['info'], fg='white', 
                     command=lambda p=period: self.set_quick_period(p)).pack(side=tk.RIGHT, padx=2)
        
        # زر البحث
        tk.Button(row3_frame, text="بحث", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', 
                 command=self.load_activity_log).pack(side=tk.RIGHT, padx=10)
        
        # إطار جدول السجل
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview للسجل
        columns = ('التاريخ والوقت', 'المستخدم', 'العملية', 'التفاصيل', 'الجدول', 'معرف السجل')
        self.log_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        # تحديد عناوين الأعمدة
        for col in columns:
            self.log_tree.heading(col, text=col)
            if col == 'التاريخ والوقت':
                self.log_tree.column(col, width=150, anchor='center')
            elif col == 'المستخدم':
                self.log_tree.column(col, width=120, anchor='center')
            elif col == 'العملية':
                self.log_tree.column(col, width=150, anchor='center')
            elif col == 'التفاصيل':
                self.log_tree.column(col, width=300, anchor='w')
            else:
                self.log_tree.column(col, width=100, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.log_tree.yview)
        self.log_tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب العناصر
        self.log_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['normal'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        self.stats_labels = {}
        stats_info = [
            ('total_activities', 'إجمالي العمليات'),
            ('today_activities', 'عمليات اليوم'),
            ('active_users', 'المستخدمين النشطين'),
            ('last_activity', 'آخر نشاط')
        ]
        
        for i, (key, label) in enumerate(stats_info):
            frame = tk.Frame(stats_frame, bg=COLORS['background'])
            frame.pack(side=tk.RIGHT, padx=10, pady=5)
            
            tk.Label(frame, text=f"{label}:", font=FONTS['small'], 
                    bg=COLORS['background']).pack()
            
            self.stats_labels[key] = tk.Label(frame, text="0", font=FONTS['normal'], 
                                            bg=COLORS['background'], fg=COLORS['primary'])
            self.stats_labels[key].pack()
        
        # تحميل قائمة المستخدمين
        self.load_users()
        
    def load_users(self):
        """تحميل قائمة المستخدمين للفلتر"""
        try:
            query = "SELECT id, name FROM users WHERE is_active = 1 ORDER BY name"
            users = self.db_manager.execute_query(query)
            
            user_values = ['الكل']
            user_values.extend([f"{user['id']}:{user['name']}" for user in users])
            
            self.user_combo['values'] = user_values
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المستخدمين:\n{str(e)}")
            
    def set_quick_period(self, period):
        """تعيين فترة سريعة"""
        try:
            start_date, end_date = get_date_range(period)
            self.from_date_var.set(start_date.strftime('%Y-%m-%d'))
            self.to_date_var.set(end_date.strftime('%Y-%m-%d'))
            self.load_activity_log()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين الفترة:\n{str(e)}")
            
    def load_activity_log(self):
        """تحميل سجل النشاط"""
        try:
            # مسح البيانات الحالية
            for item in self.log_tree.get_children():
                self.log_tree.delete(item)
                
            # بناء الاستعلام
            query = """
                SELECT ual.timestamp, u.name as user_name, ual.action, 
                       ual.details, ual.table_name, ual.record_id
                FROM user_activity_log ual
                LEFT JOIN users u ON ual.user_id = u.id
                WHERE DATE(ual.timestamp) BETWEEN ? AND ?
            """
            params = [self.from_date_var.get(), self.to_date_var.get()]
            
            # إضافة فلتر المستخدم
            if self.user_var.get() != 'الكل':
                user_id = int(self.user_var.get().split(':')[0])
                query += " AND ual.user_id = ?"
                params.append(user_id)
            
            # إضافة فلتر نوع العملية
            if self.action_var.get() != 'الكل':
                query += " AND ual.action = ?"
                params.append(self.action_var.get())
            
            query += " ORDER BY ual.timestamp DESC LIMIT 1000"
            
            activities = self.db_manager.execute_query(query, params)
            
            # إضافة النتائج إلى الجدول
            for activity in activities:
                self.log_tree.insert('', 'end', values=(
                    activity['timestamp'],
                    activity['user_name'] or 'مستخدم محذوف',
                    activity['action'],
                    activity['details'] or '',
                    activity['table_name'] or '',
                    activity['record_id'] or ''
                ))
                
            # تحديث الإحصائيات
            self.update_statistics()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل السجل:\n{str(e)}")
            
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # إجمالي العمليات في الفترة المحددة
            query = """
                SELECT COUNT(*) as total
                FROM user_activity_log
                WHERE DATE(timestamp) BETWEEN ? AND ?
            """
            result = self.db_manager.execute_query(query, [self.from_date_var.get(), self.to_date_var.get()])
            total_activities = result[0]['total'] if result else 0
            
            # عمليات اليوم
            query = """
                SELECT COUNT(*) as today_total
                FROM user_activity_log
                WHERE DATE(timestamp) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            today_activities = result[0]['today_total'] if result else 0
            
            # المستخدمين النشطين اليوم
            query = """
                SELECT COUNT(DISTINCT user_id) as active_users
                FROM user_activity_log
                WHERE DATE(timestamp) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            active_users = result[0]['active_users'] if result else 0
            
            # آخر نشاط
            query = """
                SELECT timestamp
                FROM user_activity_log
                ORDER BY timestamp DESC
                LIMIT 1
            """
            result = self.db_manager.execute_query(query)
            last_activity = result[0]['timestamp'] if result else 'لا يوجد'
            
            # تحديث التسميات
            self.stats_labels['total_activities'].config(text=str(total_activities))
            self.stats_labels['today_activities'].config(text=str(today_activities))
            self.stats_labels['active_users'].config(text=str(active_users))
            self.stats_labels['last_activity'].config(text=last_activity)
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
