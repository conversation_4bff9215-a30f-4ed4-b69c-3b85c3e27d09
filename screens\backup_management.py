# -*- coding: utf-8 -*-
"""
شاشة إدارة النسخ الاحتياطي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from datetime import datetime
from config.settings import COLORS, FONTS
from utils.backup_manager import BackupManager
from utils.database_manager import DatabaseManager
from utils.helpers import check_user_permission, show_permission_error, log_user_activity, format_file_size
from utils.arabic_support import ArabicSupport

class BackupManagement:
    """كلاس إدارة النسخ الاحتياطي"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        self.backup_manager = BackupManager(self.db_manager)
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'admin'):
            show_permission_error('إدارة النسخ الاحتياطي')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_backup_list()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة النسخ الاحتياطي")
        self.window.geometry("900x600")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 900
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة النسخ الاحتياطي",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['heading'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        self.create_statistics_widgets(stats_frame)
        
        # إطار الأزرار العلوية
        top_buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        top_buttons_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # زر إنشاء نسخة احتياطية
        tk.Button(top_buttons_frame, text="إنشاء نسخة احتياطية", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=20,
                 command=self.create_backup).pack(side=tk.RIGHT, padx=5)
        
        # زر استعادة نسخة احتياطية
        tk.Button(top_buttons_frame, text="استعادة نسخة احتياطية", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=20,
                 command=self.restore_backup).pack(side=tk.RIGHT, padx=5)
        
        # زر النسخ الاحتياطي التلقائي
        tk.Button(top_buttons_frame, text="نسخة احتياطية تلقائية", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=20,
                 command=self.auto_backup).pack(side=tk.RIGHT, padx=5)
        
        # إطار قائمة النسخ الاحتياطية
        list_frame = tk.LabelFrame(self.window, text="قائمة النسخ الاحتياطية", 
                                  font=FONTS['heading'], bg=COLORS['background'])
        list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء جدول النسخ الاحتياطية
        self.create_backup_table(list_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # أزرار الإجراءات
        tk.Button(buttons_frame, text="استعادة المحدد", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=15,
                 command=self.restore_selected).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="حذف المحدد", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.delete_selected).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تنظيف النسخ القديمة", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', width=15,
                 command=self.cleanup_old_backups).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=15,
                 command=self.load_backup_list).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
                 
    def create_statistics_widgets(self, parent):
        """إنشاء عناصر الإحصائيات"""
        stats_container = tk.Frame(parent, bg=COLORS['background'])
        stats_container.pack(fill='x', padx=10, pady=10)
        
        self.stats_labels = {}
        stats_info = [
            ('total_backups', 'إجمالي النسخ', COLORS['primary']),
            ('total_size', 'الحجم الإجمالي', COLORS['info']),
            ('last_backup', 'آخر نسخة احتياطية', COLORS['success']),
            ('auto_status', 'النسخ التلقائي', COLORS['warning'])
        ]
        
        for i, (key, label, color) in enumerate(stats_info):
            stat_frame = tk.Frame(stats_container, bg=COLORS['background'], 
                                 relief='raised', bd=1)
            stat_frame.pack(side=tk.RIGHT, padx=10, pady=5, fill='x', expand=True)
            
            tk.Label(stat_frame, text=label, font=FONTS['small'], 
                    bg=COLORS['background'], fg=COLORS['text']).pack(pady=(5, 0))
            
            self.stats_labels[key] = tk.Label(stat_frame, text="0", font=FONTS['normal'], 
                                            bg=COLORS['background'], fg=color)
            self.stats_labels[key].pack(pady=(0, 5))
            
    def create_backup_table(self, parent):
        """إنشاء جدول النسخ الاحتياطية"""
        table_frame = tk.Frame(parent, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تحديد الأعمدة
        columns = ('اسم الملف', 'النوع', 'التاريخ', 'الحجم', 'الحالة')
        
        self.backup_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة وعرضها
        column_widths = {
            'اسم الملف': 250,
            'النوع': 100,
            'التاريخ': 150,
            'الحجم': 100,
            'الحالة': 100
        }
        
        for col in columns:
            self.backup_tree.heading(col, text=col)
            self.backup_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.backup_tree.yview)
        self.backup_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.backup_tree.xview)
        self.backup_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.backup_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.backup_tree.bind('<Double-1>', self.on_backup_double_click)
        
    def load_backup_list(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            # مسح البيانات الحالية
            for item in self.backup_tree.get_children():
                self.backup_tree.delete(item)
            
            # جلب قائمة النسخ الاحتياطية
            backup_list = self.backup_manager.get_backup_list()
            
            total_backups = len(backup_list)
            total_size = 0
            last_backup_date = "لا توجد"
            
            # عرض البيانات
            for backup in backup_list:
                # تحديد نوع النسخة الاحتياطية
                backup_type_map = {
                    'manual': 'يدوي',
                    'automatic': 'تلقائي',
                    'auto': 'تلقائي',
                    'pre_restore': 'قبل الاستعادة',
                    'unknown': 'غير محدد'
                }
                backup_type = backup_type_map.get(backup['backup_type'], backup['backup_type'])
                
                # تنسيق التاريخ
                date_str = backup['created_at'].strftime('%Y-%m-%d %H:%M')
                
                # تنسيق الحجم
                size_str = format_file_size(backup['size'])
                
                # تحديد الحالة
                status = "صحيح" if os.path.exists(backup['filepath']) else "مفقود"
                
                # تحديد لون الصف
                tags = []
                if backup['backup_type'] == 'automatic':
                    tags = ['auto_backup']
                elif backup['backup_type'] == 'manual':
                    tags = ['manual_backup']
                elif status == "مفقود":
                    tags = ['missing_backup']
                
                self.backup_tree.insert('', 'end', values=(
                    backup['filename'],
                    backup_type,
                    date_str,
                    size_str,
                    status
                ), tags=tags)
                
                total_size += backup['size']
                
                # تحديث تاريخ آخر نسخة احتياطية
                if last_backup_date == "لا توجد":
                    last_backup_date = date_str
            
            # تكوين ألوان الصفوف
            self.backup_tree.tag_configure('auto_backup', background='#e8f5e8')
            self.backup_tree.tag_configure('manual_backup', background='#e8f0ff')
            self.backup_tree.tag_configure('missing_backup', background='#ffe8e8')
            
            # تحديث الإحصائيات
            self.update_statistics(total_backups, total_size, last_backup_date)
            
            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "عرض قائمة النسخ الاحتياطية",
                f"تم عرض {total_backups} نسخة احتياطية",
                "backup"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل قائمة النسخ الاحتياطية:\n{str(e)}")
            
    def update_statistics(self, total_backups, total_size, last_backup_date):
        """تحديث الإحصائيات"""
        self.stats_labels['total_backups'].config(text=str(total_backups))
        self.stats_labels['total_size'].config(text=format_file_size(total_size))
        self.stats_labels['last_backup'].config(text=last_backup_date)
        
        # حالة النسخ التلقائي
        settings = self.backup_manager.load_backup_settings()
        auto_status = "مفعل" if settings.get('auto_backup', True) else "معطل"
        self.stats_labels['auto_status'].config(text=auto_status)
        
    def get_selected_backup(self):
        """الحصول على النسخة الاحتياطية المحددة"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية أولاً")
            return None
            
        item = self.backup_tree.item(selection[0])
        filename = item['values'][0]
        
        # البحث عن النسخة الاحتياطية في القائمة
        backup_list = self.backup_manager.get_backup_list()
        for backup in backup_list:
            if backup['filename'] == filename:
                return backup
                
        return None
        
    def on_backup_double_click(self, event):
        """معالج النقر المزدوج على النسخة الاحتياطية"""
        self.restore_selected()
        
    def create_backup(self):
        """إنشاء نسخة احتياطية جديدة"""
        try:
            success, result = self.backup_manager.create_backup(
                self.current_user['id'], 'manual', show_dialog=True
            )
            
            if success:
                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{os.path.basename(result)}")
                self.load_backup_list()
            else:
                if result != "تم إلغاء العملية":
                    messagebox.showerror("خطأ", result)
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية:\n{str(e)}")
            
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            success, result = self.backup_manager.restore_backup(self.current_user['id'])
            
            if success:
                messagebox.showinfo("نجح", result)
                # إعادة تحميل البيانات
                self.load_backup_list()
            else:
                if result != "تم إلغاء العملية":
                    messagebox.showerror("خطأ", result)
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية:\n{str(e)}")
            
    def restore_selected(self):
        """استعادة النسخة الاحتياطية المحددة"""
        backup = self.get_selected_backup()
        if backup:
            try:
                success, result = self.backup_manager.restore_backup(
                    self.current_user['id'], backup['filepath']
                )
                
                if success:
                    messagebox.showinfo("نجح", result)
                    self.load_backup_list()
                else:
                    if result != "تم إلغاء العملية":
                        messagebox.showerror("خطأ", result)
                        
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية:\n{str(e)}")
                
    def delete_selected(self):
        """حذف النسخة الاحتياطية المحددة"""
        backup = self.get_selected_backup()
        if backup:
            result = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف النسخة الاحتياطية:\n{backup['filename']}؟\n"
                "هذا الإجراء لا يمكن التراجع عنه."
            )
            
            if result:
                try:
                    os.remove(backup['filepath'])
                    
                    # تسجيل العملية
                    log_user_activity(
                        self.db_manager,
                        self.current_user['id'],
                        "حذف نسخة احتياطية",
                        f"تم حذف النسخة الاحتياطية: {backup['filename']}",
                        "backup"
                    )
                    
                    messagebox.showinfo("نجح", "تم حذف النسخة الاحتياطية بنجاح")
                    self.load_backup_list()
                    
                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ في حذف النسخة الاحتياطية:\n{str(e)}")
                    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        result = messagebox.askyesno(
            "تأكيد التنظيف",
            "هل أنت متأكد من تنظيف النسخ الاحتياطية القديمة؟\n"
            "سيتم الاحتفاظ بآخر 10 نسخ احتياطية فقط."
        )
        
        if result:
            try:
                self.backup_manager.cleanup_old_backups(10)
                messagebox.showinfo("نجح", "تم تنظيف النسخ الاحتياطية القديمة بنجاح")
                self.load_backup_list()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في تنظيف النسخ الاحتياطية:\n{str(e)}")
                
    def auto_backup(self):
        """تنفيذ نسخة احتياطية تلقائية"""
        try:
            success, result = self.backup_manager.perform_auto_backup(self.current_user['id'])
            
            if success:
                messagebox.showinfo("نجح", result)
                self.load_backup_list()
            else:
                messagebox.showinfo("معلومات", result)
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في النسخ الاحتياطي التلقائي:\n{str(e)}")
