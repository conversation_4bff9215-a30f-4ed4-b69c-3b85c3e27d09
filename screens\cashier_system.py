# -*- coding: utf-8 -*-
"""
🏪 نظام الكاشير المتطور - الإصدار 3.0
💰 نظام نقاط البيع (POS) الشامل والمتقدم

الميزات:
- واجهة كاشير سريعة ومبسطة
- قارئ الباركود المدمج
- إدارة الدرج النقدي
- طباعة الفواتير التلقائية
- حساب الضرائب والخصومات
- دعم طرق الدفع المتعددة
- تتبع المبيعات اليومية
- إدارة المرتجعات السريعة
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
from datetime import datetime
import threading
import time
from decimal import Decimal
import os
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import *
from utils.database_manager import DatabaseManager
from utils.helpers import log_user_activity, format_currency, is_valid_number

class CashierSystem:
    """
    🏪 نظام الكاشير المتطور
    
    نظام نقاط البيع (POS) شامل يتضمن:
    - واجهة كاشير سريعة
    - قارئ باركود
    - إدارة الدرج النقدي
    - طباعة الفواتير
    - حساب الضرائب
    - طرق دفع متعددة
    """
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # متغيرات النظام
        self.current_sale = {
            'items': [],
            'subtotal': Decimal('0.00'),
            'tax': Decimal('0.00'),
            'discount': Decimal('0.00'),
            'total': Decimal('0.00'),
            'payment_method': 'cash',
            'customer_id': None
        }
        
        # إعدادات الكاشير
        self.cashier_settings = {
            'tax_rate': Decimal('0.15'),  # 15% ضريبة
            'auto_print': True,
            'sound_enabled': True,
            'barcode_scanner': True,
            'cash_drawer': True
        }
        
        # متغيرات الواجهة
        self.barcode_entry = None
        self.items_tree = None
        self.total_label = None
        self.payment_frame = None
        
        self.create_cashier_window()
        self.load_cashier_settings()
        self.setup_keyboard_shortcuts()
        
    def create_cashier_window(self):
        """إنشاء نافذة الكاشير الرئيسية"""
        # إنشاء نافذة جديدة
        self.cashier_window = tk.Toplevel(self.parent)
        self.cashier_window.title("🏪 نظام الكاشير المتطور - POS System")
        self.cashier_window.geometry("1600x1000")
        self.cashier_window.configure(bg=COLORS['background'])
        
        # جعل النافذة في المقدمة
        self.cashier_window.transient(self.parent)
        self.cashier_window.grab_set()
        
        # إعداد الشبكة
        self.cashier_window.grid_rowconfigure(1, weight=1)
        self.cashier_window.grid_columnconfigure(1, weight=1)
        
        # إنشاء المكونات
        self.create_header()
        self.create_main_content()
        self.create_footer()
        
        # تركيز على حقل الباركود
        self.cashier_window.after(100, lambda: self.barcode_entry.focus_set())
        
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = tk.Frame(self.cashier_window, bg=COLORS['primary'], height=80)
        header_frame.grid(row=0, column=0, columnspan=3, sticky='ew', padx=5, pady=5)
        header_frame.grid_propagate(False)
        
        # عنوان النظام
        title_label = tk.Label(
            header_frame,
            text="🏪 نظام الكاشير المتطور",
            font=FONTS['title'],
            bg=COLORS['primary'],
            fg='white'
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # معلومات الكاشير
        cashier_info = tk.Label(
            header_frame,
            text=f"الكاشير: {self.current_user.get('full_name', 'غير محدد')}",
            font=FONTS['normal'],
            bg=COLORS['primary'],
            fg='white'
        )
        cashier_info.pack(side=tk.RIGHT, padx=20, pady=20)
        
        # الوقت والتاريخ
        self.datetime_label = tk.Label(
            header_frame,
            text="",
            font=FONTS['normal'],
            bg=COLORS['primary'],
            fg='white'
        )
        self.datetime_label.pack(side=tk.RIGHT, padx=20, pady=20)
        self.update_datetime()
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.cashier_window, bg=COLORS['background'])
        main_frame.grid(row=1, column=0, columnspan=3, sticky='nsew', padx=5, pady=5)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        
        # إنشاء الأقسام
        self.create_barcode_section(main_frame)
        self.create_items_section(main_frame)
        self.create_totals_section(main_frame)
        self.create_payment_section(main_frame)
        
    def create_barcode_section(self, parent):
        """إنشاء قسم الباركود"""
        barcode_frame = tk.LabelFrame(
            parent,
            text="🔍 قارئ الباركود / البحث السريع",
            font=FONTS['header'],
            bg=COLORS['background'],
            fg=COLORS['text']
        )
        barcode_frame.grid(row=0, column=0, columnspan=2, sticky='ew', padx=5, pady=5)
        
        # حقل الباركود
        tk.Label(barcode_frame, text="الباركود أو اسم المنتج:", 
                font=FONTS['normal'], bg=COLORS['background']).pack(side=tk.LEFT, padx=5)
        
        self.barcode_entry = tk.Entry(
            barcode_frame,
            font=FONTS['large'],
            width=30,
            justify='center'
        )
        self.barcode_entry.pack(side=tk.LEFT, padx=5, pady=10)
        self.barcode_entry.bind('<Return>', self.scan_product)
        self.barcode_entry.bind('<KeyRelease>', self.on_barcode_change)
        
        # أزرار التحكم
        tk.Button(
            barcode_frame,
            text="🔍 بحث",
            font=FONTS['button'],
            bg=COLORS['info'],
            fg='white',
            command=self.scan_product
        ).pack(side=tk.LEFT, padx=5)
        
        tk.Button(
            barcode_frame,
            text="📋 قائمة المنتجات",
            font=FONTS['button'],
            bg=COLORS['secondary'],
            fg='white',
            command=self.show_products_list
        ).pack(side=tk.LEFT, padx=5)
        
    def create_items_section(self, parent):
        """إنشاء قسم عرض المنتجات"""
        items_frame = tk.LabelFrame(
            parent,
            text="🛒 عناصر الفاتورة",
            font=FONTS['header'],
            bg=COLORS['background'],
            fg=COLORS['text']
        )
        items_frame.grid(row=1, column=0, sticky='nsew', padx=5, pady=5)
        items_frame.grid_rowconfigure(0, weight=1)
        items_frame.grid_columnconfigure(0, weight=1)
        
        # جدول المنتجات
        columns = ('الرقم', 'المنتج', 'الكمية', 'السعر', 'الإجمالي')
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        self.items_tree.heading('الرقم', text='#')
        self.items_tree.heading('المنتج', text='المنتج')
        self.items_tree.heading('الكمية', text='الكمية')
        self.items_tree.heading('السعر', text='السعر')
        self.items_tree.heading('الإجمالي', text='الإجمالي')
        
        # تحديد عرض الأعمدة
        self.items_tree.column('الرقم', width=50, anchor='center')
        self.items_tree.column('المنتج', width=300, anchor='w')
        self.items_tree.column('الكمية', width=80, anchor='center')
        self.items_tree.column('السعر', width=100, anchor='e')
        self.items_tree.column('الإجمالي', width=120, anchor='e')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(items_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.grid(row=0, column=0, sticky='nsew')
        scrollbar.grid(row=0, column=1, sticky='ns')
        
        # ربط الأحداث
        self.items_tree.bind('<Double-1>', self.edit_item)
        self.items_tree.bind('<Delete>', self.remove_item)
        
        # أزرار التحكم في المنتجات
        buttons_frame = tk.Frame(items_frame, bg=COLORS['background'])
        buttons_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=5)
        
        tk.Button(
            buttons_frame,
            text="✏️ تعديل",
            font=FONTS['button'],
            bg=COLORS['warning'],
            fg='white',
            command=self.edit_item
        ).pack(side=tk.LEFT, padx=5)
        
        tk.Button(
            buttons_frame,
            text="🗑️ حذف",
            font=FONTS['button'],
            bg=COLORS['danger'],
            fg='white',
            command=self.remove_item
        ).pack(side=tk.LEFT, padx=5)
        
        tk.Button(
            buttons_frame,
            text="🧹 مسح الكل",
            font=FONTS['button'],
            bg=COLORS['secondary'],
            fg='white',
            command=self.clear_all_items
        ).pack(side=tk.LEFT, padx=5)
        
    def create_totals_section(self, parent):
        """إنشاء قسم الإجماليات"""
        totals_frame = tk.LabelFrame(
            parent,
            text="💰 الإجماليات",
            font=FONTS['header'],
            bg=COLORS['background'],
            fg=COLORS['text']
        )
        totals_frame.grid(row=1, column=1, sticky='nsew', padx=5, pady=5)
        
        # الإجمالي الفرعي
        tk.Label(totals_frame, text="الإجمالي الفرعي:", 
                font=FONTS['large'], bg=COLORS['background']).grid(row=0, column=0, sticky='w', padx=10, pady=5)
        self.subtotal_label = tk.Label(totals_frame, text="0.00 ريال", 
                                      font=FONTS['large'], bg=COLORS['background'], fg=COLORS['info'])
        self.subtotal_label.grid(row=0, column=1, sticky='e', padx=10, pady=5)
        
        # الخصم
        tk.Label(totals_frame, text="الخصم:", 
                font=FONTS['large'], bg=COLORS['background']).grid(row=1, column=0, sticky='w', padx=10, pady=5)
        self.discount_label = tk.Label(totals_frame, text="0.00 ريال", 
                                      font=FONTS['large'], bg=COLORS['background'], fg=COLORS['warning'])
        self.discount_label.grid(row=1, column=1, sticky='e', padx=10, pady=5)
        
        # الضريبة
        tk.Label(totals_frame, text="الضريبة (15%):", 
                font=FONTS['large'], bg=COLORS['background']).grid(row=2, column=0, sticky='w', padx=10, pady=5)
        self.tax_label = tk.Label(totals_frame, text="0.00 ريال", 
                                 font=FONTS['large'], bg=COLORS['background'], fg=COLORS['secondary'])
        self.tax_label.grid(row=2, column=1, sticky='e', padx=10, pady=5)
        
        # الإجمالي النهائي
        tk.Label(totals_frame, text="الإجمالي النهائي:", 
                font=FONTS['title'], bg=COLORS['background']).grid(row=3, column=0, sticky='w', padx=10, pady=10)
        self.total_label = tk.Label(totals_frame, text="0.00 ريال", 
                                   font=FONTS['title'], bg=COLORS['background'], fg=COLORS['success'])
        self.total_label.grid(row=3, column=1, sticky='e', padx=10, pady=10)
        
        # أزرار الخصم والضريبة
        buttons_frame = tk.Frame(totals_frame, bg=COLORS['background'])
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        tk.Button(
            buttons_frame,
            text="💸 إضافة خصم",
            font=FONTS['button'],
            bg=COLORS['warning'],
            fg='white',
            command=self.add_discount
        ).pack(side=tk.LEFT, padx=5)
        
        tk.Button(
            buttons_frame,
            text="🧾 تعديل الضريبة",
            font=FONTS['button'],
            bg=COLORS['info'],
            fg='white',
            command=self.modify_tax
        ).pack(side=tk.LEFT, padx=5)
        
    def create_payment_section(self, parent):
        """إنشاء قسم الدفع"""
        payment_frame = tk.LabelFrame(
            parent,
            text="💳 طرق الدفع",
            font=FONTS['header'],
            bg=COLORS['background'],
            fg=COLORS['text']
        )
        payment_frame.grid(row=2, column=0, columnspan=2, sticky='ew', padx=5, pady=5)
        
        # طرق الدفع
        methods_frame = tk.Frame(payment_frame, bg=COLORS['background'])
        methods_frame.pack(fill='x', padx=10, pady=5)
        
        self.payment_method = tk.StringVar(value='cash')
        
        tk.Radiobutton(
            methods_frame,
            text="💵 نقدي",
            variable=self.payment_method,
            value='cash',
            font=FONTS['normal'],
            bg=COLORS['background']
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Radiobutton(
            methods_frame,
            text="💳 بطاقة ائتمان",
            variable=self.payment_method,
            value='credit',
            font=FONTS['normal'],
            bg=COLORS['background']
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Radiobutton(
            methods_frame,
            text="📱 محفظة إلكترونية",
            variable=self.payment_method,
            value='wallet',
            font=FONTS['normal'],
            bg=COLORS['background']
        ).pack(side=tk.LEFT, padx=10)
        
        # أزرار الدفع
        payment_buttons_frame = tk.Frame(payment_frame, bg=COLORS['background'])
        payment_buttons_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(
            payment_buttons_frame,
            text="💰 دفع نقدي سريع",
            font=FONTS['large'],
            bg=COLORS['success'],
            fg='white',
            height=2,
            command=self.quick_cash_payment
        ).pack(side=tk.LEFT, padx=5, fill='x', expand=True)
        
        tk.Button(
            payment_buttons_frame,
            text="💳 دفع بالبطاقة",
            font=FONTS['large'],
            bg=COLORS['info'],
            fg='white',
            height=2,
            command=self.card_payment
        ).pack(side=tk.LEFT, padx=5, fill='x', expand=True)
        
        tk.Button(
            payment_buttons_frame,
            text="🧾 طباعة الفاتورة",
            font=FONTS['large'],
            bg=COLORS['primary'],
            fg='white',
            height=2,
            command=self.print_receipt
        ).pack(side=tk.LEFT, padx=5, fill='x', expand=True)
        
    def create_footer(self):
        """إنشاء تذييل النافذة"""
        footer_frame = tk.Frame(self.cashier_window, bg=COLORS['secondary'], height=50)
        footer_frame.grid(row=2, column=0, columnspan=3, sticky='ew', padx=5, pady=5)
        footer_frame.grid_propagate(False)
        
        # أزرار التحكم
        tk.Button(
            footer_frame,
            text="🏠 العودة للرئيسية",
            font=FONTS['button'],
            bg=COLORS['warning'],
            fg='white',
            command=self.close_cashier
        ).pack(side=tk.LEFT, padx=10, pady=10)
        
        tk.Button(
            footer_frame,
            text="📊 تقرير المبيعات",
            font=FONTS['button'],
            bg=COLORS['info'],
            fg='white',
            command=self.show_sales_report
        ).pack(side=tk.LEFT, padx=10, pady=10)
        
        tk.Button(
            footer_frame,
            text="💰 إدارة الدرج النقدي",
            font=FONTS['button'],
            bg=COLORS['primary'],
            fg='white',
            command=self.manage_cash_drawer
        ).pack(side=tk.LEFT, padx=10, pady=10)
        
        # معلومات الحالة
        self.status_label = tk.Label(
            footer_frame,
            text="جاهز للبيع",
            font=FONTS['normal'],
            bg=COLORS['secondary'],
            fg='white'
        )
        self.status_label.pack(side=tk.RIGHT, padx=10, pady=10)

    def update_datetime(self):
        """تحديث الوقت والتاريخ"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'datetime_label'):
                self.datetime_label.config(text=current_time)
            self.cashier_window.after(1000, self.update_datetime)
        except:
            pass

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.cashier_window.bind('<F1>', lambda e: self.show_help())
        self.cashier_window.bind('<F2>', lambda e: self.quick_cash_payment())
        self.cashier_window.bind('<F3>', lambda e: self.card_payment())
        self.cashier_window.bind('<F4>', lambda e: self.print_receipt())
        self.cashier_window.bind('<F5>', lambda e: self.show_products_list())
        self.cashier_window.bind('<F9>', lambda e: self.manage_cash_drawer())
        self.cashier_window.bind('<Escape>', lambda e: self.close_cashier())
        self.cashier_window.bind('<Control-n>', lambda e: self.new_sale())
        self.cashier_window.bind('<Control-d>', lambda e: self.add_discount())

    def load_cashier_settings(self):
        """تحميل إعدادات الكاشير"""
        try:
            settings_file = os.path.join(DATABASE_DIR, 'cashier_settings.json')
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    self.cashier_settings.update(json.load(f))
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الكاشير: {str(e)}")

    def save_cashier_settings(self):
        """حفظ إعدادات الكاشير"""
        try:
            settings_file = os.path.join(DATABASE_DIR, 'cashier_settings.json')
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.cashier_settings, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الكاشير: {str(e)}")

    def on_barcode_change(self, event=None):
        """معالج تغيير نص الباركود"""
        barcode = self.barcode_entry.get().strip()
        if len(barcode) >= 3:  # بحث تلقائي عند كتابة 3 أحرف أو أكثر
            self.search_products_live(barcode)

    def search_products_live(self, search_term):
        """البحث المباشر في المنتجات"""
        try:
            query = """
                SELECT id, name, barcode, selling_price, stock_quantity
                FROM products
                WHERE is_active = 1
                AND (name LIKE ? OR barcode LIKE ?)
                LIMIT 5
            """
            results = self.db_manager.execute_query(query, (f'%{search_term}%', f'%{search_term}%'))

            if results:
                # عرض اقتراحات البحث (يمكن تطويرها لاحقاً)
                pass

        except Exception as e:
            print(f"خطأ في البحث المباشر: {str(e)}")

    def scan_product(self, event=None):
        """مسح المنتج بالباركود أو البحث"""
        barcode = self.barcode_entry.get().strip()
        if not barcode:
            messagebox.showwarning("تحذير", "يرجى إدخال الباركود أو اسم المنتج")
            return

        try:
            # البحث بالباركود أولاً
            query = """
                SELECT id, name, barcode, selling_price, stock_quantity, cost_price
                FROM products
                WHERE is_active = 1 AND barcode = ?
            """
            result = self.db_manager.execute_query(query, (barcode,))

            if not result:
                # البحث بالاسم
                query = """
                    SELECT id, name, barcode, selling_price, stock_quantity, cost_price
                    FROM products
                    WHERE is_active = 1 AND name LIKE ?
                    LIMIT 1
                """
                result = self.db_manager.execute_query(query, (f'%{barcode}%',))

            if result:
                product = result[0]
                self.add_product_to_sale(product)
                self.barcode_entry.delete(0, tk.END)
                self.barcode_entry.focus_set()
            else:
                messagebox.showinfo("غير موجود", f"لم يتم العثور على منتج بالباركود: {barcode}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في البحث عن المنتج:\n{str(e)}")

    def add_product_to_sale(self, product, quantity=1):
        """إضافة منتج إلى الفاتورة"""
        try:
            # التحقق من المخزون
            if product['stock_quantity'] < quantity:
                messagebox.showwarning("تحذير", f"المخزون غير كافي. المتوفر: {product['stock_quantity']}")
                return

            # البحث عن المنتج في الفاتورة الحالية
            existing_item = None
            for item in self.current_sale['items']:
                if item['product_id'] == product['id']:
                    existing_item = item
                    break

            if existing_item:
                # زيادة الكمية
                new_quantity = existing_item['quantity'] + quantity
                if product['stock_quantity'] < new_quantity:
                    messagebox.showwarning("تحذير", f"المخزون غير كافي. المتوفر: {product['stock_quantity']}")
                    return
                existing_item['quantity'] = new_quantity
                existing_item['total'] = Decimal(str(existing_item['price'])) * Decimal(str(new_quantity))
            else:
                # إضافة منتج جديد
                item = {
                    'product_id': product['id'],
                    'name': product['name'],
                    'barcode': product['barcode'],
                    'price': Decimal(str(product['selling_price'])),
                    'cost_price': Decimal(str(product['cost_price'])),
                    'quantity': quantity,
                    'total': Decimal(str(product['selling_price'])) * Decimal(str(quantity))
                }
                self.current_sale['items'].append(item)

            # تحديث العرض
            self.update_items_display()
            self.calculate_totals()

            # تشغيل صوت (إذا كان مفعلاً)
            if self.cashier_settings['sound_enabled']:
                self.play_beep_sound()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إضافة المنتج:\n{str(e)}")

    def update_items_display(self):
        """تحديث عرض المنتجات في الجدول"""
        # مسح الجدول
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة المنتجات
        for i, item in enumerate(self.current_sale['items'], 1):
            self.items_tree.insert('', 'end', values=(
                i,
                item['name'],
                item['quantity'],
                f"{item['price']:.2f}",
                f"{item['total']:.2f}"
            ))

    def calculate_totals(self):
        """حساب الإجماليات"""
        try:
            # حساب الإجمالي الفرعي
            subtotal = sum(item['total'] for item in self.current_sale['items'])
            self.current_sale['subtotal'] = subtotal

            # حساب الخصم
            discount = self.current_sale['discount']

            # حساب الضريبة
            taxable_amount = subtotal - discount
            tax = taxable_amount * self.cashier_settings['tax_rate']
            self.current_sale['tax'] = tax

            # حساب الإجمالي النهائي
            total = taxable_amount + tax
            self.current_sale['total'] = total

            # تحديث العرض
            self.subtotal_label.config(text=f"{subtotal:.2f} ريال")
            self.discount_label.config(text=f"{discount:.2f} ريال")
            self.tax_label.config(text=f"{tax:.2f} ريال")
            self.total_label.config(text=f"{total:.2f} ريال")

        except Exception as e:
            print(f"خطأ في حساب الإجماليات: {str(e)}")

    def play_beep_sound(self):
        """تشغيل صوت التنبيه"""
        try:
            # يمكن استخدام winsound في Windows أو playsound
            import winsound
            winsound.Beep(1000, 100)  # تردد 1000 هرتز لمدة 100 مللي ثانية
        except:
            # في حالة عدم توفر الصوت، طباعة رسالة
            print("🔊 تم إضافة المنتج")

    def show_products_list(self):
        """عرض قائمة المنتجات للاختيار"""
        products_window = tk.Toplevel(self.cashier_window)
        products_window.title("📋 قائمة المنتجات")
        products_window.geometry("800x600")
        products_window.transient(self.cashier_window)
        products_window.grab_set()

        # إطار البحث
        search_frame = tk.Frame(products_window)
        search_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(search_frame, text="البحث:", font=FONTS['normal']).pack(side=tk.LEFT)
        search_entry = tk.Entry(search_frame, font=FONTS['normal'], width=30)
        search_entry.pack(side=tk.LEFT, padx=5)

        # جدول المنتجات
        columns = ('الرقم', 'الاسم', 'الباركود', 'السعر', 'المخزون')
        products_tree = ttk.Treeview(products_window, columns=columns, show='headings')

        for col in columns:
            products_tree.heading(col, text=col)
            products_tree.column(col, width=150)

        # تحميل المنتجات
        self.load_products_list(products_tree)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(products_window, orient='vertical', command=products_tree.yview)
        products_tree.configure(yscrollcommand=scrollbar.set)

        products_tree.pack(side='left', fill='both', expand=True, padx=10, pady=5)
        scrollbar.pack(side='right', fill='y', pady=5)

        # ربط النقر المزدوج
        def on_product_select(event):
            selection = products_tree.selection()
            if selection:
                item = products_tree.item(selection[0])
                product_id = item['values'][0]
                self.select_product_from_list(product_id)
                products_window.destroy()

        products_tree.bind('<Double-1>', on_product_select)

        # زر الإضافة
        tk.Button(
            products_window,
            text="إضافة المنتج المحدد",
            font=FONTS['button'],
            bg=COLORS['success'],
            fg='white',
            command=lambda: on_product_select(None)
        ).pack(pady=10)

    def load_products_list(self, tree):
        """تحميل قائمة المنتجات"""
        try:
            query = """
                SELECT id, name, barcode, selling_price, stock_quantity
                FROM products
                WHERE is_active = 1
                ORDER BY name
            """
            products = self.db_manager.execute_query(query)

            for product in products:
                tree.insert('', 'end', values=(
                    product['id'],
                    product['name'],
                    product['barcode'] or 'غير محدد',
                    f"{product['selling_price']:.2f}",
                    product['stock_quantity']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المنتجات:\n{str(e)}")

    def select_product_from_list(self, product_id):
        """اختيار منتج من القائمة"""
        try:
            query = """
                SELECT id, name, barcode, selling_price, stock_quantity, cost_price
                FROM products
                WHERE id = ?
            """
            result = self.db_manager.execute_query(query, (product_id,))

            if result:
                product = result[0]
                # طلب الكمية
                quantity = simpledialog.askinteger(
                    "الكمية",
                    f"أدخل كمية {product['name']}:",
                    initialvalue=1,
                    minvalue=1,
                    maxvalue=product['stock_quantity']
                )

                if quantity:
                    self.add_product_to_sale(product, quantity)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في اختيار المنتج:\n{str(e)}")

    def edit_item(self, event=None):
        """تعديل منتج في الفاتورة"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return

        try:
            item_index = int(self.items_tree.item(selection[0])['values'][0]) - 1
            item = self.current_sale['items'][item_index]

            # نافذة التعديل
            edit_window = tk.Toplevel(self.cashier_window)
            edit_window.title("تعديل المنتج")
            edit_window.geometry("400x300")
            edit_window.transient(self.cashier_window)
            edit_window.grab_set()

            # معلومات المنتج
            tk.Label(edit_window, text=f"المنتج: {item['name']}", font=FONTS['header']).pack(pady=10)

            # الكمية
            tk.Label(edit_window, text="الكمية:", font=FONTS['normal']).pack()
            quantity_var = tk.StringVar(value=str(item['quantity']))
            quantity_entry = tk.Entry(edit_window, textvariable=quantity_var, font=FONTS['normal'])
            quantity_entry.pack(pady=5)

            # السعر
            tk.Label(edit_window, text="السعر:", font=FONTS['normal']).pack()
            price_var = tk.StringVar(value=str(item['price']))
            price_entry = tk.Entry(edit_window, textvariable=price_var, font=FONTS['normal'])
            price_entry.pack(pady=5)

            def save_changes():
                try:
                    new_quantity = int(quantity_var.get())
                    new_price = float(price_var.get())

                    if new_quantity <= 0:
                        messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                        return

                    if new_price <= 0:
                        messagebox.showerror("خطأ", "السعر يجب أن يكون أكبر من صفر")
                        return

                    # تحديث المنتج
                    item['quantity'] = new_quantity
                    item['price'] = Decimal(str(new_price))
                    item['total'] = Decimal(str(new_price)) * Decimal(str(new_quantity))

                    # تحديث العرض
                    self.update_items_display()
                    self.calculate_totals()

                    edit_window.destroy()

                except ValueError:
                    messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")
                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ في التعديل:\n{str(e)}")

            # أزرار التحكم
            buttons_frame = tk.Frame(edit_window)
            buttons_frame.pack(pady=20)

            tk.Button(buttons_frame, text="حفظ", command=save_changes,
                     bg=COLORS['success'], fg='white').pack(side=tk.LEFT, padx=5)
            tk.Button(buttons_frame, text="إلغاء", command=edit_window.destroy,
                     bg=COLORS['danger'], fg='white').pack(side=tk.LEFT, padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعديل المنتج:\n{str(e)}")

    def remove_item(self, event=None):
        """حذف منتج من الفاتورة"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
            return

        try:
            item_index = int(self.items_tree.item(selection[0])['values'][0]) - 1
            item = self.current_sale['items'][item_index]

            if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف {item['name']}؟"):
                del self.current_sale['items'][item_index]
                self.update_items_display()
                self.calculate_totals()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حذف المنتج:\n{str(e)}")

    def clear_all_items(self):
        """مسح جميع المنتجات"""
        if not self.current_sale['items']:
            messagebox.showinfo("معلومات", "لا توجد منتجات لمسحها")
            return

        if messagebox.askyesno("تأكيد المسح", "هل تريد مسح جميع المنتجات؟"):
            self.current_sale['items'] = []
            self.current_sale['discount'] = Decimal('0.00')
            self.update_items_display()
            self.calculate_totals()

    def add_discount(self):
        """إضافة خصم"""
        try:
            if not self.current_sale['items']:
                messagebox.showwarning("تحذير", "لا توجد منتجات لتطبيق الخصم عليها")
                return

            discount_window = tk.Toplevel(self.cashier_window)
            discount_window.title("إضافة خصم")
            discount_window.geometry("300x200")
            discount_window.transient(self.cashier_window)
            discount_window.grab_set()

            tk.Label(discount_window, text="نوع الخصم:", font=FONTS['normal']).pack(pady=5)

            discount_type = tk.StringVar(value='amount')
            tk.Radiobutton(discount_window, text="مبلغ ثابت", variable=discount_type,
                          value='amount').pack()
            tk.Radiobutton(discount_window, text="نسبة مئوية", variable=discount_type,
                          value='percentage').pack()

            tk.Label(discount_window, text="قيمة الخصم:", font=FONTS['normal']).pack(pady=5)
            discount_value = tk.Entry(discount_window, font=FONTS['normal'])
            discount_value.pack(pady=5)

            def apply_discount():
                try:
                    value = float(discount_value.get())
                    if value <= 0:
                        messagebox.showerror("خطأ", "قيمة الخصم يجب أن تكون أكبر من صفر")
                        return

                    if discount_type.get() == 'percentage':
                        if value > 100:
                            messagebox.showerror("خطأ", "نسبة الخصم لا يمكن أن تزيد عن 100%")
                            return
                        discount_amount = self.current_sale['subtotal'] * Decimal(str(value)) / Decimal('100')
                    else:
                        discount_amount = Decimal(str(value))

                    if discount_amount > self.current_sale['subtotal']:
                        messagebox.showerror("خطأ", "قيمة الخصم لا يمكن أن تزيد عن الإجمالي الفرعي")
                        return

                    self.current_sale['discount'] = discount_amount
                    self.calculate_totals()
                    discount_window.destroy()

                except ValueError:
                    messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة")
                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ في تطبيق الخصم:\n{str(e)}")

            tk.Button(discount_window, text="تطبيق الخصم", command=apply_discount,
                     bg=COLORS['success'], fg='white').pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إضافة الخصم:\n{str(e)}")

    def modify_tax(self):
        """تعديل الضريبة"""
        try:
            current_rate = float(self.cashier_settings['tax_rate'] * 100)
            new_rate = simpledialog.askfloat(
                "تعديل الضريبة",
                f"أدخل معدل الضريبة الجديد (%):\nالمعدل الحالي: {current_rate}%",
                initialvalue=current_rate,
                minvalue=0,
                maxvalue=100
            )

            if new_rate is not None:
                self.cashier_settings['tax_rate'] = Decimal(str(new_rate)) / Decimal('100')
                self.calculate_totals()
                self.save_cashier_settings()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعديل الضريبة:\n{str(e)}")

    def quick_cash_payment(self):
        """دفع نقدي سريع"""
        if not self.current_sale['items']:
            messagebox.showwarning("تحذير", "لا توجد منتجات في الفاتورة")
            return

        try:
            total_amount = self.current_sale['total']

            # نافذة الدفع النقدي
            payment_window = tk.Toplevel(self.cashier_window)
            payment_window.title("💰 الدفع النقدي")
            payment_window.geometry("400x300")
            payment_window.transient(self.cashier_window)
            payment_window.grab_set()

            # عرض المبلغ المطلوب
            tk.Label(payment_window, text=f"المبلغ المطلوب: {total_amount:.2f} ريال",
                    font=FONTS['large'], fg=COLORS['primary']).pack(pady=10)

            # المبلغ المدفوع
            tk.Label(payment_window, text="المبلغ المدفوع:", font=FONTS['normal']).pack()
            paid_amount_var = tk.StringVar(value=str(total_amount))
            paid_entry = tk.Entry(payment_window, textvariable=paid_amount_var,
                                 font=FONTS['large'], justify='center')
            paid_entry.pack(pady=5)
            paid_entry.select_range(0, tk.END)
            paid_entry.focus_set()

            # الباقي
            change_label = tk.Label(payment_window, text="الباقي: 0.00 ريال",
                                   font=FONTS['large'], fg=COLORS['success'])
            change_label.pack(pady=10)

            def calculate_change():
                try:
                    paid = Decimal(paid_amount_var.get())
                    change = paid - total_amount
                    change_label.config(text=f"الباقي: {change:.2f} ريال")
                    if change < 0:
                        change_label.config(fg=COLORS['danger'])
                    else:
                        change_label.config(fg=COLORS['success'])
                except:
                    change_label.config(text="الباقي: خطأ في المبلغ", fg=COLORS['danger'])

            paid_amount_var.trace_add('write', lambda *args: calculate_change())

            def complete_payment():
                try:
                    paid = Decimal(paid_amount_var.get())
                    if paid < total_amount:
                        messagebox.showerror("خطأ", "المبلغ المدفوع أقل من المطلوب")
                        return

                    change = paid - total_amount

                    # حفظ الفاتورة
                    invoice_id = self.save_invoice('cash', paid, change)

                    if invoice_id:
                        # عرض رسالة النجاح
                        success_msg = f"تم الدفع بنجاح!\nرقم الفاتورة: {invoice_id}\nالباقي: {change:.2f} ريال"
                        messagebox.showinfo("نجح الدفع", success_msg)

                        # طباعة الفاتورة
                        if self.cashier_settings['auto_print']:
                            self.print_receipt(invoice_id)

                        # بدء فاتورة جديدة
                        self.new_sale()
                        payment_window.destroy()

                except ValueError:
                    messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ في الدفع:\n{str(e)}")

            # أزرار سريعة للمبالغ
            quick_amounts_frame = tk.Frame(payment_window)
            quick_amounts_frame.pack(pady=10)

            quick_amounts = [10, 20, 50, 100, 200, 500]
            for amount in quick_amounts:
                if amount >= total_amount:
                    tk.Button(
                        quick_amounts_frame,
                        text=f"{amount}",
                        command=lambda a=amount: paid_amount_var.set(str(a)),
                        bg=COLORS['info'],
                        fg='white'
                    ).pack(side=tk.LEFT, padx=2)

            # أزرار التحكم
            buttons_frame = tk.Frame(payment_window)
            buttons_frame.pack(pady=20)

            tk.Button(buttons_frame, text="💰 تأكيد الدفع", command=complete_payment,
                     bg=COLORS['success'], fg='white', font=FONTS['button']).pack(side=tk.LEFT, padx=5)
            tk.Button(buttons_frame, text="إلغاء", command=payment_window.destroy,
                     bg=COLORS['danger'], fg='white').pack(side=tk.LEFT, padx=5)

            # ربط Enter بتأكيد الدفع
            paid_entry.bind('<Return>', lambda e: complete_payment())

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في الدفع النقدي:\n{str(e)}")

    def card_payment(self):
        """دفع بالبطاقة"""
        if not self.current_sale['items']:
            messagebox.showwarning("تحذير", "لا توجد منتجات في الفاتورة")
            return

        try:
            total_amount = self.current_sale['total']

            # نافذة الدفع بالبطاقة
            payment_window = tk.Toplevel(self.cashier_window)
            payment_window.title("💳 الدفع بالبطاقة")
            payment_window.geometry("400x250")
            payment_window.transient(self.cashier_window)
            payment_window.grab_set()

            # عرض المبلغ
            tk.Label(payment_window, text=f"المبلغ: {total_amount:.2f} ريال",
                    font=FONTS['large'], fg=COLORS['primary']).pack(pady=20)

            # نوع البطاقة
            tk.Label(payment_window, text="نوع البطاقة:", font=FONTS['normal']).pack()
            card_type = tk.StringVar(value='credit')

            card_frame = tk.Frame(payment_window)
            card_frame.pack(pady=10)

            tk.Radiobutton(card_frame, text="بطاقة ائتمان", variable=card_type,
                          value='credit').pack(side=tk.LEFT, padx=10)
            tk.Radiobutton(card_frame, text="بطاقة خصم", variable=card_type,
                          value='debit').pack(side=tk.LEFT, padx=10)

            # رقم المرجع
            tk.Label(payment_window, text="رقم المرجع (اختياري):", font=FONTS['normal']).pack()
            reference_entry = tk.Entry(payment_window, font=FONTS['normal'])
            reference_entry.pack(pady=5)

            def process_card_payment():
                try:
                    # محاكاة معالجة البطاقة
                    self.status_label.config(text="جاري معالجة البطاقة...")
                    payment_window.update()

                    # تأخير لمحاكاة المعالجة
                    time.sleep(2)

                    # حفظ الفاتورة
                    payment_method = f"card_{card_type.get()}"
                    reference = reference_entry.get().strip() or None

                    invoice_id = self.save_invoice(payment_method, total_amount, Decimal('0'), reference)

                    if invoice_id:
                        messagebox.showinfo("نجح الدفع", f"تم الدفع بالبطاقة بنجاح!\nرقم الفاتورة: {invoice_id}")

                        # طباعة الفاتورة
                        if self.cashier_settings['auto_print']:
                            self.print_receipt(invoice_id)

                        # بدء فاتورة جديدة
                        self.new_sale()
                        payment_window.destroy()

                    self.status_label.config(text="جاهز للبيع")

                except Exception as e:
                    self.status_label.config(text="خطأ في معالجة البطاقة")
                    messagebox.showerror("خطأ", f"حدث خطأ في معالجة البطاقة:\n{str(e)}")

            # أزرار التحكم
            buttons_frame = tk.Frame(payment_window)
            buttons_frame.pack(pady=20)

            tk.Button(buttons_frame, text="💳 معالجة البطاقة", command=process_card_payment,
                     bg=COLORS['info'], fg='white', font=FONTS['button']).pack(side=tk.LEFT, padx=5)
            tk.Button(buttons_frame, text="إلغاء", command=payment_window.destroy,
                     bg=COLORS['danger'], fg='white').pack(side=tk.LEFT, padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في الدفع بالبطاقة:\n{str(e)}")

    def save_invoice(self, payment_method, paid_amount, change_amount, reference=None):
        """حفظ الفاتورة في قاعدة البيانات"""
        try:
            # إنشاء رقم الفاتورة
            invoice_number = self.generate_invoice_number()

            # بيانات الفاتورة
            invoice_data = {
                'invoice_number': invoice_number,
                'invoice_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'customer_id': self.current_sale.get('customer_id'),
                'user_id': self.current_user['id'],
                'subtotal_amount': float(self.current_sale['subtotal']),
                'discount_amount': float(self.current_sale['discount']),
                'tax_amount': float(self.current_sale['tax']),
                'total_amount': float(self.current_sale['total']),
                'paid_amount': float(paid_amount),
                'remaining_amount': 0.0,  # مدفوعة بالكامل
                'payment_method': payment_method,
                'payment_status': 'paid',
                'notes': f"دفع عبر نظام الكاشير - مرجع: {reference}" if reference else "دفع عبر نظام الكاشير"
            }

            # إدراج الفاتورة
            invoice_query = """
                INSERT INTO sales_invoices (
                    invoice_number, invoice_date, customer_id, user_id,
                    subtotal_amount, discount_amount, tax_amount, total_amount,
                    paid_amount, remaining_amount, payment_method, payment_status, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            invoice_values = (
                invoice_data['invoice_number'], invoice_data['invoice_date'],
                invoice_data['customer_id'], invoice_data['user_id'],
                invoice_data['subtotal_amount'], invoice_data['discount_amount'],
                invoice_data['tax_amount'], invoice_data['total_amount'],
                invoice_data['paid_amount'], invoice_data['remaining_amount'],
                invoice_data['payment_method'], invoice_data['payment_status'],
                invoice_data['notes']
            )

            self.db_manager.execute_query(invoice_query, invoice_values)

            # الحصول على ID الفاتورة
            invoice_id = self.db_manager.get_last_insert_id()

            # إدراج تفاصيل الفاتورة
            for item in self.current_sale['items']:
                item_query = """
                    INSERT INTO sales_invoice_items (
                        invoice_id, product_id, quantity, unit_price, total_price
                    ) VALUES (?, ?, ?, ?, ?)
                """

                self.db_manager.execute_query(item_query, (
                    invoice_id, item['product_id'], item['quantity'],
                    float(item['price']), float(item['total'])
                ))

                # تحديث المخزون
                update_stock_query = """
                    UPDATE products
                    SET stock_quantity = stock_quantity - ?
                    WHERE id = ?
                """
                self.db_manager.execute_query(update_stock_query, (item['quantity'], item['product_id']))

            # تسجيل النشاط
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "بيع - نظام الكاشير",
                f"فاتورة رقم {invoice_number} - المبلغ: {self.current_sale['total']:.2f}"
            )

            return invoice_id

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الفاتورة:\n{str(e)}")
            return None

    def generate_invoice_number(self):
        """إنشاء رقم فاتورة جديد"""
        try:
            # الحصول على آخر رقم فاتورة
            query = """
                SELECT invoice_number FROM sales_invoices
                WHERE invoice_number LIKE 'POS-%'
                ORDER BY id DESC LIMIT 1
            """
            result = self.db_manager.execute_query(query)

            if result:
                last_number = result[0]['invoice_number']
                # استخراج الرقم
                number_part = int(last_number.split('-')[1]) + 1
            else:
                number_part = 1

            return f"POS-{number_part:06d}"

        except Exception as e:
            # في حالة الخطأ، استخدام الوقت الحالي
            return f"POS-{int(time.time())}"

    def print_receipt(self, invoice_id=None):
        """طباعة الفاتورة"""
        try:
            if not invoice_id and not self.current_sale['items']:
                messagebox.showwarning("تحذير", "لا توجد فاتورة للطباعة")
                return

            # إنشاء نافذة معاينة الفاتورة
            preview_window = tk.Toplevel(self.cashier_window)
            preview_window.title("🧾 معاينة الفاتورة")
            preview_window.geometry("400x600")
            preview_window.transient(self.cashier_window)

            # إنشاء محتوى الفاتورة
            receipt_text = self.generate_receipt_text(invoice_id)

            # عرض النص
            text_widget = tk.Text(preview_window, font=('Courier', 10), wrap=tk.WORD)
            text_widget.pack(fill='both', expand=True, padx=10, pady=10)
            text_widget.insert('1.0', receipt_text)
            text_widget.config(state='disabled')

            # أزرار التحكم
            buttons_frame = tk.Frame(preview_window)
            buttons_frame.pack(pady=10)

            tk.Button(buttons_frame, text="🖨️ طباعة",
                     command=lambda: self.send_to_printer(receipt_text),
                     bg=COLORS['primary'], fg='white').pack(side=tk.LEFT, padx=5)
            tk.Button(buttons_frame, text="💾 حفظ",
                     command=lambda: self.save_receipt_file(receipt_text),
                     bg=COLORS['info'], fg='white').pack(side=tk.LEFT, padx=5)
            tk.Button(buttons_frame, text="إغلاق", command=preview_window.destroy,
                     bg=COLORS['secondary'], fg='white').pack(side=tk.LEFT, padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في طباعة الفاتورة:\n{str(e)}")

    def generate_receipt_text(self, invoice_id=None):
        """إنشاء نص الفاتورة"""
        try:
            receipt = []
            receipt.append("=" * 40)
            receipt.append("        فاتورة مبيعات")
            receipt.append("=" * 40)
            receipt.append("")

            # معلومات الشركة
            receipt.append("شركة المبيعات والمخازن")
            receipt.append("العنوان: المملكة العربية السعودية")
            receipt.append("الهاتف: +966-XX-XXX-XXXX")
            receipt.append("")
            receipt.append("-" * 40)

            # معلومات الفاتورة
            if invoice_id:
                # تحميل بيانات الفاتورة من قاعدة البيانات
                query = """
                    SELECT * FROM sales_invoices WHERE id = ?
                """
                invoice_data = self.db_manager.execute_query(query, (invoice_id,))[0]
                receipt.append(f"رقم الفاتورة: {invoice_data['invoice_number']}")
                receipt.append(f"التاريخ: {invoice_data['invoice_date']}")
            else:
                receipt.append(f"رقم الفاتورة: مؤقت")
                receipt.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            receipt.append(f"الكاشير: {self.current_user.get('full_name', 'غير محدد')}")
            receipt.append("")
            receipt.append("-" * 40)
            receipt.append("المنتجات:")
            receipt.append("-" * 40)

            # المنتجات
            for item in self.current_sale['items']:
                receipt.append(f"{item['name']}")
                receipt.append(f"  {item['quantity']} × {item['price']:.2f} = {item['total']:.2f}")
                receipt.append("")

            receipt.append("-" * 40)
            receipt.append(f"الإجمالي الفرعي:    {self.current_sale['subtotal']:.2f}")
            receipt.append(f"الخصم:             {self.current_sale['discount']:.2f}")
            receipt.append(f"الضريبة:           {self.current_sale['tax']:.2f}")
            receipt.append("=" * 40)
            receipt.append(f"الإجمالي النهائي:   {self.current_sale['total']:.2f}")
            receipt.append("=" * 40)
            receipt.append("")
            receipt.append("شكراً لتسوقكم معنا")
            receipt.append("نتطلع لخدمتكم مرة أخرى")
            receipt.append("")
            receipt.append("=" * 40)

            return "\n".join(receipt)

        except Exception as e:
            return f"خطأ في إنشاء الفاتورة: {str(e)}"

    def send_to_printer(self, receipt_text):
        """إرسال الفاتورة للطابعة"""
        try:
            # محاولة الطباعة (يمكن تطويرها حسب نوع الطابعة)
            import tempfile
            import os

            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(receipt_text)
                temp_file = f.name

            # محاولة الطباعة باستخدام الطابعة الافتراضية
            if os.name == 'nt':  # Windows
                os.startfile(temp_file, "print")
            else:  # Linux/Mac
                os.system(f"lpr {temp_file}")

            messagebox.showinfo("طباعة", "تم إرسال الفاتورة للطابعة")

        except Exception as e:
            messagebox.showerror("خطأ في الطباعة", f"حدث خطأ في الطباعة:\n{str(e)}")

    def save_receipt_file(self, receipt_text):
        """حفظ الفاتورة كملف"""
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                title="حفظ الفاتورة",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(receipt_text)
                messagebox.showinfo("حفظ", f"تم حفظ الفاتورة في:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"حدث خطأ في حفظ الفاتورة:\n{str(e)}")

    def new_sale(self):
        """بدء فاتورة جديدة"""
        self.current_sale = {
            'items': [],
            'subtotal': Decimal('0.00'),
            'tax': Decimal('0.00'),
            'discount': Decimal('0.00'),
            'total': Decimal('0.00'),
            'payment_method': 'cash',
            'customer_id': None
        }

        self.update_items_display()
        self.calculate_totals()
        self.barcode_entry.focus_set()
        self.status_label.config(text="فاتورة جديدة - جاهز للبيع")

    def show_sales_report(self):
        """عرض تقرير المبيعات اليومية"""
        try:
            report_window = tk.Toplevel(self.cashier_window)
            report_window.title("📊 تقرير المبيعات اليومية")
            report_window.geometry("600x400")
            report_window.transient(self.cashier_window)

            # تحميل بيانات المبيعات اليومية
            today = datetime.now().strftime('%Y-%m-%d')

            query = """
                SELECT
                    COUNT(*) as total_invoices,
                    SUM(total_amount) as total_sales,
                    SUM(tax_amount) as total_tax,
                    SUM(discount_amount) as total_discount,
                    AVG(total_amount) as avg_sale
                FROM sales_invoices
                WHERE DATE(invoice_date) = ? AND payment_status = 'paid'
            """

            result = self.db_manager.execute_query(query, (today,))
            stats = result[0] if result else {}

            # عرض الإحصائيات
            stats_frame = tk.LabelFrame(report_window, text="إحصائيات اليوم", font=FONTS['header'])
            stats_frame.pack(fill='x', padx=10, pady=10)

            tk.Label(stats_frame, text=f"عدد الفواتير: {stats.get('total_invoices', 0)}",
                    font=FONTS['normal']).pack(anchor='w', padx=10, pady=2)
            tk.Label(stats_frame, text=f"إجمالي المبيعات: {stats.get('total_sales', 0):.2f} ريال",
                    font=FONTS['normal']).pack(anchor='w', padx=10, pady=2)
            tk.Label(stats_frame, text=f"إجمالي الضرائب: {stats.get('total_tax', 0):.2f} ريال",
                    font=FONTS['normal']).pack(anchor='w', padx=10, pady=2)
            tk.Label(stats_frame, text=f"إجمالي الخصومات: {stats.get('total_discount', 0):.2f} ريال",
                    font=FONTS['normal']).pack(anchor='w', padx=10, pady=2)
            tk.Label(stats_frame, text=f"متوسط الفاتورة: {stats.get('avg_sale', 0):.2f} ريال",
                    font=FONTS['normal']).pack(anchor='w', padx=10, pady=2)

            # قائمة الفواتير
            invoices_frame = tk.LabelFrame(report_window, text="فواتير اليوم", font=FONTS['header'])
            invoices_frame.pack(fill='both', expand=True, padx=10, pady=10)

            columns = ('الرقم', 'الوقت', 'المبلغ', 'طريقة الدفع')
            invoices_tree = ttk.Treeview(invoices_frame, columns=columns, show='headings', height=10)

            for col in columns:
                invoices_tree.heading(col, text=col)
                invoices_tree.column(col, width=120)

            # تحميل الفواتير
            invoices_query = """
                SELECT invoice_number, invoice_date, total_amount, payment_method
                FROM sales_invoices
                WHERE DATE(invoice_date) = ? AND payment_status = 'paid'
                ORDER BY invoice_date DESC
            """

            invoices = self.db_manager.execute_query(invoices_query, (today,))

            for invoice in invoices:
                time_part = invoice['invoice_date'].split(' ')[1][:5]  # HH:MM
                payment_text = {
                    'cash': 'نقدي',
                    'card_credit': 'بطاقة ائتمان',
                    'card_debit': 'بطاقة خصم',
                    'wallet': 'محفظة إلكترونية'
                }.get(invoice['payment_method'], invoice['payment_method'])

                invoices_tree.insert('', 'end', values=(
                    invoice['invoice_number'],
                    time_part,
                    f"{invoice['total_amount']:.2f}",
                    payment_text
                ))

            invoices_tree.pack(fill='both', expand=True, padx=5, pady=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في عرض التقرير:\n{str(e)}")

    def manage_cash_drawer(self):
        """إدارة الدرج النقدي"""
        try:
            drawer_window = tk.Toplevel(self.cashier_window)
            drawer_window.title("💰 إدارة الدرج النقدي")
            drawer_window.geometry("400x300")
            drawer_window.transient(self.cashier_window)
            drawer_window.grab_set()

            # الرصيد الحالي
            current_balance = self.get_cash_drawer_balance()

            tk.Label(drawer_window, text=f"الرصيد الحالي: {current_balance:.2f} ريال",
                    font=FONTS['large'], fg=COLORS['primary']).pack(pady=20)

            # العمليات
            operations_frame = tk.LabelFrame(drawer_window, text="العمليات", font=FONTS['header'])
            operations_frame.pack(fill='x', padx=10, pady=10)

            def add_cash():
                amount = simpledialog.askfloat("إضافة نقد", "أدخل المبلغ المراد إضافته:", minvalue=0)
                if amount:
                    self.record_cash_transaction('add', amount, "إضافة نقد يدوية")
                    drawer_window.destroy()
                    self.manage_cash_drawer()

            def remove_cash():
                amount = simpledialog.askfloat("سحب نقد", "أدخل المبلغ المراد سحبه:", minvalue=0, maxvalue=current_balance)
                if amount:
                    self.record_cash_transaction('remove', amount, "سحب نقد يدوي")
                    drawer_window.destroy()
                    self.manage_cash_drawer()

            def count_cash():
                amount = simpledialog.askfloat("جرد النقد", "أدخل المبلغ الفعلي في الدرج:", minvalue=0)
                if amount is not None:
                    difference = amount - current_balance
                    if difference != 0:
                        action = "add" if difference > 0 else "remove"
                        self.record_cash_transaction(action, abs(difference), f"تسوية جرد - الفرق: {difference:.2f}")
                    messagebox.showinfo("جرد النقد", f"تم تسجيل الجرد\nالفرق: {difference:.2f} ريال")
                    drawer_window.destroy()
                    self.manage_cash_drawer()

            tk.Button(operations_frame, text="➕ إضافة نقد", command=add_cash,
                     bg=COLORS['success'], fg='white').pack(fill='x', padx=5, pady=2)
            tk.Button(operations_frame, text="➖ سحب نقد", command=remove_cash,
                     bg=COLORS['warning'], fg='white').pack(fill='x', padx=5, pady=2)
            tk.Button(operations_frame, text="📊 جرد النقد", command=count_cash,
                     bg=COLORS['info'], fg='white').pack(fill='x', padx=5, pady=2)

            # سجل العمليات
            history_frame = tk.LabelFrame(drawer_window, text="آخر العمليات", font=FONTS['header'])
            history_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # عرض آخر 5 عمليات
            history_query = """
                SELECT transaction_type, amount, description, created_at
                FROM cash_drawer_transactions
                WHERE user_id = ?
                ORDER BY created_at DESC LIMIT 5
            """

            try:
                transactions = self.db_manager.execute_query(history_query, (self.current_user['id'],))

                for trans in transactions:
                    trans_text = f"{trans['transaction_type']} - {trans['amount']:.2f} - {trans['description']}"
                    tk.Label(history_frame, text=trans_text, font=FONTS['small']).pack(anchor='w', padx=5)

            except:
                tk.Label(history_frame, text="لا توجد عمليات سابقة", font=FONTS['small']).pack(padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إدارة الدرج النقدي:\n{str(e)}")

    def get_cash_drawer_balance(self):
        """الحصول على رصيد الدرج النقدي"""
        try:
            # حساب الرصيد من المعاملات
            query = """
                SELECT
                    SUM(CASE WHEN transaction_type = 'add' THEN amount ELSE -amount END) as balance
                FROM cash_drawer_transactions
                WHERE user_id = ?
            """

            result = self.db_manager.execute_query(query, (self.current_user['id'],))
            return Decimal(str(result[0]['balance'] or 0))

        except:
            return Decimal('0.00')

    def record_cash_transaction(self, transaction_type, amount, description):
        """تسجيل معاملة نقدية"""
        try:
            query = """
                INSERT INTO cash_drawer_transactions (
                    user_id, transaction_type, amount, description, created_at
                ) VALUES (?, ?, ?, ?, ?)
            """

            self.db_manager.execute_query(query, (
                self.current_user['id'],
                transaction_type,
                float(amount),
                description,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))

        except Exception as e:
            print(f"خطأ في تسجيل المعاملة النقدية: {str(e)}")

    def show_help(self):
        """عرض المساعدة"""
        help_window = tk.Toplevel(self.cashier_window)
        help_window.title("❓ مساعدة نظام الكاشير")
        help_window.geometry("500x400")
        help_window.transient(self.cashier_window)

        help_text = """
🏪 نظام الكاشير المتطور - دليل الاستخدام

⌨️ اختصارات لوحة المفاتيح:
• F1 - عرض المساعدة
• F2 - دفع نقدي سريع
• F3 - دفع بالبطاقة
• F4 - طباعة الفاتورة
• F5 - قائمة المنتجات
• F9 - إدارة الدرج النقدي
• Escape - إغلاق النظام
• Ctrl+N - فاتورة جديدة
• Ctrl+D - إضافة خصم

🔍 البحث والإضافة:
• اكتب الباركود أو اسم المنتج واضغط Enter
• انقر مزدوج على منتج في القائمة لإضافته
• استخدم زر "قائمة المنتجات" للتصفح

✏️ التعديل والحذف:
• انقر مزدوج على منتج في الفاتورة للتعديل
• اضغط Delete لحذف منتج محدد
• استخدم "مسح الكل" لبدء فاتورة جديدة

💰 الدفع:
• الدفع النقدي: F2 أو زر "دفع نقدي سريع"
• الدفع بالبطاقة: F3 أو زر "دفع بالبطاقة"
• يمكن إضافة خصم قبل الدفع

🧾 الطباعة:
• الطباعة التلقائية بعد الدفع (إذا كانت مفعلة)
• يمكن طباعة الفاتورة يدوياً بـ F4
• حفظ الفاتورة كملف نصي

📊 التقارير:
• تقرير المبيعات اليومية
• إحصائيات الكاشير
• سجل العمليات النقدية
        """

        text_widget = tk.Text(help_window, font=FONTS['small'], wrap=tk.WORD)
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        text_widget.insert('1.0', help_text)
        text_widget.config(state='disabled')

        tk.Button(help_window, text="إغلاق", command=help_window.destroy,
                 bg=COLORS['primary'], fg='white').pack(pady=10)

    def close_cashier(self):
        """إغلاق نظام الكاشير"""
        if self.current_sale['items']:
            if not messagebox.askyesno("تأكيد الإغلاق",
                                     "توجد منتجات في الفاتورة الحالية.\nهل تريد الإغلاق بدون حفظ؟"):
                return

        # حفظ الإعدادات
        self.save_cashier_settings()

        # تسجيل إغلاق النظام
        log_user_activity(
            self.db_manager,
            self.current_user['id'],
            "إغلاق نظام الكاشير",
            f"تم إغلاق نظام الكاشير في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

        # إغلاق النافذة
        self.cashier_window.destroy()

# دالة لإنشاء جدول معاملات الدرج النقدي إذا لم يكن موجوداً
def create_cash_drawer_table(db_manager):
    """إنشاء جدول معاملات الدرج النقدي"""
    try:
        query = """
            CREATE TABLE IF NOT EXISTS cash_drawer_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                created_at TEXT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """
        db_manager.execute_query(query)

    except Exception as e:
        print(f"خطأ في إنشاء جدول معاملات الدرج النقدي: {str(e)}")

# دالة لإنشاء جدول تفاصيل الفواتير إذا لم يكن موجوداً
def create_invoice_items_table(db_manager):
    """إنشاء جدول تفاصيل الفواتير"""
    try:
        query = """
            CREATE TABLE IF NOT EXISTS sales_invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """
        db_manager.execute_query(query)

    except Exception as e:
        print(f"خطأ في إنشاء جدول تفاصيل الفواتير: {str(e)}")

# دالة مساعدة لتهيئة جداول نظام الكاشير
def initialize_cashier_tables():
    """تهيئة جداول نظام الكاشير"""
    try:
        db_manager = DatabaseManager()
        create_cash_drawer_table(db_manager)
        create_invoice_items_table(db_manager)
        print("✅ تم تهيئة جداول نظام الكاشير بنجاح")

    except Exception as e:
        print(f"❌ خطأ في تهيئة جداول نظام الكاشير: {str(e)}")

if __name__ == "__main__":
    # اختبار النظام
    initialize_cashier_tables()
    print("🏪 نظام الكاشير جاهز للاستخدام")
