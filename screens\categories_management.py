# -*- coding: utf-8 -*-
"""
شاشة إدارة فئات المنتجات
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from config.settings import COLORS, FONTS
from utils.database_manager import DatabaseManager
from utils.helpers import check_user_permission, show_permission_error, log_user_activity
from utils.arabic_support import ArabicSupport

class CategoriesManagement:
    """كلاس إدارة فئات المنتجات"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'products_management'):
            show_permission_error('إدارة فئات المنتجات')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_categories()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة فئات المنتجات")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة فئات المنتجات",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار البحث
        search_frame = tk.LabelFrame(self.window, text="البحث والفلترة", 
                                    font=FONTS['heading'], bg=COLORS['background'])
        search_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # صف البحث
        search_row = tk.Frame(search_frame, bg=COLORS['background'])
        search_row.pack(fill='x', padx=10, pady=5)
        
        tk.Label(search_row, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_row, textvariable=self.search_var, 
                               font=FONTS['normal'], width=30)
        search_entry.pack(side=tk.RIGHT, padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # فلتر الحالة
        tk.Label(search_row, text="الحالة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.status_var = tk.StringVar()
        status_combo = ttk.Combobox(search_row, textvariable=self.status_var,
                                   font=FONTS['normal'], width=15, state='readonly')
        status_combo['values'] = ['الكل', 'نشط', 'غير نشط']
        status_combo.set('الكل')
        status_combo.pack(side=tk.RIGHT, padx=5)
        status_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # زر البحث
        tk.Button(search_row, text="بحث", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', 
                 command=self.load_categories).pack(side=tk.RIGHT, padx=10)
        
        # زر مسح الفلاتر
        tk.Button(search_row, text="مسح الفلاتر", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', 
                 command=self.clear_filters).pack(side=tk.RIGHT, padx=5)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['heading'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء عناصر الإحصائيات
        self.create_statistics_widgets(stats_frame)
        
        # إطار قائمة الفئات
        categories_frame = tk.LabelFrame(self.window, text="قائمة الفئات", 
                                       font=FONTS['heading'], bg=COLORS['background'])
        categories_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء جدول الفئات
        self.create_categories_table(categories_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # أزرار الإجراءات
        tk.Button(buttons_frame, text="فئة جديدة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.new_category).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="عرض التفاصيل", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.view_category).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تعديل", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=15,
                 command=self.edit_category).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تغيير الحالة", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', width=15,
                 command=self.toggle_status).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="حذف", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.delete_category).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=15,
                 command=self.load_categories).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def create_statistics_widgets(self, parent):
        """إنشاء عناصر الإحصائيات"""
        stats_container = tk.Frame(parent, bg=COLORS['background'])
        stats_container.pack(fill='x', padx=10, pady=10)
        
        self.stats_labels = {}
        stats_info = [
            ('total_categories', 'إجمالي الفئات', COLORS['primary']),
            ('active_categories', 'الفئات النشطة', COLORS['success']),
            ('inactive_categories', 'الفئات غير النشطة', COLORS['danger']),
            ('products_count', 'إجمالي المنتجات', COLORS['info'])
        ]
        
        for i, (key, label, color) in enumerate(stats_info):
            stat_frame = tk.Frame(stats_container, bg=COLORS['background'], 
                                 relief='raised', bd=1)
            stat_frame.pack(side=tk.RIGHT, padx=10, pady=5, fill='x', expand=True)
            
            tk.Label(stat_frame, text=label, font=FONTS['small'], 
                    bg=COLORS['background'], fg=COLORS['text']).pack(pady=(5, 0))
            
            self.stats_labels[key] = tk.Label(stat_frame, text="0", font=FONTS['heading'], 
                                            bg=COLORS['background'], fg=color)
            self.stats_labels[key].pack(pady=(0, 5))
            
    def create_categories_table(self, parent):
        """إنشاء جدول الفئات"""
        table_frame = tk.Frame(parent, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تحديد الأعمدة
        columns = ('الرقم', 'اسم الفئة', 'الوصف', 'عدد المنتجات', 'الحالة', 'تاريخ الإنشاء', 'المستخدم')
        
        self.categories_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة وعرضها
        column_widths = {
            'الرقم': 80,
            'اسم الفئة': 200,
            'الوصف': 250,
            'عدد المنتجات': 120,
            'الحالة': 100,
            'تاريخ الإنشاء': 120,
            'المستخدم': 150
        }
        
        for col in columns:
            self.categories_tree.heading(col, text=col)
            self.categories_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.categories_tree.xview)
        self.categories_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.categories_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.categories_tree.bind('<Double-1>', self.on_category_double_click)
        
    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_var.set('')
        self.status_var.set('الكل')
        self.load_categories()
        
    def on_search_change(self, event=None):
        """معالج تغيير البحث"""
        # تأخير البحث لتجنب البحث مع كل حرف
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        self.search_timer = self.window.after(500, self.load_categories)
        
    def on_filter_change(self, event=None):
        """معالج تغيير الفلتر"""
        self.load_categories()

    def load_categories(self):
        """تحميل قائمة الفئات"""
        try:
            # مسح البيانات الحالية
            for item in self.categories_tree.get_children():
                self.categories_tree.delete(item)

            # بناء الاستعلام
            query = """
                SELECT c.id, c.name, c.description, c.is_active, c.created_at,
                       u.name as user_name,
                       COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN users u ON c.user_id = u.id
                LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
                WHERE 1=1
            """

            params = []

            # فلتر البحث
            search_term = self.search_var.get().strip()
            if search_term:
                query += " AND (c.name LIKE ? OR c.description LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern])

            # فلتر الحالة
            status_filter = self.status_var.get()
            if status_filter and status_filter != 'الكل':
                if status_filter == 'نشط':
                    query += " AND c.is_active = 1"
                elif status_filter == 'غير نشط':
                    query += " AND c.is_active = 0"

            query += " GROUP BY c.id, c.name, c.description, c.is_active, c.created_at, u.name"
            query += " ORDER BY c.name"

            # تنفيذ الاستعلام
            categories = self.db_manager.execute_query(query, params)

            # عرض البيانات
            total_categories = 0
            active_categories = 0
            inactive_categories = 0
            total_products = 0

            for category in categories:
                # تحديد لون الصف حسب الحالة
                tags = []
                if category['is_active']:
                    tags = ['active']
                    active_categories += 1
                else:
                    tags = ['inactive']
                    inactive_categories += 1

                status_text = 'نشط' if category['is_active'] else 'غير نشط'

                self.categories_tree.insert('', 'end', values=(
                    category['id'],
                    category['name'],
                    category['description'] or 'لا يوجد وصف',
                    category['products_count'],
                    status_text,
                    category['created_at'][:10] if category['created_at'] else '',
                    category['user_name'] or ''
                ), tags=tags)

                # تحديث الإحصائيات
                total_categories += 1
                total_products += category['products_count']

            # تكوين ألوان الصفوف
            self.categories_tree.tag_configure('active', background='#d4edda')
            self.categories_tree.tag_configure('inactive', background='#f8d7da')

            # تحديث الإحصائيات
            self.update_statistics(total_categories, active_categories, inactive_categories, total_products)

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "عرض قائمة فئات المنتجات",
                f"تم عرض {total_categories} فئة",
                "categories"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفئات:\n{str(e)}")

    def update_statistics(self, total_categories, active_categories, inactive_categories, total_products):
        """تحديث الإحصائيات"""
        self.stats_labels['total_categories'].config(text=str(total_categories))
        self.stats_labels['active_categories'].config(text=str(active_categories))
        self.stats_labels['inactive_categories'].config(text=str(inactive_categories))
        self.stats_labels['products_count'].config(text=str(total_products))

    def get_selected_category_id(self):
        """الحصول على معرف الفئة المحددة"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فئة أولاً")
            return None

        item = self.categories_tree.item(selection[0])
        return item['values'][0]

    def on_category_double_click(self, event):
        """معالج النقر المزدوج على الفئة"""
        self.view_category()

    def new_category(self):
        """إنشاء فئة جديدة"""
        CategoryDialog(self.window, self.current_user, self.load_categories)

    def view_category(self):
        """عرض تفاصيل الفئة"""
        category_id = self.get_selected_category_id()
        if category_id:
            CategoryDialog(self.window, self.current_user, self.load_categories, category_id, mode='view')

    def edit_category(self):
        """تعديل الفئة"""
        category_id = self.get_selected_category_id()
        if category_id:
            CategoryDialog(self.window, self.current_user, self.load_categories, category_id, mode='edit')

    def toggle_status(self):
        """تغيير حالة الفئة"""
        category_id = self.get_selected_category_id()
        if not category_id:
            return

        try:
            # جلب الحالة الحالية
            query = "SELECT name, is_active FROM categories WHERE id = ?"
            result = self.db_manager.execute_query(query, [category_id])

            if result:
                category = result[0]
                current_status = category['is_active']
                new_status = not current_status
                status_text = 'تفعيل' if new_status else 'إلغاء تفعيل'

                # تأكيد التغيير
                if messagebox.askyesno("تأكيد", f"هل تريد {status_text} فئة '{category['name']}'؟"):
                    # تحديث الحالة
                    update_query = "UPDATE categories SET is_active = ? WHERE id = ?"
                    self.db_manager.execute_query(update_query, [new_status, category_id])

                    # تسجيل العملية
                    log_user_activity(
                        self.db_manager,
                        self.current_user['id'],
                        f"{status_text} فئة منتجات",
                        f"الفئة: {category['name']}",
                        "categories",
                        category_id
                    )

                    messagebox.showinfo("نجح", f"تم {status_text} الفئة بنجاح")
                    self.load_categories()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تغيير حالة الفئة:\n{str(e)}")

    def delete_category(self):
        """حذف الفئة"""
        category_id = self.get_selected_category_id()
        if not category_id:
            return

        try:
            # جلب معلومات الفئة
            query = "SELECT name FROM categories WHERE id = ?"
            result = self.db_manager.execute_query(query, [category_id])

            if result:
                category_name = result[0]['name']

                # التحقق من وجود منتجات مرتبطة
                products_query = "SELECT COUNT(*) as count FROM products WHERE category_id = ?"
                products_result = self.db_manager.execute_query(products_query, [category_id])
                products_count = products_result[0]['count'] if products_result else 0

                if products_count > 0:
                    messagebox.showwarning("تحذير", f"لا يمكن حذف الفئة '{category_name}' لأنها تحتوي على {products_count} منتج.\nيرجى حذف أو نقل المنتجات أولاً.")
                    return

                # تأكيد الحذف
                if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف فئة '{category_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
                    # حذف الفئة
                    self.db_manager.execute_query("DELETE FROM categories WHERE id = ?", [category_id])

                    # تسجيل العملية
                    log_user_activity(
                        self.db_manager,
                        self.current_user['id'],
                        "حذف فئة منتجات",
                        f"الفئة: {category_name}",
                        "categories",
                        category_id
                    )

                    messagebox.showinfo("نجح", "تم حذف الفئة بنجاح")
                    self.load_categories()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حذف الفئة:\n{str(e)}")

class CategoryDialog:
    """حوار إدارة الفئة"""

    def __init__(self, parent, current_user, callback=None, category_id=None, mode='new'):
        self.parent = parent
        self.current_user = current_user
        self.callback = callback
        self.category_id = category_id
        self.mode = mode  # 'new', 'edit', 'view'
        self.db_manager = DatabaseManager()

        self.setup_window()
        self.create_widgets()

        if self.category_id:
            self.load_category_data()

    def setup_window(self):
        """إعداد النافذة"""
        titles = {
            'new': 'فئة جديدة',
            'edit': 'تعديل الفئة',
            'view': 'عرض الفئة'
        }

        self.window = tk.Toplevel(self.parent)
        self.window.title(titles.get(self.mode, 'إدارة الفئة'))
        self.window.geometry("500x400")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 500
        height = 400
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        titles = {
            'new': 'فئة جديدة',
            'edit': 'تعديل الفئة',
            'view': 'عرض الفئة'
        }

        title_label = tk.Label(self.window, text=titles.get(self.mode, 'إدارة الفئة'),
                              font=FONTS['title'], bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)

        # إطار البيانات
        data_frame = tk.LabelFrame(self.window, text="بيانات الفئة",
                                  font=FONTS['heading'], bg=COLORS['background'])
        data_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # اسم الفئة
        row1 = tk.Frame(data_frame, bg=COLORS['background'])
        row1.pack(fill='x', padx=10, pady=10)

        tk.Label(row1, text="اسم الفئة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.name_var = tk.StringVar()
        name_entry = tk.Entry(row1, textvariable=self.name_var, font=FONTS['normal'],
                             width=30, state='readonly' if self.mode == 'view' else 'normal')
        name_entry.pack(side=tk.RIGHT, padx=5)

        # الوصف
        row2 = tk.Frame(data_frame, bg=COLORS['background'])
        row2.pack(fill='x', padx=10, pady=5)

        tk.Label(row2, text="الوصف:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.TOP, anchor='e', padx=5)

        self.description_text = tk.Text(row2, font=FONTS['normal'], width=40, height=5,
                                       state='disabled' if self.mode == 'view' else 'normal')
        self.description_text.pack(side=tk.TOP, padx=5, pady=5)

        # الحالة (للعرض والتعديل فقط)
        if self.mode in ['edit', 'view']:
            row3 = tk.Frame(data_frame, bg=COLORS['background'])
            row3.pack(fill='x', padx=10, pady=5)

            tk.Label(row3, text="الحالة:", font=FONTS['normal'],
                    bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

            self.is_active_var = tk.BooleanVar(value=True)
            status_check = tk.Checkbutton(row3, text="نشط", variable=self.is_active_var,
                                         font=FONTS['normal'], bg=COLORS['background'],
                                         state='disabled' if self.mode == 'view' else 'normal')
            status_check.pack(side=tk.RIGHT, padx=5)

        # معلومات إضافية للعرض
        if self.mode == 'view':
            info_frame = tk.LabelFrame(data_frame, text="معلومات إضافية",
                                      font=FONTS['heading'], bg=COLORS['background'])
            info_frame.pack(fill='x', padx=10, pady=10)

            self.info_labels = {}
            info_items = [
                ('created_at', 'تاريخ الإنشاء'),
                ('user_name', 'المستخدم'),
                ('products_count', 'عدد المنتجات')
            ]

            for key, label in info_items:
                row = tk.Frame(info_frame, bg=COLORS['background'])
                row.pack(fill='x', padx=5, pady=2)

                tk.Label(row, text=f"{label}:", font=FONTS['normal'],
                        bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

                self.info_labels[key] = tk.Label(row, text="", font=FONTS['normal'],
                                               bg=COLORS['background'], fg=COLORS['primary'])
                self.info_labels[key].pack(side=tk.RIGHT, padx=5)

        # أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)

        if self.mode != 'view':
            tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                     bg=COLORS['success'], fg='white', width=15,
                     command=self.save_category).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def load_category_data(self):
        """تحميل بيانات الفئة"""
        try:
            query = """
                SELECT c.*, u.name as user_name,
                       COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN users u ON c.user_id = u.id
                LEFT JOIN products p ON c.id = p.category_id
                WHERE c.id = ?
                GROUP BY c.id
            """

            result = self.db_manager.execute_query(query, [self.category_id])

            if result:
                category = result[0]

                # تعبئة البيانات
                self.name_var.set(category['name'])

                if category['description']:
                    self.description_text.delete('1.0', tk.END)
                    self.description_text.insert('1.0', category['description'])

                if self.mode in ['edit', 'view']:
                    self.is_active_var.set(bool(category['is_active']))

                if self.mode == 'view':
                    self.info_labels['created_at'].config(text=category['created_at'][:10] if category['created_at'] else 'غير محدد')
                    self.info_labels['user_name'].config(text=category['user_name'] or 'غير محدد')
                    self.info_labels['products_count'].config(text=str(category['products_count']))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات الفئة:\n{str(e)}")

    def save_category(self):
        """حفظ الفئة"""
        try:
            # التحقق من البيانات
            name = self.name_var.get().strip()
            if not name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم الفئة")
                return

            description = self.description_text.get('1.0', tk.END).strip()

            if self.mode == 'new':
                # التحقق من عدم تكرار الاسم
                check_query = "SELECT id FROM categories WHERE name = ?"
                existing = self.db_manager.execute_query(check_query, [name])

                if existing:
                    messagebox.showwarning("تحذير", "اسم الفئة موجود مسبقاً")
                    return

                # إدراج فئة جديدة
                insert_query = """
                    INSERT INTO categories (name, description, is_active, user_id, created_at)
                    VALUES (?, ?, ?, ?, ?)
                """

                params = [
                    name,
                    description if description else None,
                    1,  # نشط افتراضياً
                    self.current_user['id'],
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ]

                self.db_manager.execute_query(insert_query, params)

                # تسجيل العملية
                log_user_activity(
                    self.db_manager,
                    self.current_user['id'],
                    "إنشاء فئة منتجات جديدة",
                    f"الفئة: {name}",
                    "categories"
                )

                messagebox.showinfo("نجح", "تم إنشاء الفئة بنجاح")

            elif self.mode == 'edit':
                # التحقق من عدم تكرار الاسم (باستثناء الفئة الحالية)
                check_query = "SELECT id FROM categories WHERE name = ? AND id != ?"
                existing = self.db_manager.execute_query(check_query, [name, self.category_id])

                if existing:
                    messagebox.showwarning("تحذير", "اسم الفئة موجود مسبقاً")
                    return

                # تحديث الفئة
                update_query = """
                    UPDATE categories
                    SET name = ?, description = ?, is_active = ?
                    WHERE id = ?
                """

                params = [
                    name,
                    description if description else None,
                    self.is_active_var.get(),
                    self.category_id
                ]

                self.db_manager.execute_query(update_query, params)

                # تسجيل العملية
                log_user_activity(
                    self.db_manager,
                    self.current_user['id'],
                    "تعديل فئة منتجات",
                    f"الفئة: {name}",
                    "categories",
                    self.category_id
                )

                messagebox.showinfo("نجح", "تم تحديث الفئة بنجاح")

            if self.callback:
                self.callback()

            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الفئة:\n{str(e)}")
