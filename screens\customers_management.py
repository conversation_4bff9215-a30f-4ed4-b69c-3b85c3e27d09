# -*- coding: utf-8 -*-
"""
شاشة إدارة العملاء
"""

import tkinter as tk
from tkinter import ttk, messagebox
from config.settings import COLORS, FONTS
from utils.database_manager import DatabaseManager
from utils.helpers import validate_email, validate_phone, format_currency
from utils.arabic_support import ArabicSupport

class CustomersManagement:
    """كلاس إدارة العملاء"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        self.setup_window()
        self.create_widgets()
        self.load_customers()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة العملاء")
        self.window.geometry("1100x600")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1100
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة العملاء",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)
        
        # الأزرار
        tk.Button(buttons_frame, text="إضافة عميل جديد", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=self.add_customer_dialog).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تعديل العميل", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', command=self.edit_customer_dialog).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="حذف العميل", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=self.delete_customer).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="كشف حساب", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', command=self.customer_statement).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', command=self.load_customers).pack(side=tk.RIGHT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(pady=5)
        
        tk.Label(search_frame, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=FONTS['normal'], width=30, justify='right')
        search_entry.pack(side=tk.RIGHT, padx=5)
        
        # إطار جدول العملاء
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview للعملاء
        columns = ('ID', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'حد الائتمان', 'الرصيد الحالي', 'الحالة')
        self.customers_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=18)
        
        # تحديد عناوين الأعمدة
        for col in columns:
            self.customers_tree.heading(col, text=col)
            if col == 'ID':
                self.customers_tree.column(col, width=50, anchor='center')
            elif col in ['حد الائتمان', 'الرصيد الحالي']:
                self.customers_tree.column(col, width=100, anchor='center')
            else:
                self.customers_tree.column(col, width=130, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب العناصر
        self.customers_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج بالتعديل
        self.customers_tree.bind('<Double-1>', lambda e: self.edit_customer_dialog())
        
    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)
                
            # جلب العملاء من قاعدة البيانات
            query = """
                SELECT id, name, phone, email, address, credit_limit, 
                       current_balance, is_active, created_at
                FROM customers
                ORDER BY name
            """
            customers = self.db_manager.execute_query(query)
            
            # إضافة العملاء إلى الجدول
            for customer in customers:
                status = "نشط" if customer['is_active'] else "غير نشط"
                
                self.customers_tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'] or '',
                    customer['email'] or '',
                    customer['address'] or '',
                    f"{customer['credit_limit']:.2f}",
                    f"{customer['current_balance']:.2f}",
                    status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل العملاء:\n{str(e)}")
            
    def on_search_change(self, *args):
        """معالج تغيير البحث"""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            self.load_customers()
            return
            
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)
                
            # البحث في العملاء
            query = """
                SELECT id, name, phone, email, address, credit_limit, 
                       current_balance, is_active, created_at
                FROM customers
                WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?
                ORDER BY name
            """
            search_pattern = f"%{search_term}%"
            customers = self.db_manager.execute_query(query, (search_pattern, search_pattern, search_pattern))
            
            # إضافة النتائج إلى الجدول
            for customer in customers:
                status = "نشط" if customer['is_active'] else "غير نشط"
                
                self.customers_tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'],
                    customer['phone'] or '',
                    customer['email'] or '',
                    customer['address'] or '',
                    f"{customer['credit_limit']:.2f}",
                    f"{customer['current_balance']:.2f}",
                    status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في البحث:\n{str(e)}")
            
    def get_selected_customer(self):
        """الحصول على العميل المحدد"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل من القائمة")
            return None
            
        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]
        
        # جلب بيانات العميل الكاملة
        query = "SELECT * FROM customers WHERE id = ?"
        customers = self.db_manager.execute_query(query, (customer_id,))
        
        if customers:
            return dict(customers[0])
        return None
        
    def add_customer_dialog(self):
        """حوار إضافة عميل جديد"""
        self.customer_dialog(mode='add')
        
    def edit_customer_dialog(self):
        """حوار تعديل عميل"""
        customer = self.get_selected_customer()
        if customer:
            self.customer_dialog(mode='edit', customer_data=customer)
            
    def customer_statement(self):
        """عرض كشف حساب العميل"""
        customer = self.get_selected_customer()
        if customer:
            messagebox.showinfo("قريباً", f"سيتم عرض كشف حساب العميل '{customer['name']}' قريباً")
            
    def delete_customer(self):
        """حذف عميل"""
        customer = self.get_selected_customer()
        if not customer:
            return
            
        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", 
                                   f"هل أنت متأكد من حذف العميل '{customer['name']}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه.")
        
        if result:
            try:
                query = "DELETE FROM customers WHERE id = ?"
                self.db_manager.execute_query(query, (customer['id'],))
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.load_customers()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف العميل:\n{str(e)}")

    def customer_dialog(self, mode='add', customer_data=None):
        """حوار إضافة/تعديل عميل"""
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة عميل جديد" if mode == 'add' else "تعديل العميل")
        dialog.geometry("500x600")
        dialog.configure(bg=COLORS['background'])
        dialog.resizable(False, False)
        ArabicSupport.setup_window_rtl(dialog)

        # توسيط الحوار
        dialog.transient(self.window)
        dialog.grab_set()

        # المتغيرات
        name_var = tk.StringVar(value=customer_data['name'] if customer_data else '')
        phone_var = tk.StringVar(value=customer_data['phone'] if customer_data else '')
        email_var = tk.StringVar(value=customer_data['email'] if customer_data else '')
        address_var = tk.StringVar(value=customer_data['address'] if customer_data else '')
        tax_number_var = tk.StringVar(value=customer_data['tax_number'] if customer_data else '')
        credit_limit_var = tk.StringVar(value=str(customer_data['credit_limit']) if customer_data else '0')
        notes_var = tk.StringVar(value=customer_data['notes'] if customer_data else '')
        active_var = tk.BooleanVar(value=bool(customer_data['is_active']) if customer_data else True)

        # إطار المحتوى
        content_frame = tk.Frame(dialog, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم العميل
        tk.Label(content_frame, text="اسم العميل:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        name_entry = tk.Entry(content_frame, textvariable=name_var, font=FONTS['normal'],
                             width=35, justify='right')
        name_entry.pack(pady=(0, 10))
        name_entry.focus()

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        phone_entry = tk.Entry(content_frame, textvariable=phone_var, font=FONTS['normal'],
                              width=35, justify='right')
        phone_entry.pack(pady=(0, 10))

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        email_entry = tk.Entry(content_frame, textvariable=email_var, font=FONTS['normal'],
                              width=35, justify='right')
        email_entry.pack(pady=(0, 10))

        # العنوان
        tk.Label(content_frame, text="العنوان:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        address_entry = tk.Entry(content_frame, textvariable=address_var, font=FONTS['normal'],
                                width=35, justify='right')
        address_entry.pack(pady=(0, 10))

        # الرقم الضريبي
        tk.Label(content_frame, text="الرقم الضريبي:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        tax_entry = tk.Entry(content_frame, textvariable=tax_number_var, font=FONTS['normal'],
                            width=35, justify='right')
        tax_entry.pack(pady=(0, 10))

        # حد الائتمان
        tk.Label(content_frame, text="حد الائتمان:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        credit_entry = tk.Entry(content_frame, textvariable=credit_limit_var, font=FONTS['normal'],
                               width=35, justify='right')
        credit_entry.pack(pady=(0, 10))

        # الملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        notes_entry = tk.Entry(content_frame, textvariable=notes_var, font=FONTS['normal'],
                              width=35, justify='right')
        notes_entry.pack(pady=(0, 15))

        # حالة العميل
        active_check = tk.Checkbutton(content_frame, text="العميل نشط", variable=active_var,
                                    font=FONTS['normal'], bg=COLORS['background'])
        active_check.pack(pady=10)

        # عرض الرصيد الحالي (للتعديل فقط)
        if mode == 'edit' and customer_data:
            balance_frame = tk.LabelFrame(content_frame, text="معلومات الحساب",
                                        font=FONTS['normal'], bg=COLORS['background'])
            balance_frame.pack(fill='x', pady=(0, 15))

            tk.Label(balance_frame, text=f"الرصيد الحالي: {format_currency(customer_data['current_balance'])}",
                    font=FONTS['normal'], bg=COLORS['background'], fg=COLORS['info']).pack(pady=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(content_frame, bg=COLORS['background'])
        buttons_frame.pack(pady=20)

        def save_customer():
            # التحقق من البيانات
            if not name_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                return

            # التحقق من البريد الإلكتروني
            if email_var.get().strip() and not validate_email(email_var.get().strip()):
                messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
                return

            # التحقق من رقم الهاتف
            if phone_var.get().strip() and not validate_phone(phone_var.get().strip()):
                messagebox.showerror("خطأ", "رقم الهاتف غير صحيح")
                return

            # التحقق من حد الائتمان
            try:
                credit_limit = float(credit_limit_var.get() or 0)
                if credit_limit < 0:
                    messagebox.showerror("خطأ", "حد الائتمان لا يمكن أن يكون سالباً")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "حد الائتمان غير صحيح")
                return

            try:
                if mode == 'add':
                    # إضافة عميل جديد
                    query = """
                        INSERT INTO customers (name, phone, email, address, tax_number,
                                             credit_limit, notes, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        name_var.get().strip(),
                        phone_var.get().strip() or None,
                        email_var.get().strip() or None,
                        address_var.get().strip() or None,
                        tax_number_var.get().strip() or None,
                        credit_limit,
                        notes_var.get().strip() or None,
                        1 if active_var.get() else 0
                    )
                else:
                    # تعديل عميل موجود
                    query = """
                        UPDATE customers
                        SET name=?, phone=?, email=?, address=?, tax_number=?,
                            credit_limit=?, notes=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    params = (
                        name_var.get().strip(),
                        phone_var.get().strip() or None,
                        email_var.get().strip() or None,
                        address_var.get().strip() or None,
                        tax_number_var.get().strip() or None,
                        credit_limit,
                        notes_var.get().strip() or None,
                        1 if active_var.get() else 0,
                        customer_data['id']
                    )

                self.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم حفظ العميل بنجاح")
                dialog.destroy()
                self.load_customers()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ العميل:\n{str(e)}")

        tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=save_customer).pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
