# -*- coding: utf-8 -*-
"""
شاشة جرد المخزون
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import check_user_permission, show_permission_error, log_user_activity
from utils.arabic_support import ArabicSupport

class InventoryCount:
    """كلاس جرد المخزون"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'inventory_management'):
            show_permission_error('جرد المخزون')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_products()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("جرد المخزون")
        self.window.geometry("1400x800")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1400
        height = 800
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="جرد المخزون",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار معلومات الجرد
        info_frame = tk.LabelFrame(self.window, text="معلومات الجرد", 
                                  font=FONTS['heading'], bg=COLORS['background'])
        info_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول - تاريخ الجرد ونوع الجرد
        row1 = tk.Frame(info_frame, bg=COLORS['background'])
        row1.pack(fill='x', padx=10, pady=5)
        
        # تاريخ الجرد
        tk.Label(row1, text="تاريخ الجرد:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.count_date_var = tk.StringVar(value=get_current_date())
        tk.Entry(row1, textvariable=self.count_date_var, font=FONTS['normal'], 
                width=12, justify='right').pack(side=tk.RIGHT, padx=5)
        
        # نوع الجرد
        tk.Label(row1, text="نوع الجرد:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.count_type_var = tk.StringVar()
        count_type_combo = ttk.Combobox(row1, textvariable=self.count_type_var,
                                       font=FONTS['normal'], width=15, state='readonly')
        count_type_combo['values'] = ['جرد شامل', 'جرد جزئي', 'جرد دوري']
        count_type_combo.set('جرد شامل')
        count_type_combo.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني - فلاتر البحث
        row2 = tk.Frame(info_frame, bg=COLORS['background'])
        row2.pack(fill='x', padx=10, pady=5)
        
        # البحث
        tk.Label(row2, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(row2, textvariable=self.search_var, 
                               font=FONTS['normal'], width=25)
        search_entry.pack(side=tk.RIGHT, padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # فلتر الفئة
        tk.Label(row2, text="الفئة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(row2, textvariable=self.category_var,
                                          font=FONTS['normal'], width=20, state='readonly')
        self.category_combo.pack(side=tk.RIGHT, padx=5)
        self.category_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # زر البحث
        tk.Button(row2, text="بحث", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', 
                 command=self.load_products).pack(side=tk.RIGHT, padx=10)
        
        # زر مسح الفلاتر
        tk.Button(row2, text="مسح الفلاتر", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', 
                 command=self.clear_filters).pack(side=tk.RIGHT, padx=5)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات الجرد", 
                                   font=FONTS['heading'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء عناصر الإحصائيات
        self.create_statistics_widgets(stats_frame)
        
        # إطار قائمة المنتجات
        products_frame = tk.LabelFrame(self.window, text="قائمة المنتجات للجرد", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        products_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء جدول المنتجات
        self.create_products_table(products_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # أزرار الإجراءات
        tk.Button(buttons_frame, text="بدء الجرد", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.start_count).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="حفظ الجرد", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=15,
                 command=self.save_count).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تصدير إلى Excel", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.export_to_excel).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', width=15,
                 command=self.print_count).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=15,
                 command=self.load_products).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # تحميل الفئات
        self.load_categories()
        
    def create_statistics_widgets(self, parent):
        """إنشاء عناصر الإحصائيات"""
        stats_container = tk.Frame(parent, bg=COLORS['background'])
        stats_container.pack(fill='x', padx=10, pady=10)
        
        self.stats_labels = {}
        stats_info = [
            ('total_products', 'إجمالي المنتجات', COLORS['primary']),
            ('counted_products', 'المنتجات المجردة', COLORS['success']),
            ('pending_products', 'المنتجات المعلقة', COLORS['warning']),
            ('variance_products', 'منتجات بفروقات', COLORS['danger'])
        ]
        
        for i, (key, label, color) in enumerate(stats_info):
            stat_frame = tk.Frame(stats_container, bg=COLORS['background'], 
                                 relief='raised', bd=1)
            stat_frame.pack(side=tk.RIGHT, padx=10, pady=5, fill='x', expand=True)
            
            tk.Label(stat_frame, text=label, font=FONTS['small'], 
                    bg=COLORS['background'], fg=COLORS['text']).pack(pady=(5, 0))
            
            self.stats_labels[key] = tk.Label(stat_frame, text="0", font=FONTS['heading'], 
                                            bg=COLORS['background'], fg=color)
            self.stats_labels[key].pack(pady=(0, 5))
            
    def create_products_table(self, parent):
        """إنشاء جدول المنتجات"""
        table_frame = tk.Frame(parent, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تحديد الأعمدة
        columns = ('الرقم', 'اسم المنتج', 'الفئة', 'الوحدة', 'الكمية الحالية', 
                  'الكمية المجردة', 'الفرق', 'الحالة', 'ملاحظات')
        
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة وعرضها
        column_widths = {
            'الرقم': 80,
            'اسم المنتج': 200,
            'الفئة': 150,
            'الوحدة': 80,
            'الكمية الحالية': 120,
            'الكمية المجردة': 120,
            'الفرق': 100,
            'الحالة': 100,
            'ملاحظات': 200
        }
        
        for col in columns:
            self.products_tree.heading(col, text=col)
            self.products_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.products_tree.xview)
        self.products_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.products_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.products_tree.bind('<Double-1>', self.on_product_double_click)
        
    def load_categories(self):
        """تحميل قائمة الفئات"""
        try:
            query = "SELECT id, name FROM categories WHERE is_active = 1 ORDER BY name"
            categories = self.db_manager.execute_query(query)
            
            category_list = ['الكل']
            for category in categories:
                category_list.append(f"{category['id']}: {category['name']}")
            
            self.category_combo['values'] = category_list
            self.category_combo.set('الكل')
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفئات:\n{str(e)}")
            
    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_var.set('')
        self.category_var.set('الكل')
        self.load_products()
        
    def on_search_change(self, event=None):
        """معالج تغيير البحث"""
        # تأخير البحث لتجنب البحث مع كل حرف
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        self.search_timer = self.window.after(500, self.load_products)
        
    def on_filter_change(self, event=None):
        """معالج تغيير الفلتر"""
        self.load_products()

    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # بناء الاستعلام
            query = """
                SELECT p.id, p.name, c.name as category_name, p.unit, p.stock_quantity,
                       ic.counted_quantity, ic.notes, ic.status
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN inventory_counts ic ON p.id = ic.product_id
                    AND ic.count_date = ?
                WHERE p.is_active = 1
            """

            params = [self.count_date_var.get()]

            # فلتر البحث
            search_term = self.search_var.get().strip()
            if search_term:
                query += " AND (p.name LIKE ? OR p.barcode LIKE ? OR c.name LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern, search_pattern])

            # فلتر الفئة
            category_filter = self.category_var.get()
            if category_filter and category_filter != 'الكل':
                category_id = int(category_filter.split(':')[0])
                query += " AND p.category_id = ?"
                params.append(category_id)

            query += " ORDER BY p.name"

            # تنفيذ الاستعلام
            products = self.db_manager.execute_query(query, params)

            # عرض البيانات
            total_products = 0
            counted_products = 0
            pending_products = 0
            variance_products = 0

            for product in products:
                counted_qty = product['counted_quantity'] if product['counted_quantity'] is not None else ''
                current_qty = product['stock_quantity']

                # حساب الفرق
                if counted_qty != '':
                    difference = float(counted_qty) - current_qty
                    difference_text = f"{difference:+.2f}"
                    if difference != 0:
                        variance_products += 1
                else:
                    difference_text = ''

                # تحديد الحالة
                status = product['status'] if product['status'] else 'معلق'
                if status == 'معلق':
                    pending_products += 1
                elif status == 'مكتمل':
                    counted_products += 1

                # تحديد لون الصف حسب الحالة
                tags = []
                if status == 'مكتمل':
                    tags = ['completed']
                elif status == 'معلق':
                    tags = ['pending']
                elif difference_text and float(difference_text) != 0:
                    tags = ['variance']

                self.products_tree.insert('', 'end', values=(
                    product['id'],
                    product['name'],
                    product['category_name'] or 'غير محدد',
                    product['unit'],
                    f"{current_qty:.2f}",
                    counted_qty,
                    difference_text,
                    status,
                    product['notes'] or ''
                ), tags=tags)

                total_products += 1

            # تكوين ألوان الصفوف
            self.products_tree.tag_configure('completed', background='#d4edda')
            self.products_tree.tag_configure('pending', background='#fff3cd')
            self.products_tree.tag_configure('variance', background='#f8d7da')

            # تحديث الإحصائيات
            self.update_statistics(total_products, counted_products, pending_products, variance_products)

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "عرض قائمة جرد المخزون",
                f"تم عرض {total_products} منتج للجرد",
                "inventory_count"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المنتجات:\n{str(e)}")

    def update_statistics(self, total_products, counted_products, pending_products, variance_products):
        """تحديث الإحصائيات"""
        self.stats_labels['total_products'].config(text=str(total_products))
        self.stats_labels['counted_products'].config(text=str(counted_products))
        self.stats_labels['pending_products'].config(text=str(pending_products))
        self.stats_labels['variance_products'].config(text=str(variance_products))

    def get_selected_product_id(self):
        """الحصول على معرف المنتج المحدد"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج أولاً")
            return None

        item = self.products_tree.item(selection[0])
        return item['values'][0]

    def on_product_double_click(self, event):
        """معالج النقر المزدوج على المنتج"""
        self.edit_count()

    def start_count(self):
        """بدء عملية الجرد"""
        if messagebox.askyesno("تأكيد", "هل تريد بدء عملية جرد جديدة؟\nسيتم مسح أي بيانات جرد سابقة لهذا التاريخ."):
            try:
                # حذف بيانات الجرد السابقة لنفس التاريخ
                delete_query = "DELETE FROM inventory_counts WHERE count_date = ?"
                self.db_manager.execute_query(delete_query, [self.count_date_var.get()])

                # تسجيل العملية
                log_user_activity(
                    self.db_manager,
                    self.current_user['id'],
                    "بدء جرد المخزون",
                    f"تاريخ الجرد: {self.count_date_var.get()}, نوع الجرد: {self.count_type_var.get()}",
                    "inventory_count"
                )

                messagebox.showinfo("نجح", "تم بدء عملية الجرد بنجاح")
                self.load_products()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في بدء الجرد:\n{str(e)}")

    def edit_count(self):
        """تعديل كمية الجرد للمنتج المحدد"""
        product_id = self.get_selected_product_id()
        if product_id:
            CountEditDialog(self.window, self.current_user, product_id,
                           self.count_date_var.get(), self.load_products)

    def save_count(self):
        """حفظ نتائج الجرد"""
        try:
            # التحقق من وجود بيانات جرد
            check_query = "SELECT COUNT(*) as count FROM inventory_counts WHERE count_date = ?"
            result = self.db_manager.execute_query(check_query, [self.count_date_var.get()])
            count = result[0]['count'] if result else 0

            if count == 0:
                messagebox.showwarning("تحذير", "لا توجد بيانات جرد لحفظها")
                return

            if messagebox.askyesno("تأكيد", "هل تريد حفظ نتائج الجرد وتحديث كميات المخزون؟"):
                # تحديث كميات المخزون بناءً على نتائج الجرد
                update_query = """
                    UPDATE products
                    SET stock_quantity = (
                        SELECT counted_quantity
                        FROM inventory_counts
                        WHERE product_id = products.id
                        AND count_date = ?
                        AND counted_quantity IS NOT NULL
                    )
                    WHERE id IN (
                        SELECT product_id
                        FROM inventory_counts
                        WHERE count_date = ?
                        AND counted_quantity IS NOT NULL
                    )
                """

                self.db_manager.execute_query(update_query, [self.count_date_var.get(), self.count_date_var.get()])

                # تسجيل حركات المخزون للفروقات
                self.record_inventory_movements()

                # تسجيل العملية
                log_user_activity(
                    self.db_manager,
                    self.current_user['id'],
                    "حفظ نتائج جرد المخزون",
                    f"تاريخ الجرد: {self.count_date_var.get()}",
                    "inventory_count"
                )

                messagebox.showinfo("نجح", "تم حفظ نتائج الجرد وتحديث المخزون بنجاح")
                self.load_products()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الجرد:\n{str(e)}")

    def record_inventory_movements(self):
        """تسجيل حركات المخزون للفروقات"""
        try:
            # جلب الفروقات
            query = """
                SELECT ic.product_id, p.name, p.stock_quantity, ic.counted_quantity,
                       (ic.counted_quantity - p.stock_quantity) as difference
                FROM inventory_counts ic
                JOIN products p ON ic.product_id = p.id
                WHERE ic.count_date = ?
                AND ic.counted_quantity IS NOT NULL
                AND ic.counted_quantity != p.stock_quantity
            """

            differences = self.db_manager.execute_query(query, [self.count_date_var.get()])

            for diff in differences:
                movement_type = 'in' if diff['difference'] > 0 else 'out'
                quantity = abs(diff['difference'])

                # إدراج حركة المخزون
                movement_query = """
                    INSERT INTO inventory_movements
                    (product_id, movement_type, quantity, reference_type, notes,
                     user_id, movement_date, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """

                movement_params = [
                    diff['product_id'],
                    movement_type,
                    quantity,
                    'inventory_count',
                    f"تسوية جرد - الفرق: {diff['difference']:+.2f}",
                    self.current_user['id'],
                    self.count_date_var.get(),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ]

                self.db_manager.execute_query(movement_query, movement_params)

        except Exception as e:
            print(f"خطأ في تسجيل حركات المخزون: {str(e)}")

    def export_to_excel(self):
        """تصدير إلى Excel"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة التصدير إلى Excel قريباً")

    def print_count(self):
        """طباعة الجرد"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة الطباعة قريباً")

class CountEditDialog:
    """حوار تعديل كمية الجرد"""

    def __init__(self, parent, current_user, product_id, count_date, callback=None):
        self.parent = parent
        self.current_user = current_user
        self.product_id = product_id
        self.count_date = count_date
        self.callback = callback
        self.db_manager = DatabaseManager()

        self.setup_window()
        self.create_widgets()
        self.load_product_data()

    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تعديل كمية الجرد")
        self.window.geometry("500x400")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 500
        height = 400
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = tk.Label(self.window, text="تعديل كمية الجرد",
                              font=FONTS['title'], bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)

        # إطار معلومات المنتج
        product_frame = tk.LabelFrame(self.window, text="معلومات المنتج",
                                     font=FONTS['heading'], bg=COLORS['background'])
        product_frame.pack(fill='x', padx=20, pady=10)

        # اسم المنتج
        row1 = tk.Frame(product_frame, bg=COLORS['background'])
        row1.pack(fill='x', padx=10, pady=5)

        tk.Label(row1, text="اسم المنتج:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.product_name_label = tk.Label(row1, text="", font=FONTS['normal'],
                                          bg=COLORS['background'], fg=COLORS['primary'])
        self.product_name_label.pack(side=tk.RIGHT, padx=5)

        # الفئة
        row2 = tk.Frame(product_frame, bg=COLORS['background'])
        row2.pack(fill='x', padx=10, pady=5)

        tk.Label(row2, text="الفئة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.category_label = tk.Label(row2, text="", font=FONTS['normal'],
                                      bg=COLORS['background'], fg=COLORS['info'])
        self.category_label.pack(side=tk.RIGHT, padx=5)

        # الوحدة
        row3 = tk.Frame(product_frame, bg=COLORS['background'])
        row3.pack(fill='x', padx=10, pady=5)

        tk.Label(row3, text="الوحدة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.unit_label = tk.Label(row3, text="", font=FONTS['normal'],
                                  bg=COLORS['background'], fg=COLORS['info'])
        self.unit_label.pack(side=tk.RIGHT, padx=5)

        # إطار بيانات الجرد
        count_frame = tk.LabelFrame(self.window, text="بيانات الجرد",
                                   font=FONTS['heading'], bg=COLORS['background'])
        count_frame.pack(fill='x', padx=20, pady=10)

        # الكمية الحالية
        row4 = tk.Frame(count_frame, bg=COLORS['background'])
        row4.pack(fill='x', padx=10, pady=5)

        tk.Label(row4, text="الكمية الحالية:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.current_qty_label = tk.Label(row4, text="", font=FONTS['normal'],
                                         bg=COLORS['background'], fg=COLORS['success'])
        self.current_qty_label.pack(side=tk.RIGHT, padx=5)

        # الكمية المجردة
        row5 = tk.Frame(count_frame, bg=COLORS['background'])
        row5.pack(fill='x', padx=10, pady=5)

        tk.Label(row5, text="الكمية المجردة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.counted_qty_var = tk.StringVar()
        counted_qty_entry = tk.Entry(row5, textvariable=self.counted_qty_var,
                                    font=FONTS['normal'], width=20)
        counted_qty_entry.pack(side=tk.RIGHT, padx=5)
        counted_qty_entry.bind('<KeyRelease>', self.calculate_difference)

        # الفرق
        row6 = tk.Frame(count_frame, bg=COLORS['background'])
        row6.pack(fill='x', padx=10, pady=5)

        tk.Label(row6, text="الفرق:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.difference_label = tk.Label(row6, text="0.00", font=FONTS['normal'],
                                        bg=COLORS['background'], fg=COLORS['warning'])
        self.difference_label.pack(side=tk.RIGHT, padx=5)

        # الملاحظات
        row7 = tk.Frame(count_frame, bg=COLORS['background'])
        row7.pack(fill='x', padx=10, pady=5)

        tk.Label(row7, text="الملاحظات:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.TOP, anchor='e', padx=5)

        self.notes_text = tk.Text(row7, font=FONTS['normal'], width=40, height=4)
        self.notes_text.pack(side=tk.TOP, padx=5, pady=5)

        # أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.save_count).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def load_product_data(self):
        """تحميل بيانات المنتج"""
        try:
            # جلب بيانات المنتج
            product_query = """
                SELECT p.name, p.unit, p.stock_quantity, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.id = ?
            """

            product_result = self.db_manager.execute_query(product_query, [self.product_id])

            if product_result:
                product = product_result[0]
                self.product_name_label.config(text=product['name'])
                self.category_label.config(text=product['category_name'] or 'غير محدد')
                self.unit_label.config(text=product['unit'])
                self.current_qty_label.config(text=f"{product['stock_quantity']:.2f}")
                self.current_quantity = product['stock_quantity']

            # جلب بيانات الجرد الموجودة
            count_query = """
                SELECT counted_quantity, notes, status
                FROM inventory_counts
                WHERE product_id = ? AND count_date = ?
            """

            count_result = self.db_manager.execute_query(count_query, [self.product_id, self.count_date])

            if count_result:
                count_data = count_result[0]
                if count_data['counted_quantity'] is not None:
                    self.counted_qty_var.set(str(count_data['counted_quantity']))
                if count_data['notes']:
                    self.notes_text.delete('1.0', tk.END)
                    self.notes_text.insert('1.0', count_data['notes'])

            self.calculate_difference()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات المنتج:\n{str(e)}")

    def calculate_difference(self, event=None):
        """حساب الفرق"""
        try:
            counted_qty = self.counted_qty_var.get().strip()
            if counted_qty:
                difference = float(counted_qty) - self.current_quantity
                self.difference_label.config(text=f"{difference:+.2f}")

                # تغيير لون الفرق حسب القيمة
                if difference > 0:
                    self.difference_label.config(fg=COLORS['success'])
                elif difference < 0:
                    self.difference_label.config(fg=COLORS['danger'])
                else:
                    self.difference_label.config(fg=COLORS['text'])
            else:
                self.difference_label.config(text="0.00", fg=COLORS['text'])

        except ValueError:
            self.difference_label.config(text="خطأ", fg=COLORS['danger'])

    def save_count(self):
        """حفظ بيانات الجرد"""
        try:
            # التحقق من البيانات
            counted_qty = self.counted_qty_var.get().strip()
            if not counted_qty:
                messagebox.showwarning("تحذير", "يرجى إدخال الكمية المجردة")
                return

            try:
                counted_quantity = float(counted_qty)
            except ValueError:
                messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
                return

            if counted_quantity < 0:
                messagebox.showwarning("تحذير", "لا يمكن أن تكون الكمية سالبة")
                return

            notes = self.notes_text.get('1.0', tk.END).strip()

            # التحقق من وجود سجل جرد سابق
            check_query = """
                SELECT id FROM inventory_counts
                WHERE product_id = ? AND count_date = ?
            """

            existing = self.db_manager.execute_query(check_query, [self.product_id, self.count_date])

            if existing:
                # تحديث السجل الموجود
                update_query = """
                    UPDATE inventory_counts
                    SET counted_quantity = ?, notes = ?, status = 'مكتمل',
                        updated_at = CURRENT_TIMESTAMP
                    WHERE product_id = ? AND count_date = ?
                """

                self.db_manager.execute_query(update_query, [
                    counted_quantity, notes, self.product_id, self.count_date
                ])
            else:
                # إنشاء سجل جديد
                insert_query = """
                    INSERT INTO inventory_counts
                    (product_id, count_date, counted_quantity, notes, status,
                     user_id, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """

                self.db_manager.execute_query(insert_query, [
                    self.product_id, self.count_date, counted_quantity, notes,
                    'مكتمل', self.current_user['id'],
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ])

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "تعديل كمية جرد المخزون",
                f"المنتج: {self.product_id}, الكمية المجردة: {counted_quantity}",
                "inventory_count",
                self.product_id
            )

            messagebox.showinfo("نجح", "تم حفظ بيانات الجرد بنجاح")

            if self.callback:
                self.callback()

            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ بيانات الجرد:\n{str(e)}")
