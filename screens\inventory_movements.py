# -*- coding: utf-8 -*-
"""
شاشة حركات المخزون
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import (get_date_range, check_user_permission, show_permission_error, 
                          log_user_activity, format_currency)
from utils.arabic_support import ArabicSupport

class InventoryMovements:
    """كلاس حركات المخزون"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'inventory_management'):
            show_permission_error('حركات المخزون')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_movements()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("حركات المخزون")
        self.window.geometry("1500x800")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1500
        height = 800
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="حركات المخزون",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الفلاتر
        filters_frame = tk.LabelFrame(self.window, text="فلاتر البحث", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        filters_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول - فلاتر التاريخ
        row1_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # من تاريخ
        tk.Label(row1_frame, text="من تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.from_date_var = tk.StringVar(value=get_current_date())
        from_date_entry = tk.Entry(row1_frame, textvariable=self.from_date_var, 
                                  font=FONTS['normal'], width=12, justify='right')
        from_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # إلى تاريخ
        tk.Label(row1_frame, text="إلى تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.to_date_var = tk.StringVar(value=get_current_date())
        to_date_entry = tk.Entry(row1_frame, textvariable=self.to_date_var, 
                                font=FONTS['normal'], width=12, justify='right')
        to_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني - فلاتر نوع الحركة والمرجع
        row2_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        # نوع الحركة
        tk.Label(row2_frame, text="نوع الحركة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.movement_type_var = tk.StringVar()
        movement_type_combo = ttk.Combobox(row2_frame, textvariable=self.movement_type_var,
                                          font=FONTS['normal'], width=15, state='readonly')
        movement_type_combo['values'] = ['الكل', 'وارد', 'صادر', 'تسوية']
        movement_type_combo.set('الكل')
        movement_type_combo.pack(side=tk.RIGHT, padx=5)
        movement_type_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # نوع المرجع
        tk.Label(row2_frame, text="نوع المرجع:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.reference_type_var = tk.StringVar()
        reference_type_combo = ttk.Combobox(row2_frame, textvariable=self.reference_type_var,
                                           font=FONTS['normal'], width=15, state='readonly')
        reference_type_combo['values'] = ['الكل', 'مبيعات', 'مشتريات', 'جرد', 'تسوية']
        reference_type_combo.set('الكل')
        reference_type_combo.pack(side=tk.RIGHT, padx=5)
        reference_type_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # البحث
        tk.Label(row2_frame, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(row2_frame, textvariable=self.search_var, 
                               font=FONTS['normal'], width=20)
        search_entry.pack(side=tk.RIGHT, padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # الصف الثالث - فترات سريعة وأزرار
        row3_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row3_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row3_frame, text="فترات سريعة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        quick_periods = [
            ('اليوم', 'today'),
            ('هذا الأسبوع', 'this_week'),
            ('هذا الشهر', 'this_month'),
            ('الشهر الماضي', 'last_month')
        ]
        
        for text, period in quick_periods:
            tk.Button(row3_frame, text=text, font=FONTS['small'],
                     bg=COLORS['info'], fg='white', 
                     command=lambda p=period: self.set_quick_period(p)).pack(side=tk.RIGHT, padx=2)
        
        # زر البحث
        tk.Button(row3_frame, text="بحث", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', 
                 command=self.load_movements).pack(side=tk.RIGHT, padx=10)
        
        # زر مسح الفلاتر
        tk.Button(row3_frame, text="مسح الفلاتر", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', 
                 command=self.clear_filters).pack(side=tk.RIGHT, padx=5)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['heading'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء عناصر الإحصائيات
        self.create_statistics_widgets(stats_frame)
        
        # إطار قائمة الحركات
        movements_frame = tk.LabelFrame(self.window, text="قائمة حركات المخزون", 
                                       font=FONTS['heading'], bg=COLORS['background'])
        movements_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء جدول الحركات
        self.create_movements_table(movements_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # أزرار الإجراءات
        tk.Button(buttons_frame, text="حركة جديدة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.new_movement).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="عرض التفاصيل", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.view_movement).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تصدير إلى Excel", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=15,
                 command=self.export_to_excel).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', width=15,
                 command=self.print_movements).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=15,
                 command=self.load_movements).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def create_statistics_widgets(self, parent):
        """إنشاء عناصر الإحصائيات"""
        stats_container = tk.Frame(parent, bg=COLORS['background'])
        stats_container.pack(fill='x', padx=10, pady=10)
        
        self.stats_labels = {}
        stats_info = [
            ('total_movements', 'إجمالي الحركات', COLORS['primary']),
            ('in_movements', 'الحركات الواردة', COLORS['success']),
            ('out_movements', 'الحركات الصادرة', COLORS['danger']),
            ('adjustment_movements', 'حركات التسوية', COLORS['warning'])
        ]
        
        for i, (key, label, color) in enumerate(stats_info):
            stat_frame = tk.Frame(stats_container, bg=COLORS['background'], 
                                 relief='raised', bd=1)
            stat_frame.pack(side=tk.RIGHT, padx=10, pady=5, fill='x', expand=True)
            
            tk.Label(stat_frame, text=label, font=FONTS['small'], 
                    bg=COLORS['background'], fg=COLORS['text']).pack(pady=(5, 0))
            
            self.stats_labels[key] = tk.Label(stat_frame, text="0", font=FONTS['heading'], 
                                            bg=COLORS['background'], fg=color)
            self.stats_labels[key].pack(pady=(0, 5))
            
    def create_movements_table(self, parent):
        """إنشاء جدول الحركات"""
        table_frame = tk.Frame(parent, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تحديد الأعمدة
        columns = ('الرقم', 'التاريخ', 'المنتج', 'نوع الحركة', 'الكمية', 
                  'نوع المرجع', 'رقم المرجع', 'الملاحظات', 'المستخدم')
        
        self.movements_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة وعرضها
        column_widths = {
            'الرقم': 80,
            'التاريخ': 100,
            'المنتج': 200,
            'نوع الحركة': 100,
            'الكمية': 100,
            'نوع المرجع': 120,
            'رقم المرجع': 120,
            'الملاحظات': 200,
            'المستخدم': 120
        }
        
        for col in columns:
            self.movements_tree.heading(col, text=col)
            self.movements_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.movements_tree.yview)
        self.movements_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.movements_tree.xview)
        self.movements_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.movements_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.movements_tree.bind('<Double-1>', self.on_movement_double_click)

    def set_quick_period(self, period):
        """تعيين فترة سريعة"""
        try:
            start_date, end_date = get_date_range(period)
            self.from_date_var.set(start_date.strftime('%Y-%m-%d'))
            self.to_date_var.set(end_date.strftime('%Y-%m-%d'))
            self.load_movements()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين الفترة:\n{str(e)}")

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.from_date_var.set(get_current_date())
        self.to_date_var.set(get_current_date())
        self.movement_type_var.set('الكل')
        self.reference_type_var.set('الكل')
        self.search_var.set('')
        self.load_movements()

    def on_search_change(self, event=None):
        """معالج تغيير البحث"""
        # تأخير البحث لتجنب البحث مع كل حرف
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        self.search_timer = self.window.after(500, self.load_movements)

    def on_filter_change(self, event=None):
        """معالج تغيير الفلتر"""
        self.load_movements()

    def load_movements(self):
        """تحميل قائمة حركات المخزون"""
        try:
            # مسح البيانات الحالية
            for item in self.movements_tree.get_children():
                self.movements_tree.delete(item)

            # بناء الاستعلام
            query = """
                SELECT im.id, im.movement_date, p.name as product_name,
                       im.movement_type, im.quantity, im.reference_type,
                       im.reference_id, im.notes, u.name as user_name
                FROM inventory_movements im
                LEFT JOIN products p ON im.product_id = p.id
                LEFT JOIN users u ON im.user_id = u.id
                WHERE 1=1
            """

            params = []

            # فلتر التاريخ
            from_date = self.from_date_var.get()
            to_date = self.to_date_var.get()
            if from_date and to_date:
                query += " AND DATE(im.movement_date) BETWEEN ? AND ?"
                params.extend([from_date, to_date])

            # فلتر نوع الحركة
            movement_type_filter = self.movement_type_var.get()
            if movement_type_filter and movement_type_filter != 'الكل':
                movement_type_map = {
                    'وارد': 'in',
                    'صادر': 'out',
                    'تسوية': 'adjustment'
                }
                if movement_type_filter in movement_type_map:
                    query += " AND im.movement_type = ?"
                    params.append(movement_type_map[movement_type_filter])

            # فلتر نوع المرجع
            reference_type_filter = self.reference_type_var.get()
            if reference_type_filter and reference_type_filter != 'الكل':
                reference_type_map = {
                    'مبيعات': 'sales_invoice',
                    'مشتريات': 'purchase_invoice',
                    'جرد': 'inventory_count',
                    'تسوية': 'adjustment'
                }
                if reference_type_filter in reference_type_map:
                    query += " AND im.reference_type = ?"
                    params.append(reference_type_map[reference_type_filter])

            # فلتر البحث
            search_term = self.search_var.get().strip()
            if search_term:
                query += " AND (p.name LIKE ? OR im.notes LIKE ? OR u.name LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern, search_pattern])

            query += " ORDER BY im.movement_date DESC, im.id DESC"

            # تنفيذ الاستعلام
            movements = self.db_manager.execute_query(query, params)

            # عرض البيانات
            total_movements = 0
            in_movements = 0
            out_movements = 0
            adjustment_movements = 0

            movement_type_names = {
                'in': 'وارد',
                'out': 'صادر',
                'adjustment': 'تسوية'
            }

            reference_type_names = {
                'sales_invoice': 'فاتورة مبيعات',
                'purchase_invoice': 'فاتورة مشتريات',
                'inventory_count': 'جرد مخزون',
                'adjustment': 'تسوية',
                'manual': 'يدوي'
            }

            for movement in movements:
                # تحديد لون الصف حسب نوع الحركة
                tags = []
                if movement['movement_type'] == 'in':
                    tags = ['in_movement']
                    in_movements += 1
                elif movement['movement_type'] == 'out':
                    tags = ['out_movement']
                    out_movements += 1
                elif movement['movement_type'] == 'adjustment':
                    tags = ['adjustment_movement']
                    adjustment_movements += 1

                # تحديد نوع الحركة والمرجع
                movement_type_ar = movement_type_names.get(movement['movement_type'], movement['movement_type'])
                reference_type_ar = reference_type_names.get(movement['reference_type'], movement['reference_type'])

                self.movements_tree.insert('', 'end', values=(
                    movement['id'],
                    movement['movement_date'],
                    movement['product_name'] or 'غير محدد',
                    movement_type_ar,
                    f"{movement['quantity']:.2f}",
                    reference_type_ar,
                    movement['reference_id'] or '',
                    movement['notes'] or '',
                    movement['user_name'] or ''
                ), tags=tags)

                total_movements += 1

            # تكوين ألوان الصفوف
            self.movements_tree.tag_configure('in_movement', background='#d4edda')
            self.movements_tree.tag_configure('out_movement', background='#f8d7da')
            self.movements_tree.tag_configure('adjustment_movement', background='#fff3cd')

            # تحديث الإحصائيات
            self.update_statistics(total_movements, in_movements, out_movements, adjustment_movements)

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "عرض حركات المخزون",
                f"تم عرض {total_movements} حركة",
                "inventory_movements"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل حركات المخزون:\n{str(e)}")

    def update_statistics(self, total_movements, in_movements, out_movements, adjustment_movements):
        """تحديث الإحصائيات"""
        self.stats_labels['total_movements'].config(text=str(total_movements))
        self.stats_labels['in_movements'].config(text=str(in_movements))
        self.stats_labels['out_movements'].config(text=str(out_movements))
        self.stats_labels['adjustment_movements'].config(text=str(adjustment_movements))

    def get_selected_movement_id(self):
        """الحصول على معرف الحركة المحددة"""
        selection = self.movements_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار حركة أولاً")
            return None

        item = self.movements_tree.item(selection[0])
        return item['values'][0]

    def on_movement_double_click(self, event):
        """معالج النقر المزدوج على الحركة"""
        self.view_movement()

    def new_movement(self):
        """إنشاء حركة جديدة"""
        MovementDialog(self.window, self.current_user, self.load_movements)

    def view_movement(self):
        """عرض تفاصيل الحركة"""
        movement_id = self.get_selected_movement_id()
        if movement_id:
            # جلب بيانات الحركة
            query = """
                SELECT im.*, p.name as product_name, u.name as user_name
                FROM inventory_movements im
                LEFT JOIN products p ON im.product_id = p.id
                LEFT JOIN users u ON im.user_id = u.id
                WHERE im.id = ?
            """
            result = self.db_manager.execute_query(query, [movement_id])
            if result:
                movement_data = result[0]
                self.show_movement_details(movement_data)

    def show_movement_details(self, movement_data):
        """عرض تفاصيل الحركة في نافذة منفصلة"""
        details_window = tk.Toplevel(self.window)
        details_window.title(f"تفاصيل الحركة - {movement_data['id']}")
        details_window.geometry("600x500")
        details_window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(details_window)

        # العنوان
        title_label = tk.Label(details_window, text=f"تفاصيل الحركة - {movement_data['id']}",
                              font=FONTS['title'], bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)

        # معلومات الحركة
        info_frame = tk.LabelFrame(details_window, text="معلومات الحركة",
                                  font=FONTS['heading'], bg=COLORS['background'])
        info_frame.pack(fill='x', padx=20, pady=10)

        movement_type_names = {
            'in': 'وارد',
            'out': 'صادر',
            'adjustment': 'تسوية'
        }

        reference_type_names = {
            'sales_invoice': 'فاتورة مبيعات',
            'purchase_invoice': 'فاتورة مشتريات',
            'inventory_count': 'جرد مخزون',
            'adjustment': 'تسوية',
            'manual': 'يدوي'
        }

        movement_type_ar = movement_type_names.get(movement_data['movement_type'], movement_data['movement_type'])
        reference_type_ar = reference_type_names.get(movement_data['reference_type'], movement_data['reference_type'])

        info_text = f"""
رقم الحركة: {movement_data['id']}
التاريخ: {movement_data['movement_date']}
المنتج: {movement_data['product_name'] or 'غير محدد'}
نوع الحركة: {movement_type_ar}
الكمية: {movement_data['quantity']:.2f}
نوع المرجع: {reference_type_ar}
رقم المرجع: {movement_data['reference_id'] or 'غير محدد'}
الملاحظات: {movement_data['notes'] or 'لا توجد ملاحظات'}
المستخدم: {movement_data['user_name'] or 'غير محدد'}
تاريخ الإنشاء: {movement_data['created_at'] or 'غير محدد'}
        """

        tk.Label(info_frame, text=info_text, font=FONTS['normal'],
                bg=COLORS['background'], justify='right').pack(padx=10, pady=10)

        # زر الإغلاق
        tk.Button(details_window, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white',
                 command=details_window.destroy).pack(pady=10)

    def export_to_excel(self):
        """تصدير إلى Excel"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة التصدير إلى Excel قريباً")

    def print_movements(self):
        """طباعة الحركات"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة الطباعة قريباً")

class MovementDialog:
    """حوار إنشاء حركة مخزون جديدة"""

    def __init__(self, parent, current_user, callback=None):
        self.parent = parent
        self.current_user = current_user
        self.callback = callback
        self.db_manager = DatabaseManager()

        self.setup_window()
        self.create_widgets()
        self.load_products()

    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("حركة مخزون جديدة")
        self.window.geometry("600x500")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 600
        height = 500
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = tk.Label(self.window, text="حركة مخزون جديدة",
                              font=FONTS['title'], bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)

        # إطار البيانات
        data_frame = tk.LabelFrame(self.window, text="بيانات الحركة",
                                  font=FONTS['heading'], bg=COLORS['background'])
        data_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # التاريخ
        row1 = tk.Frame(data_frame, bg=COLORS['background'])
        row1.pack(fill='x', padx=10, pady=5)

        tk.Label(row1, text="التاريخ:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.movement_date_var = tk.StringVar(value=get_current_date())
        tk.Entry(row1, textvariable=self.movement_date_var, font=FONTS['normal'],
                width=20).pack(side=tk.RIGHT, padx=5)

        # المنتج
        row2 = tk.Frame(data_frame, bg=COLORS['background'])
        row2.pack(fill='x', padx=10, pady=5)

        tk.Label(row2, text="المنتج:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(row2, textvariable=self.product_var,
                                         font=FONTS['normal'], width=30, state='readonly')
        self.product_combo.pack(side=tk.RIGHT, padx=5)

        # نوع الحركة
        row3 = tk.Frame(data_frame, bg=COLORS['background'])
        row3.pack(fill='x', padx=10, pady=5)

        tk.Label(row3, text="نوع الحركة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.movement_type_var = tk.StringVar()
        movement_type_combo = ttk.Combobox(row3, textvariable=self.movement_type_var,
                                          font=FONTS['normal'], width=20, state='readonly')
        movement_type_combo['values'] = ['وارد', 'صادر', 'تسوية']
        movement_type_combo.pack(side=tk.RIGHT, padx=5)

        # الكمية
        row4 = tk.Frame(data_frame, bg=COLORS['background'])
        row4.pack(fill='x', padx=10, pady=5)

        tk.Label(row4, text="الكمية:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.quantity_var = tk.StringVar()
        tk.Entry(row4, textvariable=self.quantity_var, font=FONTS['normal'],
                width=20).pack(side=tk.RIGHT, padx=5)

        # نوع المرجع
        row5 = tk.Frame(data_frame, bg=COLORS['background'])
        row5.pack(fill='x', padx=10, pady=5)

        tk.Label(row5, text="نوع المرجع:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.reference_type_var = tk.StringVar()
        reference_type_combo = ttk.Combobox(row5, textvariable=self.reference_type_var,
                                           font=FONTS['normal'], width=20, state='readonly')
        reference_type_combo['values'] = ['يدوي', 'تسوية', 'جرد مخزون']
        reference_type_combo.set('يدوي')
        reference_type_combo.pack(side=tk.RIGHT, padx=5)

        # رقم المرجع
        row6 = tk.Frame(data_frame, bg=COLORS['background'])
        row6.pack(fill='x', padx=10, pady=5)

        tk.Label(row6, text="رقم المرجع:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.reference_id_var = tk.StringVar()
        tk.Entry(row6, textvariable=self.reference_id_var, font=FONTS['normal'],
                width=20).pack(side=tk.RIGHT, padx=5)

        # الملاحظات
        row7 = tk.Frame(data_frame, bg=COLORS['background'])
        row7.pack(fill='x', padx=10, pady=5)

        tk.Label(row7, text="الملاحظات:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.TOP, anchor='e', padx=5)

        self.notes_text = tk.Text(row7, font=FONTS['normal'], width=50, height=4)
        self.notes_text.pack(side=tk.TOP, padx=5, pady=5)

        # أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.save_movement).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            query = "SELECT id, name FROM products WHERE is_active = 1 ORDER BY name"
            products = self.db_manager.execute_query(query)

            product_list = []
            for product in products:
                product_list.append(f"{product['id']}: {product['name']}")

            self.product_combo['values'] = product_list

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المنتجات:\n{str(e)}")

    def save_movement(self):
        """حفظ الحركة"""
        try:
            # التحقق من البيانات
            if not self.product_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار منتج")
                return

            if not self.movement_type_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار نوع الحركة")
                return

            if not self.quantity_var.get() or float(self.quantity_var.get()) <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
                return

            # استخراج البيانات
            try:
                product_id = int(self.product_var.get().split(':')[0])
            except (ValueError, IndexError):
                messagebox.showerror("خطأ", "يرجى اختيار منتج صحيح")
                return

            quantity = float(self.quantity_var.get())

            # تحويل نوع الحركة إلى الإنجليزية
            movement_type_map = {
                'وارد': 'in',
                'صادر': 'out',
                'تسوية': 'adjustment'
            }
            movement_type = movement_type_map.get(self.movement_type_var.get())

            # تحويل نوع المرجع إلى الإنجليزية
            reference_type_map = {
                'يدوي': 'manual',
                'تسوية': 'adjustment',
                'جرد مخزون': 'inventory_count'
            }
            reference_type = reference_type_map.get(self.reference_type_var.get(), 'manual')

            notes = self.notes_text.get('1.0', tk.END).strip()

            # إدراج الحركة
            insert_query = """
                INSERT INTO inventory_movements (
                    product_id, movement_type, quantity, reference_type,
                    reference_id, notes, user_id, movement_date, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = [
                product_id,
                movement_type,
                quantity,
                reference_type,
                self.reference_id_var.get() or None,
                notes if notes else None,
                self.current_user['id'],
                self.movement_date_var.get(),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ]

            self.db_manager.execute_query(insert_query, params)

            # تحديث كمية المنتج
            if movement_type == 'in':
                update_query = "UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?"
            elif movement_type == 'out':
                update_query = "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?"
            else:  # adjustment
                # للتسوية، نحتاج لمعرفة إذا كانت زيادة أم نقص
                # هنا نفترض أن الكمية الموجبة تعني زيادة والسالبة تعني نقص
                if quantity >= 0:
                    update_query = "UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?"
                else:
                    update_query = "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?"
                    quantity = abs(quantity)

            self.db_manager.execute_query(update_query, [quantity, product_id])

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "إنشاء حركة مخزون",
                f"المنتج: {product_id}, النوع: {self.movement_type_var.get()}, الكمية: {quantity}",
                "inventory_movements"
            )

            messagebox.showinfo("نجح", "تم حفظ الحركة بنجاح")

            if self.callback:
                self.callback()

            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الحركة:\n{str(e)}")
