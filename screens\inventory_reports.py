# -*- coding: utf-8 -*-
"""
شاشة تقارير المخزون
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import (get_date_range, check_user_permission, show_permission_error, 
                          log_user_activity, format_currency)
from utils.arabic_support import ArabicSupport

class InventoryReports:
    """كلاس تقارير المخزون"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'reports_view'):
            show_permission_error('عرض تقارير المخزون')
            return
        
        self.setup_window()
        self.create_widgets()
        self.update_statistics()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تقارير المخزون")
        self.window.geometry("1400x900")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1400
        height = 900
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="تقارير المخزون",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الإحصائيات السريعة
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['heading'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء الإحصائيات
        self.create_statistics_widgets(stats_frame)
        
        # إطار أنواع التقارير
        reports_frame = tk.LabelFrame(self.window, text="أنواع تقارير المخزون", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        reports_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء أزرار التقارير
        self.create_report_buttons(reports_frame)
        
        # إطار عرض التقرير
        display_frame = tk.LabelFrame(self.window, text="عرض التقرير", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        display_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء منطقة عرض التقرير
        self.create_report_display(display_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(buttons_frame, text="تحديث الإحصائيات", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.update_statistics).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.LEFT, padx=5)
        
    def create_statistics_widgets(self, parent):
        """إنشاء عناصر الإحصائيات"""
        stats_container = tk.Frame(parent, bg=COLORS['background'])
        stats_container.pack(fill='x', padx=10, pady=10)
        
        self.stats_labels = {}
        stats_info = [
            ('total_products', 'إجمالي المنتجات', COLORS['primary']),
            ('active_products', 'المنتجات النشطة', COLORS['success']),
            ('low_stock_products', 'منتجات ناقصة', COLORS['warning']),
            ('out_of_stock_products', 'منتجات منتهية', COLORS['danger']),
            ('total_inventory_value', 'قيمة المخزون الإجمالية', COLORS['info']),
            ('categories_count', 'عدد الفئات', COLORS['secondary'])
        ]
        
        # ترتيب الإحصائيات في صفين
        for i, (key, label, color) in enumerate(stats_info):
            row = i // 3
            col = i % 3
            
            stat_frame = tk.Frame(stats_container, bg=COLORS['background'], 
                                 relief='raised', bd=1)
            stat_frame.grid(row=row, column=col, padx=10, pady=5, sticky='ew')
            
            tk.Label(stat_frame, text=label, font=FONTS['small'], 
                    bg=COLORS['background'], fg=COLORS['text']).pack(pady=(5, 0))
            
            self.stats_labels[key] = tk.Label(stat_frame, text="0", font=FONTS['heading'], 
                                            bg=COLORS['background'], fg=color)
            self.stats_labels[key].pack(pady=(0, 5))
        
        # تكوين الأعمدة لتوزيع متساوي
        for i in range(3):
            stats_container.grid_columnconfigure(i, weight=1)
            
    def create_report_buttons(self, parent):
        """إنشاء أزرار التقارير"""
        buttons_container = tk.Frame(parent, bg=COLORS['background'])
        buttons_container.pack(fill='x', padx=10, pady=10)
        
        reports_info = [
            ('تقرير المخزون الحالي', 'current_inventory', COLORS['primary'], 
             'عرض جميع المنتجات مع كمياتها وقيمها الحالية'),
            ('تقرير المنتجات الناقصة', 'low_stock', COLORS['warning'],
             'المنتجات التي وصلت لحد الحد الأدنى أو أقل'),
            ('تقرير المنتجات المنتهية', 'out_of_stock', COLORS['danger'],
             'المنتجات التي نفدت من المخزون'),
            ('تقرير حركة المخزون', 'inventory_movements', COLORS['info'],
             'جميع حركات الدخول والخروج للمخزون'),
            ('تقرير المخزون حسب الفئة', 'inventory_by_category', COLORS['success'],
             'تجميع المخزون حسب فئات المنتجات'),
            ('تقرير تقييم المخزون', 'inventory_valuation', COLORS['secondary'],
             'قيمة المخزون بأسعار التكلفة والبيع')
        ]
        
        # ترتيب الأزرار في صفين
        for i, (text, command, color, tooltip) in enumerate(reports_info):
            row = i // 3
            col = i % 3
            
            btn = tk.Button(buttons_container, text=text, font=FONTS['button'],
                           bg=color, fg='white', width=25, height=2,
                           command=lambda cmd=command: self.generate_report(cmd))
            btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
            
            # إضافة tooltip (يمكن تطويره لاحقاً)
            
        # تكوين الأعمدة لتوزيع متساوي
        for i in range(3):
            buttons_container.grid_columnconfigure(i, weight=1)
            
    def create_report_display(self, parent):
        """إنشاء منطقة عرض التقرير"""
        # إطار التحكم في التقرير
        control_frame = tk.Frame(parent, bg=COLORS['background'])
        control_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(control_frame, text="نوع التقرير الحالي:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.current_report_label = tk.Label(control_frame, text="لم يتم اختيار تقرير", 
                                           font=FONTS['normal'], bg=COLORS['background'], 
                                           fg=COLORS['primary'])
        self.current_report_label.pack(side=tk.RIGHT, padx=5)
        
        # جدول عرض البيانات
        table_frame = tk.Frame(parent, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إنشاء Treeview
        self.report_tree = ttk.Treeview(table_frame, show='headings', height=20)
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.report_tree.yview)
        self.report_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.report_tree.xview)
        self.report_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.report_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
    def update_statistics(self):
        """تحديث الإحصائيات السريعة"""
        try:
            # إجمالي المنتجات
            total_products_query = "SELECT COUNT(*) as count FROM products"
            result = self.db_manager.execute_query(total_products_query)
            total_products = result[0]['count'] if result else 0
            
            # المنتجات النشطة
            active_products_query = "SELECT COUNT(*) as count FROM products WHERE is_active = 1"
            result = self.db_manager.execute_query(active_products_query)
            active_products = result[0]['count'] if result else 0
            
            # المنتجات الناقصة
            low_stock_query = """
                SELECT COUNT(*) as count FROM products 
                WHERE is_active = 1 AND stock_quantity <= min_stock_level AND stock_quantity > 0
            """
            result = self.db_manager.execute_query(low_stock_query)
            low_stock = result[0]['count'] if result else 0
            
            # المنتجات المنتهية
            out_of_stock_query = """
                SELECT COUNT(*) as count FROM products 
                WHERE is_active = 1 AND stock_quantity = 0
            """
            result = self.db_manager.execute_query(out_of_stock_query)
            out_of_stock = result[0]['count'] if result else 0
            
            # قيمة المخزون الإجمالية
            inventory_value_query = """
                SELECT COALESCE(SUM(stock_quantity * cost_price), 0) as total_value
                FROM products WHERE is_active = 1
            """
            result = self.db_manager.execute_query(inventory_value_query)
            inventory_value = result[0]['total_value'] if result else 0
            
            # عدد الفئات
            categories_query = "SELECT COUNT(*) as count FROM categories"
            result = self.db_manager.execute_query(categories_query)
            categories_count = result[0]['count'] if result else 0
            
            # تحديث التسميات
            self.stats_labels['total_products'].config(text=str(total_products))
            self.stats_labels['active_products'].config(text=str(active_products))
            self.stats_labels['low_stock_products'].config(text=str(low_stock))
            self.stats_labels['out_of_stock_products'].config(text=str(out_of_stock))
            self.stats_labels['total_inventory_value'].config(text=f"{inventory_value:,.2f}")
            self.stats_labels['categories_count'].config(text=str(categories_count))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحديث الإحصائيات:\n{str(e)}")
            
    def generate_report(self, report_type):
        """إنشاء التقرير المحدد"""
        try:
            # مسح البيانات الحالية
            for item in self.report_tree.get_children():
                self.report_tree.delete(item)
                
            if report_type == 'current_inventory':
                self.current_inventory_report()
            elif report_type == 'low_stock':
                self.low_stock_report()
            elif report_type == 'out_of_stock':
                self.out_of_stock_report()
            elif report_type == 'inventory_movements':
                self.inventory_movements_report()
            elif report_type == 'inventory_by_category':
                self.inventory_by_category_report()
            elif report_type == 'inventory_valuation':
                self.inventory_valuation_report()
                
            # تسجيل العملية
            report_names = {
                'current_inventory': 'تقرير المخزون الحالي',
                'low_stock': 'تقرير المنتجات الناقصة',
                'out_of_stock': 'تقرير المنتجات المنتهية',
                'inventory_movements': 'تقرير حركة المخزون',
                'inventory_by_category': 'تقرير المخزون حسب الفئة',
                'inventory_valuation': 'تقرير تقييم المخزون'
            }
            
            report_name = report_names.get(report_type, 'تقرير مخزون')
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                f"عرض {report_name}",
                f"تم عرض {report_name}",
                "inventory_reports"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def current_inventory_report(self):
        """تقرير المخزون الحالي"""
        self.current_report_label.config(text="تقرير المخزون الحالي")

        # تحديد الأعمدة
        columns = ('الرقم', 'اسم المنتج', 'الباركود', 'الفئة', 'الوحدة', 'الكمية الحالية',
                  'الحد الأدنى', 'سعر التكلفة', 'سعر البيع', 'قيمة المخزون', 'الحالة')

        self.report_tree['columns'] = columns

        # تحديد عناوين الأعمدة
        for col in columns:
            self.report_tree.heading(col, text=col)
            if col in ['الكمية الحالية', 'الحد الأدنى']:
                self.report_tree.column(col, width=80, anchor='center')
            elif col in ['سعر التكلفة', 'سعر البيع', 'قيمة المخزون']:
                self.report_tree.column(col, width=100, anchor='center')
            elif col == 'الرقم':
                self.report_tree.column(col, width=60, anchor='center')
            elif col == 'الحالة':
                self.report_tree.column(col, width=80, anchor='center')
            else:
                self.report_tree.column(col, width=120, anchor='center')

        # جلب البيانات
        query = """
            SELECT p.id, p.name, p.barcode, c.name as category_name, p.unit,
                   p.stock_quantity, p.min_stock_level, p.cost_price, p.selling_price,
                   (p.stock_quantity * p.cost_price) as inventory_value
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.is_active = 1
            ORDER BY p.name
        """

        products = self.db_manager.execute_query(query)

        total_value = 0
        for i, product in enumerate(products, 1):
            # تحديد حالة المخزون
            if product['stock_quantity'] == 0:
                status = 'منتهي'
                status_color = 'red'
            elif product['stock_quantity'] <= product['min_stock_level']:
                status = 'ناقص'
                status_color = 'orange'
            else:
                status = 'متوفر'
                status_color = 'green'

            inventory_value = product['inventory_value'] or 0
            total_value += inventory_value

            item_id = self.report_tree.insert('', 'end', values=(
                i,
                product['name'],
                product['barcode'] or '',
                product['category_name'] or 'غير محدد',
                product['unit'],
                f"{product['stock_quantity']:.2f}",
                product['min_stock_level'],
                f"{product['cost_price']:.2f}",
                f"{product['selling_price']:.2f}",
                f"{inventory_value:.2f}",
                status
            ))

            # تلوين الصف حسب الحالة
            if status == 'منتهي':
                self.report_tree.set(item_id, 'الحالة', status)
            elif status == 'ناقص':
                self.report_tree.set(item_id, 'الحالة', status)

        # إضافة صف الإجمالي
        self.report_tree.insert('', 'end', values=(
            '', 'الإجمالي', '', '', '', '', '', '', '', f"{total_value:.2f}", ''
        ))

    def low_stock_report(self):
        """تقرير المنتجات الناقصة"""
        self.current_report_label.config(text="تقرير المنتجات الناقصة")

        columns = ('الرقم', 'اسم المنتج', 'الفئة', 'الكمية الحالية', 'الحد الأدنى',
                  'الكمية المطلوبة', 'سعر التكلفة', 'القيمة المطلوبة')

        self.report_tree['columns'] = columns

        for col in columns:
            self.report_tree.heading(col, text=col)
            if col in ['الكمية الحالية', 'الحد الأدنى', 'الكمية المطلوبة']:
                self.report_tree.column(col, width=100, anchor='center')
            elif col in ['سعر التكلفة', 'القيمة المطلوبة']:
                self.report_tree.column(col, width=120, anchor='center')
            elif col == 'الرقم':
                self.report_tree.column(col, width=60, anchor='center')
            else:
                self.report_tree.column(col, width=150, anchor='center')

        query = """
            SELECT p.id, p.name, c.name as category_name, p.stock_quantity,
                   p.min_stock_level, p.cost_price
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.is_active = 1 AND p.stock_quantity <= p.min_stock_level AND p.stock_quantity > 0
            ORDER BY (p.min_stock_level - p.stock_quantity) DESC
        """

        products = self.db_manager.execute_query(query)

        total_required_value = 0
        for i, product in enumerate(products, 1):
            required_quantity = product['min_stock_level'] - product['stock_quantity']
            required_value = required_quantity * product['cost_price']
            total_required_value += required_value

            self.report_tree.insert('', 'end', values=(
                i,
                product['name'],
                product['category_name'] or 'غير محدد',
                f"{product['stock_quantity']:.2f}",
                product['min_stock_level'],
                f"{required_quantity:.2f}",
                f"{product['cost_price']:.2f}",
                f"{required_value:.2f}"
            ))

        # إضافة صف الإجمالي
        self.report_tree.insert('', 'end', values=(
            '', 'الإجمالي', '', '', '', '', '', f"{total_required_value:.2f}"
        ))

    def out_of_stock_report(self):
        """تقرير المنتجات المنتهية"""
        self.current_report_label.config(text="تقرير المنتجات المنتهية")

        columns = ('الرقم', 'اسم المنتج', 'الفئة', 'الحد الأدنى', 'آخر حركة',
                  'سعر التكلفة', 'سعر البيع', 'الأولوية')

        self.report_tree['columns'] = columns

        for col in columns:
            self.report_tree.heading(col, text=col)
            if col in ['الحد الأدنى', 'سعر التكلفة', 'سعر البيع']:
                self.report_tree.column(col, width=100, anchor='center')
            elif col in ['آخر حركة', 'الأولوية']:
                self.report_tree.column(col, width=120, anchor='center')
            elif col == 'الرقم':
                self.report_tree.column(col, width=60, anchor='center')
            else:
                self.report_tree.column(col, width=150, anchor='center')

        query = """
            SELECT p.id, p.name, c.name as category_name, p.min_stock_level,
                   p.cost_price, p.selling_price,
                   (SELECT MAX(movement_date) FROM inventory_movements
                    WHERE product_id = p.id) as last_movement
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.is_active = 1 AND p.stock_quantity = 0
            ORDER BY p.min_stock_level DESC
        """

        products = self.db_manager.execute_query(query)

        for i, product in enumerate(products, 1):
            # تحديد الأولوية بناءً على الحد الأدنى
            if product['min_stock_level'] >= 10:
                priority = 'عالية'
            elif product['min_stock_level'] >= 5:
                priority = 'متوسطة'
            else:
                priority = 'منخفضة'

            last_movement = product['last_movement'] or 'غير محدد'

            self.report_tree.insert('', 'end', values=(
                i,
                product['name'],
                product['category_name'] or 'غير محدد',
                product['min_stock_level'],
                last_movement,
                f"{product['cost_price']:.2f}",
                f"{product['selling_price']:.2f}",
                priority
            ))

    def inventory_movements_report(self):
        """تقرير حركة المخزون"""
        self.current_report_label.config(text="تقرير حركة المخزون")

        columns = ('الرقم', 'التاريخ', 'المنتج', 'نوع الحركة', 'الكمية', 'المرجع',
                  'رقم المرجع', 'المستخدم', 'ملاحظات')

        self.report_tree['columns'] = columns

        for col in columns:
            self.report_tree.heading(col, text=col)
            if col in ['الكمية', 'رقم المرجع']:
                self.report_tree.column(col, width=80, anchor='center')
            elif col in ['التاريخ', 'نوع الحركة', 'المرجع']:
                self.report_tree.column(col, width=100, anchor='center')
            elif col == 'الرقم':
                self.report_tree.column(col, width=60, anchor='center')
            else:
                self.report_tree.column(col, width=120, anchor='center')

        query = """
            SELECT im.id, im.movement_date, p.name as product_name, im.movement_type,
                   im.quantity, im.reference_type, im.reference_id, u.name as user_name,
                   im.notes
            FROM inventory_movements im
            JOIN products p ON im.product_id = p.id
            LEFT JOIN users u ON im.user_id = u.id
            ORDER BY im.movement_date DESC, im.id DESC
            LIMIT 500
        """

        movements = self.db_manager.execute_query(query)

        movement_types = {
            'in': 'دخول',
            'out': 'خروج',
            'adjustment': 'تعديل'
        }

        reference_types = {
            'sales': 'مبيعات',
            'purchase': 'مشتريات',
            'adjustment': 'تعديل',
            'return': 'مرتجع'
        }

        for i, movement in enumerate(movements, 1):
            movement_type_text = movement_types.get(movement['movement_type'], movement['movement_type'])
            reference_type_text = reference_types.get(movement['reference_type'], movement['reference_type'] or '')

            self.report_tree.insert('', 'end', values=(
                i,
                movement['movement_date'],
                movement['product_name'],
                movement_type_text,
                f"{movement['quantity']:.2f}",
                reference_type_text,
                movement['reference_id'] or '',
                movement['user_name'] or '',
                movement['notes'] or ''
            ))

    def inventory_by_category_report(self):
        """تقرير المخزون حسب الفئة"""
        self.current_report_label.config(text="تقرير المخزون حسب الفئة")

        columns = ('الرقم', 'اسم الفئة', 'عدد المنتجات', 'إجمالي الكمية',
                  'المنتجات المتوفرة', 'المنتجات الناقصة', 'المنتجات المنتهية',
                  'قيمة المخزون', 'متوسط سعر البيع')

        self.report_tree['columns'] = columns

        for col in columns:
            self.report_tree.heading(col, text=col)
            if col in ['عدد المنتجات', 'المنتجات المتوفرة', 'المنتجات الناقصة', 'المنتجات المنتهية']:
                self.report_tree.column(col, width=100, anchor='center')
            elif col in ['إجمالي الكمية', 'قيمة المخزون', 'متوسط سعر البيع']:
                self.report_tree.column(col, width=120, anchor='center')
            elif col == 'الرقم':
                self.report_tree.column(col, width=60, anchor='center')
            else:
                self.report_tree.column(col, width=150, anchor='center')

        query = """
            SELECT c.id, c.name as category_name,
                   COUNT(p.id) as product_count,
                   COALESCE(SUM(p.stock_quantity), 0) as total_quantity,
                   COUNT(CASE WHEN p.stock_quantity > p.min_stock_level THEN 1 END) as available_products,
                   COUNT(CASE WHEN p.stock_quantity <= p.min_stock_level AND p.stock_quantity > 0 THEN 1 END) as low_stock_products,
                   COUNT(CASE WHEN p.stock_quantity = 0 THEN 1 END) as out_of_stock_products,
                   COALESCE(SUM(p.stock_quantity * p.cost_price), 0) as inventory_value,
                   COALESCE(AVG(p.selling_price), 0) as avg_selling_price
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
            GROUP BY c.id, c.name
            HAVING COUNT(p.id) > 0
            ORDER BY inventory_value DESC
        """

        categories = self.db_manager.execute_query(query)

        total_products = 0
        total_quantity = 0
        total_value = 0

        for i, category in enumerate(categories, 1):
            total_products += category['product_count']
            total_quantity += category['total_quantity']
            total_value += category['inventory_value']

            self.report_tree.insert('', 'end', values=(
                i,
                category['category_name'],
                category['product_count'],
                f"{category['total_quantity']:.2f}",
                category['available_products'],
                category['low_stock_products'],
                category['out_of_stock_products'],
                f"{category['inventory_value']:.2f}",
                f"{category['avg_selling_price']:.2f}"
            ))

        # إضافة صف الإجمالي
        self.report_tree.insert('', 'end', values=(
            '', 'الإجمالي', total_products, f"{total_quantity:.2f}",
            '', '', '', f"{total_value:.2f}", ''
        ))

    def inventory_valuation_report(self):
        """تقرير تقييم المخزون"""
        self.current_report_label.config(text="تقرير تقييم المخزون")

        columns = ('الرقم', 'اسم المنتج', 'الفئة', 'الكمية', 'سعر التكلفة',
                  'قيمة التكلفة', 'سعر البيع', 'قيمة البيع', 'الربح المتوقع', 'هامش الربح %')

        self.report_tree['columns'] = columns

        for col in columns:
            self.report_tree.heading(col, text=col)
            if col in ['الكمية']:
                self.report_tree.column(col, width=80, anchor='center')
            elif col in ['سعر التكلفة', 'قيمة التكلفة', 'سعر البيع', 'قيمة البيع', 'الربح المتوقع']:
                self.report_tree.column(col, width=100, anchor='center')
            elif col == 'هامش الربح %':
                self.report_tree.column(col, width=90, anchor='center')
            elif col == 'الرقم':
                self.report_tree.column(col, width=60, anchor='center')
            else:
                self.report_tree.column(col, width=120, anchor='center')

        query = """
            SELECT p.id, p.name, c.name as category_name, p.stock_quantity,
                   p.cost_price, p.selling_price
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.is_active = 1 AND p.stock_quantity > 0
            ORDER BY (p.stock_quantity * (p.selling_price - p.cost_price)) DESC
        """

        products = self.db_manager.execute_query(query)

        total_cost_value = 0
        total_selling_value = 0
        total_expected_profit = 0

        for i, product in enumerate(products, 1):
            cost_value = product['stock_quantity'] * product['cost_price']
            selling_value = product['stock_quantity'] * product['selling_price']
            expected_profit = selling_value - cost_value

            if product['selling_price'] > 0:
                profit_margin = ((product['selling_price'] - product['cost_price']) / product['selling_price']) * 100
            else:
                profit_margin = 0

            total_cost_value += cost_value
            total_selling_value += selling_value
            total_expected_profit += expected_profit

            self.report_tree.insert('', 'end', values=(
                i,
                product['name'],
                product['category_name'] or 'غير محدد',
                f"{product['stock_quantity']:.2f}",
                f"{product['cost_price']:.2f}",
                f"{cost_value:.2f}",
                f"{product['selling_price']:.2f}",
                f"{selling_value:.2f}",
                f"{expected_profit:.2f}",
                f"{profit_margin:.1f}%"
            ))

        # حساب هامش الربح الإجمالي
        overall_margin = 0
        if total_selling_value > 0:
            overall_margin = (total_expected_profit / total_selling_value) * 100

        # إضافة صف الإجمالي
        self.report_tree.insert('', 'end', values=(
            '', 'الإجمالي', '', '', '', f"{total_cost_value:.2f}",
            '', f"{total_selling_value:.2f}", f"{total_expected_profit:.2f}", f"{overall_margin:.1f}%"
        ))
