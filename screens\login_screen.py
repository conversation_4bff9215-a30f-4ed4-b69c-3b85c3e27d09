# -*- coding: utf-8 -*-
"""
شاشة تسجيل الدخول
"""

import tkinter as tk
from tkinter import ttk, messagebox
from config.settings import COLORS, FONTS, COMPANY_INFO
from utils.database_manager import DatabaseManager

class LoginScreen:
    """كلاس شاشة تسجيل الدخول"""
    
    def __init__(self, parent, on_success_callback):
        self.parent = parent
        self.on_success_callback = on_success_callback
        self.db_manager = DatabaseManager()
        
        # إخفاء النافذة الرئيسية مؤقتاً
        self.parent.withdraw()
        
        # إنشاء نافذة تسجيل الدخول
        self.login_window = tk.Toplevel(self.parent)
        self.setup_login_window()
        self.create_widgets()
        
    def setup_login_window(self):
        """إعداد نافذة تسجيل الدخول"""
        self.login_window.title("تسجيل الدخول - " + COMPANY_INFO['name'])
        self.login_window.geometry("400x500")
        self.login_window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.login_window.transient(self.parent)
        self.login_window.grab_set()
        
        # إعداد الألوان
        self.login_window.configure(bg=COLORS['background'])
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.login_window.update_idletasks()
        width = 400
        height = 500
        x = (self.login_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.login_window.winfo_screenheight() // 2) - (height // 2)
        self.login_window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.login_window, bg=COLORS['background'])
        main_frame.pack(expand=True, fill='both', padx=30, pady=30)
        
        # شعار الشركة أو العنوان
        title_label = tk.Label(
            main_frame,
            text=COMPANY_INFO['name'],
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 10))
        
        subtitle_label = tk.Label(
            main_frame,
            text="برنامج محاسبة المبيعات والمخازن",
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['text']
        )
        subtitle_label.pack(pady=(0, 30))
        
        # إطار تسجيل الدخول
        login_frame = tk.LabelFrame(
            main_frame,
            text="تسجيل الدخول",
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['primary'],
            padx=20,
            pady=20
        )
        login_frame.pack(fill='x', pady=(0, 20))
        
        # اسم المستخدم
        tk.Label(
            login_frame,
            text="اسم المستخدم:",
            font=FONTS['normal'],
            bg=COLORS['background'],
            fg=COLORS['text']
        ).pack(anchor='e', pady=(0, 5))
        
        self.username_entry = tk.Entry(
            login_frame,
            font=FONTS['normal'],
            width=25,
            justify='right'
        )
        self.username_entry.pack(pady=(0, 15))
        self.username_entry.focus()
        
        # كلمة المرور
        tk.Label(
            login_frame,
            text="كلمة المرور:",
            font=FONTS['normal'],
            bg=COLORS['background'],
            fg=COLORS['text']
        ).pack(anchor='e', pady=(0, 5))
        
        self.password_entry = tk.Entry(
            login_frame,
            font=FONTS['normal'],
            width=25,
            show='*',
            justify='right'
        )
        self.password_entry.pack(pady=(0, 20))
        
        # زر تسجيل الدخول
        login_button = tk.Button(
            login_frame,
            text="تسجيل الدخول",
            font=FONTS['button'],
            bg=COLORS['primary'],
            fg='white',
            width=20,
            height=2,
            command=self.login
        )
        login_button.pack(pady=(0, 10))

        # رسالة أمان
        security_label = tk.Label(
            main_frame,
            text="يرجى استخدام بيانات تسجيل الدخول الصحيحة",
            font=FONTS['small'],
            bg=COLORS['background'],
            fg=COLORS['secondary']
        )
        security_label.pack(pady=(10, 0))

        # ربط مفتاح Enter بتسجيل الدخول
        self.login_window.bind('<Return>', lambda event: self.login())
        
    def login(self):
        """معالج تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
            
        try:
            user_data = self.db_manager.authenticate_user(username, password)
            
            if user_data:
                # إغلاق نافذة تسجيل الدخول
                self.login_window.destroy()
                
                # إظهار النافذة الرئيسية
                self.parent.deiconify()
                
                # استدعاء دالة النجاح
                self.on_success_callback(user_data)
                
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_entry.delete(0, tk.END)
                self.username_entry.focus()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تسجيل الدخول:\n{str(e)}")
            
    def __del__(self):
        """تنظيف الموارد"""
        try:
            if hasattr(self, 'login_window') and self.login_window.winfo_exists():
                self.login_window.destroy()
        except:
            pass
