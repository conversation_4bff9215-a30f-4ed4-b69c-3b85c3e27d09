# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية للبرنامج
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from config.settings import COLORS, FONTS, COMPANY_INFO
from utils.helpers import check_user_permission, get_user_role_text, apply_rtl_settings
from utils.arabic_support import ArabicSupport
from screens.users_management import UsersManagement

class MainInterface:
    """كلاس الواجهة الرئيسية"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.setup_main_interface()
        
    def setup_main_interface(self):
        """إعداد الواجهة الرئيسية"""
        # مسح المحتوى الحالي
        for widget in self.parent.winfo_children():
            widget.destroy()

        # إعداد النافذة الرئيسية
        self.parent.configure(bg=COLORS['background'])

        # تعيين اتجاه النص من اليمين إلى اليسار
        self.parent.option_add('*Text.direction', 'rtl')
        self.parent.option_add('*Entry.justify', 'right')
        
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()

        # تطبيق إعدادات RTL على جميع العناصر
        apply_rtl_settings(self.parent)
        ArabicSupport.setup_window_rtl(self.parent)
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.parent)
        self.parent.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="النسخ الاحتياطي", command=self.backup_database)
        file_menu.add_command(label="استعادة النسخة الاحتياطية", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="إعدادات البرنامج", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.parent.quit)
        
        # قائمة المبيعات
        if check_user_permission(self.current_user['role'], 'sales_create') or check_user_permission(self.current_user['role'], 'sales_view'):
            sales_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المبيعات", menu=sales_menu)

            if check_user_permission(self.current_user['role'], 'sales_create'):
                sales_menu.add_command(label="فاتورة مبيعات جديدة", command=self.new_sales_invoice)

            if check_user_permission(self.current_user['role'], 'sales_view'):
                sales_menu.add_command(label="قائمة فواتير المبيعات", command=self.sales_invoices_list)
                sales_menu.add_command(label="المرتجعات", command=self.sales_returns)

        # قائمة المشتريات
        if check_user_permission(self.current_user['role'], 'purchases_create') or check_user_permission(self.current_user['role'], 'purchases_management'):
            purchases_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المشتريات", menu=purchases_menu)

            if check_user_permission(self.current_user['role'], 'purchases_create'):
                purchases_menu.add_command(label="فاتورة شراء جديدة", command=self.new_purchase_invoice)

            if check_user_permission(self.current_user['role'], 'purchases_management'):
                purchases_menu.add_command(label="قائمة فواتير المشتريات", command=self.purchase_invoices_list)
                purchases_menu.add_command(label="المرتجعات", command=self.purchase_returns)

        # قائمة المخزون
        if check_user_permission(self.current_user['role'], 'products_management') or check_user_permission(self.current_user['role'], 'inventory_management'):
            inventory_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المخزون", menu=inventory_menu)

            if check_user_permission(self.current_user['role'], 'products_management'):
                inventory_menu.add_command(label="إدارة المنتجات", command=self.manage_products)
                inventory_menu.add_command(label="إدارة الفئات", command=self.manage_categories)

            if check_user_permission(self.current_user['role'], 'inventory_management'):
                inventory_menu.add_command(label="جرد المخزون", command=self.inventory_count)
                inventory_menu.add_command(label="حركات المخزون", command=self.inventory_movements)

        # قائمة العملاء
        if check_user_permission(self.current_user['role'], 'customers_management'):
            customers_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="العملاء", menu=customers_menu)
            customers_menu.add_command(label="إدارة العملاء", command=self.manage_customers)
            customers_menu.add_command(label="كشف حساب عميل", command=self.customer_statement)

        # قائمة الموردين
        if check_user_permission(self.current_user['role'], 'suppliers_management'):
            suppliers_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الموردين", menu=suppliers_menu)
            suppliers_menu.add_command(label="إدارة الموردين", command=self.manage_suppliers)
            suppliers_menu.add_command(label="كشف حساب مورد", command=self.supplier_statement)

        # قائمة التقارير
        if check_user_permission(self.current_user['role'], 'reports_view'):
            reports_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="التقارير", menu=reports_menu)
            reports_menu.add_command(label="تقرير المبيعات", command=self.sales_report)
            reports_menu.add_command(label="تقرير المشتريات", command=self.purchases_report)
            reports_menu.add_command(label="تقرير الأرباح والخسائر", command=self.profit_loss_report)
            reports_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        
        # قائمة الإدارة (للمدير فقط)
        if self.current_user['role'] == 'admin':
            admin_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الإدارة", menu=admin_menu)
            admin_menu.add_command(label="إدارة المستخدمين", command=self.manage_users)
            admin_menu.add_command(label="صلاحيات المستخدمين", command=self.user_permissions)
            admin_menu.add_command(label="سجل العمليات", command=self.activity_log)
            admin_menu.add_separator()
            admin_menu.add_command(label="إدارة النسخ الاحتياطي", command=self.backup_management)
            admin_menu.add_command(label="استعادة نسخة احتياطية", command=self.restore_database)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.about)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.parent, bg=COLORS['primary'], height=30)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # معلومات المستخدم
        user_info = f"المستخدم: {self.current_user['name']} | الدور: {get_user_role_text(self.current_user['role'])}"
        self.user_label = tk.Label(
            self.status_frame,
            text=user_info,
            bg=COLORS['primary'],
            fg='white',
            font=FONTS['small']
        )
        self.user_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # معلومات الشركة
        company_info = f"{COMPANY_INFO['name']} | {COMPANY_INFO['phone']}"
        self.company_label = tk.Label(
            self.status_frame,
            text=company_info,
            bg=COLORS['primary'],
            fg='white',
            font=FONTS['small']
        )
        self.company_label.pack(side=tk.RIGHT, padx=10, pady=5)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.parent, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            main_frame,
            text=f"مرحباً {self.current_user['name']}",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار السريعة
        quick_buttons_frame = tk.LabelFrame(
            main_frame,
            text="الوصول السريع",
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        quick_buttons_frame.pack(fill='x', pady=(0, 20))
        
        # إنشاء الأزرار السريعة
        self.create_quick_buttons(quick_buttons_frame)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(
            main_frame,
            text="إحصائيات سريعة",
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        stats_frame.pack(fill='both', expand=True)
        
        # إنشاء الإحصائيات
        self.create_statistics(stats_frame)
        
    def create_quick_buttons(self, parent):
        """إنشاء الأزرار السريعة"""
        buttons_frame = tk.Frame(parent, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        # صف الأزرار الأول
        row1_frame = tk.Frame(buttons_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', pady=(0, 10))
        
        if check_user_permission(self.current_user['role'], 'sales_create'):
            sales_btn = tk.Button(
                row1_frame,
                text="فاتورة مبيعات جديدة",
                font=FONTS['button'],
                bg=COLORS['success'],
                fg='white',
                width=20,
                height=2,
                command=self.new_sales_invoice
            )
            sales_btn.pack(side=tk.RIGHT, padx=5)

        if check_user_permission(self.current_user['role'], 'purchases_create'):
            purchase_btn = tk.Button(
                row1_frame,
                text="فاتورة شراء جديدة",
                font=FONTS['button'],
                bg=COLORS['warning'],
                fg='white',
                width=20,
                height=2,
                command=self.new_purchase_invoice
            )
            purchase_btn.pack(side=tk.RIGHT, padx=5)

        if check_user_permission(self.current_user['role'], 'products_management'):
            products_btn = tk.Button(
                row1_frame,
                text="إدارة المنتجات",
                font=FONTS['button'],
                bg=COLORS['info'],
                fg='white',
                width=20,
                height=2,
                command=self.manage_products
            )
            products_btn.pack(side=tk.RIGHT, padx=5)
        
        # صف الأزرار الثاني
        row2_frame = tk.Frame(buttons_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x')
        
        if check_user_permission(self.current_user['role'], 'customers_management'):
            customers_btn = tk.Button(
                row2_frame,
                text="إدارة العملاء",
                font=FONTS['button'],
                bg=COLORS['primary'],
                fg='white',
                width=20,
                height=2,
                command=self.manage_customers
            )
            customers_btn.pack(side=tk.RIGHT, padx=5)

        if check_user_permission(self.current_user['role'], 'suppliers_management'):
            suppliers_btn = tk.Button(
                row2_frame,
                text="إدارة الموردين",
                font=FONTS['button'],
                bg=COLORS['secondary'],
                fg='white',
                width=20,
                height=2,
                command=self.manage_suppliers
            )
            suppliers_btn.pack(side=tk.RIGHT, padx=5)

        if check_user_permission(self.current_user['role'], 'reports_view'):
            reports_btn = tk.Button(
                row2_frame,
                text="التقارير",
                font=FONTS['button'],
                bg=COLORS['dark'],
                fg='white',
                width=20,
                height=2,
                command=self.sales_report
            )
            reports_btn.pack(side=tk.RIGHT, padx=5)
            
    def create_statistics(self, parent):
        """إنشاء لوحة الإحصائيات التفاعلية"""
        stats_frame = tk.Frame(parent, bg=COLORS['background'])
        stats_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان لوحة الإحصائيات
        title_frame = tk.Frame(stats_frame, bg=COLORS['background'])
        title_frame.pack(fill='x', pady=(0, 20))

        title_label = tk.Label(
            title_frame,
            text="📊 لوحة الإحصائيات والمؤشرات",
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(side=tk.LEFT)

        # زر تحديث الإحصائيات
        refresh_btn = tk.Button(
            title_frame,
            text="🔄 تحديث",
            font=FONTS['small'],
            bg=COLORS['info'],
            fg='white',
            width=10,
            command=self.refresh_statistics
        )
        refresh_btn.pack(side=tk.RIGHT)

        # إطار الإحصائيات الرئيسي
        main_stats_frame = tk.Frame(stats_frame, bg=COLORS['background'])
        main_stats_frame.pack(fill='both', expand=True)

        # الصف الأول - إحصائيات المبيعات والمشتريات
        row1_frame = tk.Frame(main_stats_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', pady=(0, 15))

        self.create_sales_stats(row1_frame)
        self.create_purchases_stats(row1_frame)

        # الصف الثاني - إحصائيات المخزون والعملاء
        row2_frame = tk.Frame(main_stats_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', pady=(0, 15))

        self.create_inventory_stats(row2_frame)
        self.create_customers_stats(row2_frame)

        # الصف الثالث - إحصائيات مالية وعامة
        row3_frame = tk.Frame(main_stats_frame, bg=COLORS['background'])
        row3_frame.pack(fill='x', pady=(0, 15))

        self.create_financial_stats(row3_frame)
        self.create_system_stats(row3_frame)

        # تحميل الإحصائيات عند الإنشاء
        self.refresh_statistics()

    def create_stat_card(self, parent, title, value, subtitle="", color=COLORS['primary'], icon="📊"):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.LabelFrame(
            parent,
            text=f"{icon} {title}",
            font=FONTS['normal'],
            bg=COLORS['background'],
            fg=color,
            relief='raised',
            bd=2
        )
        card_frame.pack(side=tk.LEFT, fill='both', expand=True, padx=5)

        # القيمة الرئيسية
        value_label = tk.Label(
            card_frame,
            text=str(value),
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=color
        )
        value_label.pack(pady=(10, 5))

        # النص الفرعي
        if subtitle:
            subtitle_label = tk.Label(
                card_frame,
                text=subtitle,
                font=FONTS['small'],
                bg=COLORS['background'],
                fg=COLORS['text']
            )
            subtitle_label.pack(pady=(0, 10))

        return card_frame

    def create_sales_stats(self, parent):
        """إنشاء إحصائيات المبيعات"""
        try:
            from utils.database_manager import DatabaseManager
            db_manager = DatabaseManager()

            # مبيعات اليوم
            today_sales_query = """
                SELECT COALESCE(SUM(total_amount), 0) as today_sales
                FROM sales_invoices
                WHERE DATE(invoice_date) = DATE('now', 'localtime')
            """
            result = db_manager.execute_query(today_sales_query)
            today_sales = result[0]['today_sales'] if result else 0

            # مبيعات الشهر
            month_sales_query = """
                SELECT COALESCE(SUM(total_amount), 0) as month_sales
                FROM sales_invoices
                WHERE strftime('%Y-%m', invoice_date) = strftime('%Y-%m', 'now', 'localtime')
            """
            result = db_manager.execute_query(month_sales_query)
            month_sales = result[0]['month_sales'] if result else 0

            # عدد الفواتير اليوم
            today_invoices_query = """
                SELECT COUNT(*) as count
                FROM sales_invoices
                WHERE DATE(invoice_date) = DATE('now', 'localtime')
            """
            result = db_manager.execute_query(today_invoices_query)
            today_invoices = result[0]['count'] if result else 0

            self.create_stat_card(
                parent,
                "مبيعات اليوم",
                f"{today_sales:,.0f} ريال",
                f"{today_invoices} فاتورة",
                COLORS['success'],
                "💰"
            )

            self.create_stat_card(
                parent,
                "مبيعات الشهر",
                f"{month_sales:,.0f} ريال",
                "إجمالي الشهر الحالي",
                COLORS['info'],
                "📈"
            )

        except Exception as e:
            print(f"خطأ في تحميل إحصائيات المبيعات: {str(e)}")
            self.create_stat_card(parent, "مبيعات اليوم", "خطأ", "", COLORS['danger'], "❌")

    def create_purchases_stats(self, parent):
        """إنشاء إحصائيات المشتريات"""
        try:
            from utils.database_manager import DatabaseManager
            db_manager = DatabaseManager()

            # مشتريات اليوم
            today_purchases_query = """
                SELECT COALESCE(SUM(total_amount), 0) as today_purchases
                FROM purchase_invoices
                WHERE DATE(invoice_date) = DATE('now', 'localtime')
            """
            result = db_manager.execute_query(today_purchases_query)
            today_purchases = result[0]['today_purchases'] if result else 0

            # مشتريات الشهر
            month_purchases_query = """
                SELECT COALESCE(SUM(total_amount), 0) as month_purchases
                FROM purchase_invoices
                WHERE strftime('%Y-%m', invoice_date) = strftime('%Y-%m', 'now', 'localtime')
            """
            result = db_manager.execute_query(month_purchases_query)
            month_purchases = result[0]['month_purchases'] if result else 0

            self.create_stat_card(
                parent,
                "مشتريات اليوم",
                f"{today_purchases:,.0f} ريال",
                "إجمالي اليوم",
                COLORS['warning'],
                "🛒"
            )

            self.create_stat_card(
                parent,
                "مشتريات الشهر",
                f"{month_purchases:,.0f} ريال",
                "إجمالي الشهر الحالي",
                COLORS['secondary'],
                "📦"
            )

        except Exception as e:
            print(f"خطأ في تحميل إحصائيات المشتريات: {str(e)}")
            self.create_stat_card(parent, "مشتريات اليوم", "خطأ", "", COLORS['danger'], "❌")

    def create_inventory_stats(self, parent):
        """إنشاء إحصائيات المخزون"""
        try:
            from utils.database_manager import DatabaseManager
            db_manager = DatabaseManager()

            # إجمالي المنتجات
            total_products_query = """
                SELECT COUNT(*) as count
                FROM products
                WHERE is_active = 1
            """
            result = db_manager.execute_query(total_products_query)
            total_products = result[0]['count'] if result else 0

            # المنتجات المنخفضة
            low_stock_query = """
                SELECT COUNT(*) as count
                FROM products
                WHERE is_active = 1 AND stock_quantity <= min_stock_level
            """
            result = db_manager.execute_query(low_stock_query)
            low_stock = result[0]['count'] if result else 0

            # قيمة المخزون
            inventory_value_query = """
                SELECT COALESCE(SUM(stock_quantity * cost_price), 0) as total_value
                FROM products
                WHERE is_active = 1
            """
            result = db_manager.execute_query(inventory_value_query)
            inventory_value = result[0]['total_value'] if result else 0

            self.create_stat_card(
                parent,
                "إجمالي المنتجات",
                f"{total_products:,}",
                f"{low_stock} منتج منخفض",
                COLORS['info'],
                "📦"
            )

            self.create_stat_card(
                parent,
                "قيمة المخزون",
                f"{inventory_value:,.0f} ريال",
                "إجمالي قيمة المخزون",
                COLORS['primary'],
                "💎"
            )

        except Exception as e:
            print(f"خطأ في تحميل إحصائيات المخزون: {str(e)}")
            self.create_stat_card(parent, "المخزون", "خطأ", "", COLORS['danger'], "❌")

    def create_customers_stats(self, parent):
        """إنشاء إحصائيات العملاء"""
        try:
            from utils.database_manager import DatabaseManager
            db_manager = DatabaseManager()

            # إجمالي العملاء
            total_customers_query = """
                SELECT COUNT(*) as count
                FROM customers
                WHERE is_active = 1
            """
            result = db_manager.execute_query(total_customers_query)
            total_customers = result[0]['count'] if result else 0

            # العملاء المدينون (تقديري - بناءً على الفواتير غير المدفوعة)
            debtors_query = """
                SELECT COUNT(DISTINCT customer_id) as count,
                       COALESCE(SUM(remaining_amount), 0) as total_debt
                FROM sales_invoices
                WHERE customer_id IS NOT NULL AND remaining_amount > 0
            """
            result = db_manager.execute_query(debtors_query)
            debtors_count = result[0]['count'] if result else 0
            total_debt = result[0]['total_debt'] if result else 0

            self.create_stat_card(
                parent,
                "إجمالي العملاء",
                f"{total_customers:,}",
                f"{debtors_count} عميل مدين",
                COLORS['success'],
                "👥"
            )

            self.create_stat_card(
                parent,
                "إجمالي الديون",
                f"{total_debt:,.0f} ريال",
                "مستحق من العملاء",
                COLORS['warning'],
                "💳"
            )

        except Exception as e:
            print(f"خطأ في تحميل إحصائيات العملاء: {str(e)}")
            self.create_stat_card(parent, "العملاء", "خطأ", "", COLORS['danger'], "❌")

    def create_financial_stats(self, parent):
        """إنشاء الإحصائيات المالية"""
        try:
            from utils.database_manager import DatabaseManager
            db_manager = DatabaseManager()

            # استعلام مبسط للربح
            sales_query = """
                SELECT COALESCE(SUM(total_amount), 0) as sales_total
                FROM sales_invoices
                WHERE strftime('%Y-%m', invoice_date) = strftime('%Y-%m', 'now', 'localtime')
            """
            purchases_query = """
                SELECT COALESCE(SUM(total_amount), 0) as purchases_total
                FROM purchase_invoices
                WHERE strftime('%Y-%m', invoice_date) = strftime('%Y-%m', 'now', 'localtime')
            """

            sales_result = db_manager.execute_query(sales_query)
            purchases_result = db_manager.execute_query(purchases_query)

            sales_total = sales_result[0]['sales_total'] if sales_result else 0
            purchases_total = purchases_result[0]['purchases_total'] if purchases_result else 0
            estimated_profit = sales_total - purchases_total

            # النقد في الصندوق (تقديري - بناءً على المبيعات النقدية)
            cash_query = """
                SELECT COALESCE(SUM(paid_amount), 0) as cash_sales
                FROM sales_invoices
                WHERE DATE(invoice_date) = DATE('now', 'localtime')
                AND payment_status = 'paid'
            """
            result = db_manager.execute_query(cash_query)
            cash_sales = result[0]['cash_sales'] if result else 0

            self.create_stat_card(
                parent,
                "الربح المقدر",
                f"{estimated_profit:,.0f} ريال",
                "للشهر الحالي",
                COLORS['success'] if estimated_profit >= 0 else COLORS['danger'],
                "💰"
            )

            self.create_stat_card(
                parent,
                "المبيعات النقدية",
                f"{cash_sales:,.0f} ريال",
                "مبيعات نقدية اليوم",
                COLORS['info'],
                "💵"
            )

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات المالية: {str(e)}")
            self.create_stat_card(parent, "الإحصائيات المالية", "خطأ", "", COLORS['danger'], "❌")

    def create_system_stats(self, parent):
        """إنشاء إحصائيات النظام"""
        try:
            from utils.database_manager import DatabaseManager
            from datetime import datetime
            import os

            db_manager = DatabaseManager()

            # حجم قاعدة البيانات
            db_size = 0
            try:
                from config.settings import DATABASE_PATH
                if os.path.exists(DATABASE_PATH):
                    db_size = os.path.getsize(DATABASE_PATH) / (1024 * 1024)  # MB
            except:
                pass

            # عدد المستخدمين النشطين
            users_query = """
                SELECT COUNT(*) as count
                FROM users
                WHERE is_active = 1
            """
            result = db_manager.execute_query(users_query)
            active_users = result[0]['count'] if result else 0

            # آخر نسخة احتياطية
            backup_info = "لا توجد"
            try:
                from utils.backup_manager import BackupManager
                backup_manager = BackupManager(db_manager)
                backup_list = backup_manager.get_backup_list()
                if backup_list:
                    last_backup = backup_list[0]['created_at']
                    days_ago = (datetime.now() - last_backup).days
                    backup_info = f"منذ {days_ago} يوم"
            except:
                pass

            self.create_stat_card(
                parent,
                "حجم قاعدة البيانات",
                f"{db_size:.1f} MB",
                f"{active_users} مستخدم نشط",
                COLORS['secondary'],
                "💾"
            )

            self.create_stat_card(
                parent,
                "آخر نسخة احتياطية",
                backup_info,
                "حالة النسخ الاحتياطي",
                COLORS['info'],
                "🔄"
            )

        except Exception as e:
            print(f"خطأ في تحميل إحصائيات النظام: {str(e)}")
            self.create_stat_card(parent, "إحصائيات النظام", "خطأ", "", COLORS['danger'], "❌")

    def refresh_statistics(self):
        """تحديث جميع الإحصائيات"""
        try:
            # سيتم استدعاء هذه الدالة عند الضغط على زر التحديث
            # الإحصائيات تتحدث تلقائياً عند إنشاء البطاقات
            print("تم تحديث الإحصائيات")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
        
    # دوال معالجة الأحداث (سيتم تطويرها لاحقاً)
    def new_sales_invoice(self):
        """فتح شاشة فاتورة مبيعات جديدة"""
        from screens.sales_management import SalesInvoiceDialog
        SalesInvoiceDialog(self.parent, self.current_user)

    def sales_invoices_list(self):
        """فتح شاشة قائمة فواتير المبيعات"""
        from screens.sales_invoices_list import SalesInvoicesList
        SalesInvoicesList(self.parent, self.current_user)
        
    def new_purchase_invoice(self):
        """فتح شاشة فاتورة شراء جديدة"""
        from screens.purchases_management import PurchaseInvoiceDialog
        PurchaseInvoiceDialog(self.parent, self.current_user)

    def purchase_invoices_list(self):
        """فتح شاشة قائمة فواتير المشتريات"""
        from screens.purchase_invoices_list import PurchaseInvoicesList
        PurchaseInvoicesList(self.parent, self.current_user)
        
    def manage_products(self):
        """فتح شاشة إدارة المنتجات"""
        from screens.products_management import ProductsManagement
        ProductsManagement(self.parent, self.current_user)
        
    def manage_customers(self):
        """فتح شاشة إدارة العملاء"""
        from screens.customers_management import CustomersManagement
        CustomersManagement(self.parent, self.current_user)

    def manage_suppliers(self):
        """فتح شاشة إدارة الموردين"""
        from screens.suppliers_management import SuppliersManagement
        SuppliersManagement(self.parent, self.current_user)
        
    def sales_report(self):
        """فتح شاشة التقارير"""
        from screens.reports_management import ReportsManagement
        ReportsManagement(self.parent, self.current_user)
        
    def manage_users(self):
        """فتح شاشة إدارة المستخدمين"""
        UsersManagement(self.parent, self.current_user)
        
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تأكيد", "هل تريد تسجيل الخروج؟")
        if result:
            # تسجيل عملية تسجيل الخروج
            try:
                from utils.helpers import log_user_activity
                from utils.database_manager import DatabaseManager
                from datetime import datetime

                db_manager = DatabaseManager()
                logout_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                details = f"تسجيل خروج في {logout_time}"
                log_user_activity(
                    db_manager,
                    self.current_user['id'],
                    "تسجيل الخروج",
                    details
                )
            except Exception as e:
                print(f"خطأ في تسجيل عملية الخروج: {str(e)}")

            self.parent.quit()
            
    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        from utils.backup_manager import BackupManager
        from utils.database_manager import DatabaseManager

        try:
            db_manager = DatabaseManager()
            backup_manager = BackupManager(db_manager)

            success, result = backup_manager.create_backup(
                self.current_user['id'], 'manual', show_dialog=True
            )

            if success:
                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{os.path.basename(result)}")
            else:
                if result != "تم إلغاء العملية":
                    messagebox.showerror("خطأ", result)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def restore_database(self):
        """فتح شاشة استعادة النسخة الاحتياطية"""
        from screens.restore_backup import RestoreBackup
        RestoreBackup(self.parent, self.current_user)

    def backup_management(self):
        """فتح شاشة إدارة النسخ الاحتياطي"""
        from screens.backup_management import BackupManagement
        BackupManagement(self.parent, self.current_user)
        
    def show_settings(self):
        """فتح شاشة إعدادات البرنامج"""
        from screens.program_settings import ProgramSettings
        ProgramSettings(self.parent, self.current_user)
        
    def about(self):
        """فتح نافذة حول البرنامج المتقدمة"""
        from screens.about_program import AboutProgram
        AboutProgram(self.parent, self.current_user)
        
    def user_guide(self):
        """فتح دليل المستخدم التفاعلي"""
        from screens.user_guide import UserGuide
        UserGuide(self.parent, self.current_user)
        
    # باقي الدوال سيتم تطويرها في المراحل القادمة

    def sales_returns(self):
        """فتح شاشة مرتجعات المبيعات"""
        from screens.returns_management import ReturnsManagement
        ReturnsManagement(self.parent, self.current_user, 'sales')

    def purchase_returns(self):
        """فتح شاشة مرتجعات المشتريات"""
        from screens.returns_management import ReturnsManagement
        ReturnsManagement(self.parent, self.current_user, 'purchase')
    def manage_categories(self):
        """فتح شاشة إدارة فئات المنتجات"""
        from screens.categories_management import CategoriesManagement
        CategoriesManagement(self.parent, self.current_user)
    def inventory_count(self):
        """فتح شاشة جرد المخزون"""
        from screens.inventory_count import InventoryCount
        InventoryCount(self.parent, self.current_user)
    def inventory_movements(self):
        """فتح شاشة حركات المخزون"""
        from screens.inventory_movements import InventoryMovements
        InventoryMovements(self.parent, self.current_user)
    def customer_statement(self):
        """فتح شاشة كشف حساب العميل"""
        from screens.customer_statement import CustomerStatement
        CustomerStatement(self.parent, self.current_user)
    def supplier_statement(self):
        """فتح شاشة كشف حساب المورد"""
        from screens.supplier_statement import SupplierStatement
        SupplierStatement(self.parent, self.current_user)
    def purchases_report(self):
        """فتح شاشة تقارير المشتريات"""
        from screens.purchases_reports import PurchasesReports
        PurchasesReports(self.parent, self.current_user)
    def profit_loss_report(self):
        """فتح شاشة تقرير الأرباح والخسائر"""
        from screens.profit_loss_report import ProfitLossReport
        ProfitLossReport(self.parent, self.current_user)
    def inventory_report(self):
        """فتح شاشة تقارير المخزون"""
        from screens.inventory_reports import InventoryReports
        InventoryReports(self.parent, self.current_user)
    def user_permissions(self):
        """فتح شاشة إدارة صلاحيات المستخدمين"""
        from screens.user_permissions import UserPermissions
        UserPermissions(self.parent, self.current_user)
    def activity_log(self):
        """فتح شاشة سجل نشاط المستخدمين"""
        from screens.activity_log import ActivityLog
        ActivityLog(self.parent, self.current_user)
