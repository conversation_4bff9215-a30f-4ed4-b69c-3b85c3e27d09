# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية للبرنامج
"""

import tkinter as tk
from tkinter import ttk, messagebox
from config.settings import COLORS, FONTS, COMPANY_INFO
from utils.helpers import check_user_permission, get_user_role_text
from screens.users_management import UsersManagement

class MainInterface:
    """كلاس الواجهة الرئيسية"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.setup_main_interface()
        
    def setup_main_interface(self):
        """إعداد الواجهة الرئيسية"""
        # مسح المحتوى الحالي
        for widget in self.parent.winfo_children():
            widget.destroy()
            
        # إعداد النافذة الرئيسية
        self.parent.configure(bg=COLORS['background'])
        
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.parent)
        self.parent.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="النسخ الاحتياطي", command=self.backup_database)
        file_menu.add_command(label="استعادة النسخة الاحتياطية", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="إعدادات البرنامج", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.parent.quit)
        
        # قائمة المبيعات
        if check_user_permission(self.current_user['role'], 'sales'):
            sales_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المبيعات", menu=sales_menu)
            sales_menu.add_command(label="فاتورة مبيعات جديدة", command=self.new_sales_invoice)
            sales_menu.add_command(label="قائمة فواتير المبيعات", command=self.sales_invoices_list)
            sales_menu.add_command(label="المرتجعات", command=self.sales_returns)
        
        # قائمة المشتريات
        if check_user_permission(self.current_user['role'], 'purchases'):
            purchases_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المشتريات", menu=purchases_menu)
            purchases_menu.add_command(label="فاتورة شراء جديدة", command=self.new_purchase_invoice)
            purchases_menu.add_command(label="قائمة فواتير المشتريات", command=self.purchase_invoices_list)
            purchases_menu.add_command(label="المرتجعات", command=self.purchase_returns)
        
        # قائمة المخزون
        if check_user_permission(self.current_user['role'], 'inventory'):
            inventory_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="المخزون", menu=inventory_menu)
            inventory_menu.add_command(label="إدارة المنتجات", command=self.manage_products)
            inventory_menu.add_command(label="إدارة الفئات", command=self.manage_categories)
            inventory_menu.add_command(label="جرد المخزون", command=self.inventory_count)
            inventory_menu.add_command(label="حركات المخزون", command=self.inventory_movements)
        
        # قائمة العملاء
        if check_user_permission(self.current_user['role'], 'customers'):
            customers_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="العملاء", menu=customers_menu)
            customers_menu.add_command(label="إدارة العملاء", command=self.manage_customers)
            customers_menu.add_command(label="كشف حساب عميل", command=self.customer_statement)
        
        # قائمة الموردين
        if check_user_permission(self.current_user['role'], 'suppliers'):
            suppliers_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الموردين", menu=suppliers_menu)
            suppliers_menu.add_command(label="إدارة الموردين", command=self.manage_suppliers)
            suppliers_menu.add_command(label="كشف حساب مورد", command=self.supplier_statement)
        
        # قائمة التقارير
        if check_user_permission(self.current_user['role'], 'reports'):
            reports_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="التقارير", menu=reports_menu)
            reports_menu.add_command(label="تقرير المبيعات", command=self.sales_report)
            reports_menu.add_command(label="تقرير المشتريات", command=self.purchases_report)
            reports_menu.add_command(label="تقرير الأرباح والخسائر", command=self.profit_loss_report)
            reports_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        
        # قائمة الإدارة (للمدير فقط)
        if self.current_user['role'] == 'admin':
            admin_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الإدارة", menu=admin_menu)
            admin_menu.add_command(label="إدارة المستخدمين", command=self.manage_users)
            admin_menu.add_command(label="صلاحيات المستخدمين", command=self.user_permissions)
            admin_menu.add_command(label="سجل العمليات", command=self.activity_log)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.about)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.parent, bg=COLORS['primary'], height=30)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # معلومات المستخدم
        user_info = f"المستخدم: {self.current_user['name']} | الدور: {get_user_role_text(self.current_user['role'])}"
        self.user_label = tk.Label(
            self.status_frame,
            text=user_info,
            bg=COLORS['primary'],
            fg='white',
            font=FONTS['small']
        )
        self.user_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # معلومات الشركة
        company_info = f"{COMPANY_INFO['name']} | {COMPANY_INFO['phone']}"
        self.company_label = tk.Label(
            self.status_frame,
            text=company_info,
            bg=COLORS['primary'],
            fg='white',
            font=FONTS['small']
        )
        self.company_label.pack(side=tk.RIGHT, padx=10, pady=5)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.parent, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            main_frame,
            text=f"مرحباً {self.current_user['name']}",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار السريعة
        quick_buttons_frame = tk.LabelFrame(
            main_frame,
            text="الوصول السريع",
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        quick_buttons_frame.pack(fill='x', pady=(0, 20))
        
        # إنشاء الأزرار السريعة
        self.create_quick_buttons(quick_buttons_frame)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(
            main_frame,
            text="إحصائيات سريعة",
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        stats_frame.pack(fill='both', expand=True)
        
        # إنشاء الإحصائيات
        self.create_statistics(stats_frame)
        
    def create_quick_buttons(self, parent):
        """إنشاء الأزرار السريعة"""
        buttons_frame = tk.Frame(parent, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        # صف الأزرار الأول
        row1_frame = tk.Frame(buttons_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', pady=(0, 10))
        
        if check_user_permission(self.current_user['role'], 'sales'):
            sales_btn = tk.Button(
                row1_frame,
                text="فاتورة مبيعات جديدة",
                font=FONTS['button'],
                bg=COLORS['success'],
                fg='white',
                width=20,
                height=2,
                command=self.new_sales_invoice
            )
            sales_btn.pack(side=tk.LEFT, padx=5)
        
        if check_user_permission(self.current_user['role'], 'purchases'):
            purchase_btn = tk.Button(
                row1_frame,
                text="فاتورة شراء جديدة",
                font=FONTS['button'],
                bg=COLORS['warning'],
                fg='white',
                width=20,
                height=2,
                command=self.new_purchase_invoice
            )
            purchase_btn.pack(side=tk.LEFT, padx=5)
        
        if check_user_permission(self.current_user['role'], 'inventory'):
            products_btn = tk.Button(
                row1_frame,
                text="إدارة المنتجات",
                font=FONTS['button'],
                bg=COLORS['info'],
                fg='white',
                width=20,
                height=2,
                command=self.manage_products
            )
            products_btn.pack(side=tk.LEFT, padx=5)
        
        # صف الأزرار الثاني
        row2_frame = tk.Frame(buttons_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x')
        
        if check_user_permission(self.current_user['role'], 'customers'):
            customers_btn = tk.Button(
                row2_frame,
                text="إدارة العملاء",
                font=FONTS['button'],
                bg=COLORS['primary'],
                fg='white',
                width=20,
                height=2,
                command=self.manage_customers
            )
            customers_btn.pack(side=tk.LEFT, padx=5)
        
        if check_user_permission(self.current_user['role'], 'suppliers'):
            suppliers_btn = tk.Button(
                row2_frame,
                text="إدارة الموردين",
                font=FONTS['button'],
                bg=COLORS['secondary'],
                fg='white',
                width=20,
                height=2,
                command=self.manage_suppliers
            )
            suppliers_btn.pack(side=tk.LEFT, padx=5)
        
        if check_user_permission(self.current_user['role'], 'reports'):
            reports_btn = tk.Button(
                row2_frame,
                text="التقارير",
                font=FONTS['button'],
                bg=COLORS['dark'],
                fg='white',
                width=20,
                height=2,
                command=self.sales_report
            )
            reports_btn.pack(side=tk.LEFT, padx=5)
            
    def create_statistics(self, parent):
        """إنشاء الإحصائيات السريعة"""
        stats_frame = tk.Frame(parent, bg=COLORS['background'])
        stats_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # سيتم تطوير هذا القسم لاحقاً لعرض الإحصائيات
        placeholder_label = tk.Label(
            stats_frame,
            text="سيتم عرض الإحصائيات هنا قريباً...",
            font=FONTS['normal'],
            bg=COLORS['background'],
            fg=COLORS['text']
        )
        placeholder_label.pack(expand=True)
        
    # دوال معالجة الأحداث (سيتم تطويرها لاحقاً)
    def new_sales_invoice(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def new_purchase_invoice(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def manage_products(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def manage_customers(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def manage_suppliers(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def sales_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def manage_users(self):
        """فتح شاشة إدارة المستخدمين"""
        UsersManagement(self.parent, self.current_user)
        
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تأكيد", "هل تريد تسجيل الخروج؟")
        if result:
            self.parent.quit()
            
    def backup_database(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def restore_database(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def show_settings(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")
        
    def about(self):
        messagebox.showinfo("حول البرنامج", 
                          f"{COMPANY_INFO['name']}\n"
                          "برنامج محاسبة المبيعات والمخازن\n"
                          "تم التطوير باستخدام Augment Agent")
        
    def user_guide(self):
        messagebox.showinfo("قريباً", "سيتم تطوير دليل المستخدم قريباً")
        
    # باقي الدوال سيتم تطويرها في المراحل القادمة
    def sales_invoices_list(self): pass
    def sales_returns(self): pass
    def purchase_invoices_list(self): pass
    def purchase_returns(self): pass
    def manage_categories(self): pass
    def inventory_count(self): pass
    def inventory_movements(self): pass
    def customer_statement(self): pass
    def supplier_statement(self): pass
    def purchases_report(self): pass
    def profit_loss_report(self): pass
    def inventory_report(self): pass
    def user_permissions(self): pass
    def activity_log(self): pass
