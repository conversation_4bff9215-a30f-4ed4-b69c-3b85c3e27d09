# -*- coding: utf-8 -*-
"""
شاشة إدارة المنتجات
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from PIL import Image, ImageTk
from config.settings import COLORS, FONTS, IMAGES_DIR
from utils.database_manager import DatabaseManager
from utils.helpers import (validate_phone, is_valid_number, format_currency, generate_barcode,
                          check_user_permission, log_user_activity, show_permission_error)
from utils.arabic_support import ArabicSupport

class ProductsManagement:
    """كلاس إدارة المنتجات"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        self.selected_image_path = None
        
        self.setup_window()
        self.create_widgets()
        self.load_products()
        self.load_categories()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة المنتجات")
        self.window.geometry("1200x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1200
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة المنتجات",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)
        
        # الأزرار
        tk.Button(buttons_frame, text="إضافة منتج جديد", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=self.add_product_dialog).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تعديل المنتج", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', command=self.edit_product_dialog).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="حذف المنتج", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=self.delete_product).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إدارة الفئات", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', command=self.manage_categories).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', command=self.load_products).pack(side=tk.RIGHT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(pady=5)
        
        tk.Label(search_frame, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=FONTS['normal'], width=30, justify='right')
        search_entry.pack(side=tk.RIGHT, padx=5)
        
        # إطار جدول المنتجات
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview للمنتجات
        columns = ('ID', 'الاسم', 'الباركود', 'الفئة', 'الوحدة', 'سعر التكلفة', 'سعر البيع', 'الكمية', 'الحد الأدنى', 'الحالة')
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        # تحديد عناوين الأعمدة
        for col in columns:
            self.products_tree.heading(col, text=col)
            if col in ['سعر التكلفة', 'سعر البيع']:
                self.products_tree.column(col, width=100, anchor='center')
            elif col in ['الكمية', 'الحد الأدنى']:
                self.products_tree.column(col, width=80, anchor='center')
            elif col == 'ID':
                self.products_tree.column(col, width=50, anchor='center')
            else:
                self.products_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب العناصر
        self.products_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج بالتعديل
        self.products_tree.bind('<Double-1>', lambda e: self.edit_product_dialog())
        
    def load_categories(self):
        """تحميل قائمة الفئات"""
        try:
            query = "SELECT id, name FROM categories ORDER BY name"
            self.categories = self.db_manager.execute_query(query)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفئات:\n{str(e)}")
            self.categories = []
            
    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
                
            # جلب المنتجات من قاعدة البيانات
            query = """
                SELECT p.id, p.name, p.barcode, c.name as category_name, p.unit,
                       p.cost_price, p.selling_price, p.stock_quantity, 
                       p.min_stock_level, p.is_active
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                ORDER BY p.name
            """
            products = self.db_manager.execute_query(query)
            
            # إضافة المنتجات إلى الجدول
            for product in products:
                status = "نشط" if product['is_active'] else "غير نشط"
                category_name = product['category_name'] or 'غير محدد'
                
                # تلوين الصفوف حسب مستوى المخزون
                item_id = self.products_tree.insert('', 'end', values=(
                    product['id'],
                    product['name'],
                    product['barcode'] or '',
                    category_name,
                    product['unit'],
                    f"{product['cost_price']:.2f}",
                    f"{product['selling_price']:.2f}",
                    product['stock_quantity'],
                    product['min_stock_level'],
                    status
                ))
                
                # تلوين الصف إذا كان المخزون أقل من الحد الأدنى
                if product['stock_quantity'] <= product['min_stock_level']:
                    self.products_tree.set(item_id, 'الكمية', f"{product['stock_quantity']} ⚠️")
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المنتجات:\n{str(e)}")
            
    def on_search_change(self, *args):
        """معالج تغيير البحث"""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            self.load_products()
            return
            
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
                
            # البحث في المنتجات
            query = """
                SELECT p.id, p.name, p.barcode, c.name as category_name, p.unit,
                       p.cost_price, p.selling_price, p.stock_quantity, 
                       p.min_stock_level, p.is_active
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.name LIKE ? OR p.barcode LIKE ? OR c.name LIKE ?
                ORDER BY p.name
            """
            search_pattern = f"%{search_term}%"
            products = self.db_manager.execute_query(query, (search_pattern, search_pattern, search_pattern))
            
            # إضافة النتائج إلى الجدول
            for product in products:
                status = "نشط" if product['is_active'] else "غير نشط"
                category_name = product['category_name'] or 'غير محدد'
                
                item_id = self.products_tree.insert('', 'end', values=(
                    product['id'],
                    product['name'],
                    product['barcode'] or '',
                    category_name,
                    product['unit'],
                    f"{product['cost_price']:.2f}",
                    f"{product['selling_price']:.2f}",
                    product['stock_quantity'],
                    product['min_stock_level'],
                    status
                ))
                
                if product['stock_quantity'] <= product['min_stock_level']:
                    self.products_tree.set(item_id, 'الكمية', f"{product['stock_quantity']} ⚠️")
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في البحث:\n{str(e)}")
            
    def get_selected_product(self):
        """الحصول على المنتج المحدد"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج من القائمة")
            return None
            
        item = self.products_tree.item(selection[0])
        product_id = item['values'][0]
        
        # جلب بيانات المنتج الكاملة
        query = "SELECT * FROM products WHERE id = ?"
        products = self.db_manager.execute_query(query, (product_id,))
        
        if products:
            return dict(products[0])
        return None

    def add_product_dialog(self):
        """حوار إضافة منتج جديد"""
        # التحقق من الصلاحية
        if not check_user_permission(self.current_user['role'], 'products_management'):
            show_permission_error('إدارة المنتجات')
            return

        self.product_dialog(mode='add')

    def edit_product_dialog(self):
        """حوار تعديل منتج"""
        # التحقق من الصلاحية
        if not check_user_permission(self.current_user['role'], 'products_management'):
            show_permission_error('إدارة المنتجات')
            return

        product = self.get_selected_product()
        if product:
            self.product_dialog(mode='edit', product_data=product)

    def product_dialog(self, mode='add', product_data=None):
        """حوار إضافة/تعديل منتج"""
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة منتج جديد" if mode == 'add' else "تعديل المنتج")
        dialog.geometry("600x700")
        dialog.configure(bg=COLORS['background'])
        dialog.resizable(False, False)
        ArabicSupport.setup_window_rtl(dialog)

        # توسيط الحوار
        dialog.transient(self.window)
        dialog.grab_set()

        # المتغيرات
        name_var = tk.StringVar(value=product_data['name'] if product_data else '')
        barcode_var = tk.StringVar(value=product_data['barcode'] if product_data else '')
        category_var = tk.StringVar(value=str(product_data['category_id']) if product_data and product_data['category_id'] else '')
        description_var = tk.StringVar(value=product_data['description'] if product_data else '')
        unit_var = tk.StringVar(value=product_data['unit'] if product_data else 'قطعة')
        cost_price_var = tk.StringVar(value=str(product_data['cost_price']) if product_data else '0')
        selling_price_var = tk.StringVar(value=str(product_data['selling_price']) if product_data else '0')
        stock_quantity_var = tk.StringVar(value=str(product_data['stock_quantity']) if product_data else '0')
        min_stock_var = tk.StringVar(value=str(product_data['min_stock_level']) if product_data else '0')
        active_var = tk.BooleanVar(value=bool(product_data['is_active']) if product_data else True)

        # إطار المحتوى مع شريط تمرير
        canvas = tk.Canvas(dialog, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(scrollable_frame, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم المنتج
        tk.Label(content_frame, text="اسم المنتج:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        name_entry = tk.Entry(content_frame, textvariable=name_var, font=FONTS['normal'],
                             width=40, justify='right')
        name_entry.pack(pady=(0, 10))

        # الباركود
        barcode_frame = tk.Frame(content_frame, bg=COLORS['background'])
        barcode_frame.pack(fill='x', pady=(0, 10))

        tk.Label(barcode_frame, text="الباركود:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))

        barcode_entry_frame = tk.Frame(barcode_frame, bg=COLORS['background'])
        barcode_entry_frame.pack()

        barcode_entry = tk.Entry(barcode_entry_frame, textvariable=barcode_var,
                                font=FONTS['normal'], width=30, justify='right')
        barcode_entry.pack(side=tk.RIGHT, padx=(0, 5))

        tk.Button(barcode_entry_frame, text="إنشاء تلقائي", font=FONTS['small'],
                 bg=COLORS['info'], fg='white',
                 command=lambda: barcode_var.set(generate_barcode())).pack(side=tk.RIGHT)

        # الفئة
        tk.Label(content_frame, text="الفئة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        category_combo = ttk.Combobox(content_frame, textvariable=category_var,
                                     font=FONTS['normal'], width=37, state='readonly')
        category_values = [f"{cat['id']}:{cat['name']}" for cat in self.categories]
        category_combo['values'] = category_values
        category_combo.pack(pady=(0, 10))

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        description_entry = tk.Entry(content_frame, textvariable=description_var,
                                   font=FONTS['normal'], width=40, justify='right')
        description_entry.pack(pady=(0, 10))

        # الوحدة
        tk.Label(content_frame, text="وحدة القياس:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        unit_combo = ttk.Combobox(content_frame, textvariable=unit_var,
                                 font=FONTS['normal'], width=37)
        unit_combo['values'] = ['قطعة', 'كيلو', 'متر', 'لتر', 'علبة', 'كرتون', 'دزينة']
        unit_combo.pack(pady=(0, 10))

        # الأسعار
        prices_frame = tk.Frame(content_frame, bg=COLORS['background'])
        prices_frame.pack(fill='x', pady=(0, 10))

        # سعر التكلفة
        cost_frame = tk.Frame(prices_frame, bg=COLORS['background'])
        cost_frame.pack(side=tk.RIGHT, padx=10)

        tk.Label(cost_frame, text="سعر التكلفة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e')
        cost_entry = tk.Entry(cost_frame, textvariable=cost_price_var,
                             font=FONTS['normal'], width=15, justify='right')
        cost_entry.pack()

        # سعر البيع
        selling_frame = tk.Frame(prices_frame, bg=COLORS['background'])
        selling_frame.pack(side=tk.RIGHT, padx=10)

        tk.Label(selling_frame, text="سعر البيع:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e')
        selling_entry = tk.Entry(selling_frame, textvariable=selling_price_var,
                                font=FONTS['normal'], width=15, justify='right')
        selling_entry.pack()

        # الكميات
        quantities_frame = tk.Frame(content_frame, bg=COLORS['background'])
        quantities_frame.pack(fill='x', pady=(0, 10))

        # الكمية الحالية
        stock_frame = tk.Frame(quantities_frame, bg=COLORS['background'])
        stock_frame.pack(side=tk.RIGHT, padx=10)

        tk.Label(stock_frame, text="الكمية الحالية:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e')
        stock_entry = tk.Entry(stock_frame, textvariable=stock_quantity_var,
                              font=FONTS['normal'], width=15, justify='right')
        stock_entry.pack()

        # الحد الأدنى
        min_frame = tk.Frame(quantities_frame, bg=COLORS['background'])
        min_frame.pack(side=tk.RIGHT, padx=10)

        tk.Label(min_frame, text="الحد الأدنى:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e')
        min_entry = tk.Entry(min_frame, textvariable=min_stock_var,
                            font=FONTS['normal'], width=15, justify='right')
        min_entry.pack()

        # الصورة
        image_frame = tk.LabelFrame(content_frame, text="صورة المنتج",
                                   font=FONTS['normal'], bg=COLORS['background'])
        image_frame.pack(fill='x', pady=(0, 10))

        self.image_label = tk.Label(image_frame, text="لا توجد صورة",
                                   bg=COLORS['light'], width=20, height=8)
        self.image_label.pack(pady=5)

        image_buttons_frame = tk.Frame(image_frame, bg=COLORS['background'])
        image_buttons_frame.pack()

        tk.Button(image_buttons_frame, text="اختيار صورة", font=FONTS['small'],
                 bg=COLORS['info'], fg='white',
                 command=lambda: self.select_image()).pack(side=tk.RIGHT, padx=5)

        tk.Button(image_buttons_frame, text="حذف الصورة", font=FONTS['small'],
                 bg=COLORS['danger'], fg='white',
                 command=lambda: self.remove_image()).pack(side=tk.RIGHT, padx=5)

        # تحميل الصورة الحالية إذا كانت موجودة
        if product_data and product_data.get('image_path'):
            self.load_product_image(product_data['image_path'])

        # حالة المنتج
        active_check = tk.Checkbutton(content_frame, text="المنتج نشط", variable=active_var,
                                    font=FONTS['normal'], bg=COLORS['background'])
        active_check.pack(pady=10)

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(content_frame, bg=COLORS['background'])
        buttons_frame.pack(pady=20)

        def save_product():
            # التحقق من البيانات
            if not name_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
                return

            if not is_valid_number(cost_price_var.get(), 0):
                messagebox.showerror("خطأ", "سعر التكلفة غير صحيح")
                return

            if not is_valid_number(selling_price_var.get(), 0):
                messagebox.showerror("خطأ", "سعر البيع غير صحيح")
                return

            if not is_valid_number(stock_quantity_var.get(), 0):
                messagebox.showerror("خطأ", "الكمية غير صحيحة")
                return

            if not is_valid_number(min_stock_var.get(), 0):
                messagebox.showerror("خطأ", "الحد الأدنى غير صحيح")
                return

            try:
                # استخراج معرف الفئة
                category_id = None
                if category_var.get():
                    try:
                        category_id = int(category_var.get().split(':')[0])
                    except (ValueError, IndexError):
                        messagebox.showerror("خطأ", "يرجى اختيار فئة صحيحة")
                        return

                if mode == 'add':
                    # إضافة منتج جديد
                    query = """
                        INSERT INTO products (name, barcode, category_id, description, unit,
                                            cost_price, selling_price, stock_quantity,
                                            min_stock_level, image_path, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        name_var.get().strip(),
                        barcode_var.get().strip() or None,
                        category_id,
                        description_var.get().strip() or None,
                        unit_var.get().strip(),
                        float(cost_price_var.get()),
                        float(selling_price_var.get()),
                        int(float(stock_quantity_var.get())),
                        int(float(min_stock_var.get())),
                        self.selected_image_path,
                        1 if active_var.get() else 0
                    )
                else:
                    # تعديل منتج موجود
                    query = """
                        UPDATE products
                        SET name=?, barcode=?, category_id=?, description=?, unit=?,
                            cost_price=?, selling_price=?, stock_quantity=?,
                            min_stock_level=?, image_path=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    params = (
                        name_var.get().strip(),
                        barcode_var.get().strip() or None,
                        category_id,
                        description_var.get().strip() or None,
                        unit_var.get().strip(),
                        float(cost_price_var.get()),
                        float(selling_price_var.get()),
                        int(float(stock_quantity_var.get())),
                        int(float(min_stock_var.get())),
                        self.selected_image_path,
                        1 if active_var.get() else 0,
                        product_data['id']
                    )

                self.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم حفظ المنتج بنجاح")
                dialog.destroy()
                self.load_products()

            except Exception as e:
                if "UNIQUE constraint failed" in str(e):
                    messagebox.showerror("خطأ", "الباركود موجود مسبقاً")
                else:
                    messagebox.showerror("خطأ", f"حدث خطأ في حفظ المنتج:\n{str(e)}")

        tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=save_product).pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

        # ترتيب العناصر
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def select_image(self):
        """اختيار صورة للمنتج"""
        file_types = [
            ('صور', '*.png *.jpg *.jpeg *.gif *.bmp'),
            ('PNG', '*.png'),
            ('JPEG', '*.jpg *.jpeg'),
            ('جميع الملفات', '*.*')
        ]

        filename = filedialog.askopenfilename(
            title="اختيار صورة المنتج",
            filetypes=file_types
        )

        if filename:
            try:
                # إنشاء مجلد الصور إذا لم يكن موجوداً
                if not os.path.exists(IMAGES_DIR):
                    os.makedirs(IMAGES_DIR)

                # نسخ الصورة إلى مجلد الصور
                import shutil
                filename_only = os.path.basename(filename)
                new_path = os.path.join(IMAGES_DIR, filename_only)
                shutil.copy2(filename, new_path)

                self.selected_image_path = new_path
                self.load_product_image(new_path)

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في تحميل الصورة:\n{str(e)}")

    def remove_image(self):
        """حذف صورة المنتج"""
        self.selected_image_path = None
        self.image_label.configure(image='', text="لا توجد صورة")

    def load_product_image(self, image_path):
        """تحميل صورة المنتج"""
        try:
            if image_path and os.path.exists(image_path):
                # تحميل وتغيير حجم الصورة
                image = Image.open(image_path)
                image = image.resize((150, 150), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(image)

                self.image_label.configure(image=photo, text='')
                self.image_label.image = photo  # الاحتفاظ بمرجع للصورة
                self.selected_image_path = image_path
            else:
                self.image_label.configure(image='', text="لا توجد صورة")

        except Exception as e:
            self.image_label.configure(image='', text="خطأ في تحميل الصورة")

    def delete_product(self):
        """حذف منتج"""
        product = self.get_selected_product()
        if not product:
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف المنتج '{product['name']}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه.")

        if result:
            try:
                query = "DELETE FROM products WHERE id = ?"
                self.db_manager.execute_query(query, (product['id'],))
                messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
                self.load_products()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف المنتج:\n{str(e)}")

    def manage_categories(self):
        """إدارة فئات المنتجات"""
        CategoriesManagement(self.window, self.current_user, self.load_categories)

class CategoriesManagement:
    """كلاس إدارة فئات المنتجات"""

    def __init__(self, parent, current_user, callback=None):
        self.parent = parent
        self.current_user = current_user
        self.callback = callback
        self.db_manager = DatabaseManager()

        self.setup_window()
        self.create_widgets()
        self.load_categories()

    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة فئات المنتجات")
        self.window.geometry("600x500")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)

        # توسيط النافذة
        self.center_window()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 600
        height = 500
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة فئات المنتجات",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)

        tk.Button(buttons_frame, text="إضافة فئة جديدة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=self.add_category_dialog).pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="تعديل الفئة", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', command=self.edit_category_dialog).pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="حذف الفئة", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=self.delete_category).pack(side=tk.RIGHT, padx=5)

        # إطار جدول الفئات
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # إنشاء Treeview للفئات
        columns = ('ID', 'اسم الفئة', 'الوصف', 'تاريخ الإنشاء')
        self.categories_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تحديد عناوين الأعمدة
        for col in columns:
            self.categories_tree.heading(col, text=col)
            if col == 'ID':
                self.categories_tree.column(col, width=50, anchor='center')
            else:
                self.categories_tree.column(col, width=150, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=scrollbar.set)

        # ترتيب العناصر
        self.categories_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # ربط النقر المزدوج بالتعديل
        self.categories_tree.bind('<Double-1>', lambda e: self.edit_category_dialog())

    def load_categories(self):
        """تحميل قائمة الفئات"""
        try:
            # مسح البيانات الحالية
            for item in self.categories_tree.get_children():
                self.categories_tree.delete(item)

            # جلب الفئات من قاعدة البيانات
            query = "SELECT id, name, description, created_at FROM categories ORDER BY name"
            categories = self.db_manager.execute_query(query)

            # إضافة الفئات إلى الجدول
            for category in categories:
                self.categories_tree.insert('', 'end', values=(
                    category['id'],
                    category['name'],
                    category['description'] or '',
                    category['created_at'][:10] if category['created_at'] else ''
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفئات:\n{str(e)}")

    def get_selected_category(self):
        """الحصول على الفئة المحددة"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فئة من القائمة")
            return None

        item = self.categories_tree.item(selection[0])
        category_id = item['values'][0]

        # جلب بيانات الفئة الكاملة
        query = "SELECT * FROM categories WHERE id = ?"
        categories = self.db_manager.execute_query(query, (category_id,))

        if categories:
            return dict(categories[0])
        return None

    def add_category_dialog(self):
        """حوار إضافة فئة جديدة"""
        self.category_dialog(mode='add')

    def edit_category_dialog(self):
        """حوار تعديل فئة"""
        category = self.get_selected_category()
        if category:
            self.category_dialog(mode='edit', category_data=category)

    def category_dialog(self, mode='add', category_data=None):
        """حوار إضافة/تعديل فئة"""
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة فئة جديدة" if mode == 'add' else "تعديل الفئة")
        dialog.geometry("400x300")
        dialog.configure(bg=COLORS['background'])
        dialog.resizable(False, False)
        ArabicSupport.setup_window_rtl(dialog)

        # توسيط الحوار
        dialog.transient(self.window)
        dialog.grab_set()

        # المتغيرات
        name_var = tk.StringVar(value=category_data['name'] if category_data else '')
        description_var = tk.StringVar(value=category_data['description'] if category_data else '')

        # إطار المحتوى
        content_frame = tk.Frame(dialog, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم الفئة
        tk.Label(content_frame, text="اسم الفئة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        name_entry = tk.Entry(content_frame, textvariable=name_var, font=FONTS['normal'],
                             width=30, justify='right')
        name_entry.pack(pady=(0, 15))
        name_entry.focus()

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        description_entry = tk.Entry(content_frame, textvariable=description_var,
                                   font=FONTS['normal'], width=30, justify='right')
        description_entry.pack(pady=(0, 20))

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(content_frame, bg=COLORS['background'])
        buttons_frame.pack(pady=20)

        def save_category():
            if not name_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال اسم الفئة")
                return

            try:
                if mode == 'add':
                    query = "INSERT INTO categories (name, description) VALUES (?, ?)"
                    params = (name_var.get().strip(), description_var.get().strip() or None)
                else:
                    query = "UPDATE categories SET name=?, description=? WHERE id=?"
                    params = (name_var.get().strip(), description_var.get().strip() or None, category_data['id'])

                self.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم حفظ الفئة بنجاح")
                dialog.destroy()
                self.load_categories()
                if self.callback:
                    self.callback()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ الفئة:\n{str(e)}")

        tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=save_category).pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def delete_category(self):
        """حذف فئة"""
        category = self.get_selected_category()
        if not category:
            return

        # التحقق من وجود منتجات في هذه الفئة
        try:
            query = "SELECT COUNT(*) as count FROM products WHERE category_id = ?"
            result = self.db_manager.execute_query(query, (category['id'],))
            products_count = result[0]['count'] if result else 0

            if products_count > 0:
                messagebox.showerror("خطأ", f"لا يمكن حذف هذه الفئة لأنها تحتوي على {products_count} منتج")
                return

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التحقق من المنتجات:\n{str(e)}")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الفئة '{category['name']}'؟")

        if result:
            try:
                query = "DELETE FROM categories WHERE id = ?"
                self.db_manager.execute_query(query, (category['id'],))
                messagebox.showinfo("نجح", "تم حذف الفئة بنجاح")
                self.load_categories()
                if self.callback:
                    self.callback()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف الفئة:\n{str(e)}")
