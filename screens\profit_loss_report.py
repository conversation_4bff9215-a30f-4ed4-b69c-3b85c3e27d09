# -*- coding: utf-8 -*-
"""
شاشة تقرير الأرباح والخسائر
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import (format_currency, get_date_range, format_date,
                          check_user_permission, show_permission_error, log_user_activity,
                          calculate_cost_of_goods_sold)
from utils.arabic_support import ArabicSupport

class ProfitLossReport:
    """كلاس تقرير الأرباح والخسائر"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'reports_view'):
            show_permission_error('عرض تقرير الأرباح والخسائر')
            return
        
        self.setup_window()
        self.create_widgets()
        self.generate_report()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تقرير الأرباح والخسائر")
        self.window.geometry("1200x800")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1200
        height = 800
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="تقرير الأرباح والخسائر",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار فلاتر التاريخ
        date_frame = tk.LabelFrame(self.window, text="فترة التقرير", 
                                  font=FONTS['heading'], bg=COLORS['background'])
        date_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول
        row1_frame = tk.Frame(date_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # من تاريخ
        tk.Label(row1_frame, text="من تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.from_date_var = tk.StringVar(value=get_current_date())
        from_date_entry = tk.Entry(row1_frame, textvariable=self.from_date_var, 
                                  font=FONTS['normal'], width=12, justify='right')
        from_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # إلى تاريخ
        tk.Label(row1_frame, text="إلى تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.to_date_var = tk.StringVar(value=get_current_date())
        to_date_entry = tk.Entry(row1_frame, textvariable=self.to_date_var, 
                                font=FONTS['normal'], width=12, justify='right')
        to_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني - فترات سريعة وزر التحديث
        row2_frame = tk.Frame(date_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row2_frame, text="فترات سريعة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        quick_periods = [
            ('اليوم', 'today'),
            ('هذا الأسبوع', 'this_week'),
            ('هذا الشهر', 'this_month'),
            ('الشهر الماضي', 'last_month'),
            ('هذا العام', 'this_year')
        ]
        
        for text, period in quick_periods:
            tk.Button(row2_frame, text=text, font=FONTS['small'],
                     bg=COLORS['info'], fg='white', 
                     command=lambda p=period: self.set_quick_period(p)).pack(side=tk.RIGHT, padx=2)
        
        # زر تحديث التقرير
        tk.Button(row2_frame, text="تحديث التقرير", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', 
                 command=self.generate_report).pack(side=tk.RIGHT, padx=10)
        
        # إطار التقرير الرئيسي
        report_frame = tk.LabelFrame(self.window, text="بيان الأرباح والخسائر", 
                                    font=FONTS['heading'], bg=COLORS['background'])
        report_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء إطار قابل للتمرير
        canvas = tk.Canvas(report_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(report_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")
        
        # أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.print_report).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تصدير Excel", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.export_report).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def set_quick_period(self, period):
        """تعيين فترة سريعة"""
        try:
            start_date, end_date = get_date_range(period)
            self.from_date_var.set(start_date.strftime('%Y-%m-%d'))
            self.to_date_var.set(end_date.strftime('%Y-%m-%d'))
            self.generate_report()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين الفترة:\n{str(e)}")
            
    def get_date_filter(self):
        """الحصول على فلتر التاريخ"""
        return self.from_date_var.get(), self.to_date_var.get()
        
    def create_section_header(self, text, color=COLORS['primary']):
        """إنشاء عنوان قسم"""
        header_frame = tk.Frame(self.scrollable_frame, bg=COLORS['background'])
        header_frame.pack(fill='x', padx=10, pady=(10, 5))
        
        tk.Label(header_frame, text=text, font=FONTS['heading'],
                bg=COLORS['background'], fg=color).pack(anchor='w')
        
        # خط فاصل
        separator = tk.Frame(header_frame, height=2, bg=color)
        separator.pack(fill='x', pady=(2, 0))
        
    def create_data_row(self, label, amount, level=0, bold=False):
        """إنشاء صف بيانات"""
        row_frame = tk.Frame(self.scrollable_frame, bg=COLORS['background'])
        row_frame.pack(fill='x', padx=(20 + level*20, 10), pady=1)
        
        font = FONTS['heading'] if bold else FONTS['normal']
        color = COLORS['primary'] if bold else COLORS['text']
        
        tk.Label(row_frame, text=label, font=font,
                bg=COLORS['background'], fg=color, anchor='w').pack(side=tk.RIGHT, fill='x', expand=True)
        
        tk.Label(row_frame, text=f"{amount:,.2f}", font=font,
                bg=COLORS['background'], fg=color, width=15, anchor='e').pack(side=tk.LEFT)
        
    def generate_report(self):
        """إنشاء تقرير الأرباح والخسائر"""
        try:
            # مسح التقرير الحالي
            for widget in self.scrollable_frame.winfo_children():
                widget.destroy()
                
            from_date, to_date = self.get_date_filter()
            
            # عنوان التقرير
            title_frame = tk.Frame(self.scrollable_frame, bg=COLORS['background'])
            title_frame.pack(fill='x', padx=10, pady=10)
            
            tk.Label(title_frame, text=f"بيان الأرباح والخسائر من {from_date} إلى {to_date}",
                    font=FONTS['title'], bg=COLORS['background'], fg=COLORS['primary']).pack()
            
            # حساب الإيرادات
            revenues = self.calculate_revenues(from_date, to_date)
            
            # حساب التكاليف
            costs = self.calculate_costs(from_date, to_date)
            
            # حساب المصروفات
            expenses = self.calculate_expenses(from_date, to_date)
            
            # عرض الإيرادات
            self.create_section_header("الإيرادات", COLORS['success'])
            self.create_data_row("إيرادات المبيعات", revenues['sales_revenue'], level=1)
            self.create_data_row("خصومات المبيعات", -revenues['sales_discounts'], level=1)
            self.create_data_row("مرتجعات المبيعات", -revenues['sales_returns'], level=1)
            self.create_data_row("صافي إيرادات المبيعات", revenues['net_sales'], bold=True)
            
            # عرض تكلفة البضاعة المباعة
            self.create_section_header("تكلفة البضاعة المباعة", COLORS['warning'])
            self.create_data_row("مخزون أول المدة", costs['opening_inventory'], level=1)
            self.create_data_row("المشتريات", costs['purchases'], level=1)
            self.create_data_row("خصومات المشتريات", -costs['purchase_discounts'], level=1)
            self.create_data_row("مرتجعات المشتريات", -costs['purchase_returns'], level=1)
            self.create_data_row("صافي المشتريات", costs['net_purchases'], level=1)
            self.create_data_row("البضاعة المتاحة للبيع", costs['goods_available'], level=1)
            self.create_data_row("مخزون آخر المدة", -costs['closing_inventory'], level=1)
            self.create_data_row("تكلفة البضاعة المباعة", costs['cost_of_goods_sold'], bold=True)
            
            # حساب إجمالي الربح
            gross_profit = revenues['net_sales'] - costs['cost_of_goods_sold']
            self.create_section_header("إجمالي الربح", COLORS['info'])
            self.create_data_row("إجمالي الربح", gross_profit, bold=True)
            
            # عرض المصروفات التشغيلية
            self.create_section_header("المصروفات التشغيلية", COLORS['danger'])
            self.create_data_row("مصروفات إدارية", expenses['administrative'], level=1)
            self.create_data_row("مصروفات بيعية وتسويقية", expenses['selling'], level=1)
            self.create_data_row("مصروفات عمومية", expenses['general'], level=1)
            total_expenses = expenses['administrative'] + expenses['selling'] + expenses['general']
            self.create_data_row("إجمالي المصروفات التشغيلية", total_expenses, bold=True)
            
            # حساب صافي الربح
            net_profit = gross_profit - total_expenses
            profit_color = COLORS['success'] if net_profit >= 0 else COLORS['danger']
            profit_text = "صافي الربح" if net_profit >= 0 else "صافي الخسارة"
            
            self.create_section_header(profit_text, profit_color)
            self.create_data_row(profit_text, abs(net_profit), bold=True)
            
            # حساب النسب المالية
            self.create_section_header("النسب المالية", COLORS['secondary'])
            if revenues['net_sales'] > 0:
                gross_profit_margin = (gross_profit / revenues['net_sales']) * 100
                net_profit_margin = (net_profit / revenues['net_sales']) * 100
                cost_ratio = (costs['cost_of_goods_sold'] / revenues['net_sales']) * 100
                
                self.create_data_row(f"هامش إجمالي الربح: {gross_profit_margin:.2f}%", 0, level=1)
                self.create_data_row(f"هامش صافي الربح: {net_profit_margin:.2f}%", 0, level=1)
                self.create_data_row(f"نسبة تكلفة البضاعة المباعة: {cost_ratio:.2f}%", 0, level=1)
            
            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "عرض تقرير الأرباح والخسائر",
                f"الفترة: من {from_date} إلى {to_date}",
                "reports"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")
            
    def calculate_revenues(self, from_date, to_date):
        """حساب الإيرادات"""
        try:
            # إيرادات المبيعات
            sales_query = """
                SELECT 
                    COALESCE(SUM(subtotal), 0) as sales_revenue,
                    COALESCE(SUM(discount_amount), 0) as sales_discounts
                FROM sales_invoices
                WHERE DATE(invoice_date) BETWEEN ? AND ?
            """
            
            sales_result = self.db_manager.execute_query(sales_query, [from_date, to_date])
            sales_data = sales_result[0] if sales_result else {'sales_revenue': 0, 'sales_discounts': 0}
            
            # مرتجعات المبيعات (افتراضي 0 - يمكن تطويره لاحقاً)
            sales_returns = 0
            
            net_sales = sales_data['sales_revenue'] - sales_data['sales_discounts'] - sales_returns
            
            return {
                'sales_revenue': sales_data['sales_revenue'],
                'sales_discounts': sales_data['sales_discounts'],
                'sales_returns': sales_returns,
                'net_sales': net_sales
            }
            
        except Exception as e:
            print(f"خطأ في حساب الإيرادات: {str(e)}")
            return {'sales_revenue': 0, 'sales_discounts': 0, 'sales_returns': 0, 'net_sales': 0}

    def calculate_costs(self, from_date, to_date):
        """حساب تكلفة البضاعة المباعة"""
        try:
            # المشتريات
            purchases_query = """
                SELECT
                    COALESCE(SUM(subtotal), 0) as purchases,
                    COALESCE(SUM(discount_amount), 0) as purchase_discounts
                FROM purchase_invoices
                WHERE DATE(invoice_date) BETWEEN ? AND ?
            """

            purchases_result = self.db_manager.execute_query(purchases_query, [from_date, to_date])
            purchases_data = purchases_result[0] if purchases_result else {'purchases': 0, 'purchase_discounts': 0}

            # مرتجعات المشتريات (افتراضي 0 - يمكن تطويره لاحقاً)
            purchase_returns = 0

            # صافي المشتريات
            net_purchases = purchases_data['purchases'] - purchases_data['purchase_discounts'] - purchase_returns

            # مخزون أول وآخر المدة (تقدير بناءً على المنتجات الحالية)
            inventory_query = """
                SELECT COALESCE(SUM(stock_quantity * selling_price * 0.7), 0) as inventory_value
                FROM products
                WHERE is_active = 1
            """

            inventory_result = self.db_manager.execute_query(inventory_query)
            current_inventory = inventory_result[0]['inventory_value'] if inventory_result else 0

            # افتراض أن مخزون أول المدة = مخزون آخر المدة (تبسيط)
            opening_inventory = current_inventory
            closing_inventory = current_inventory

            # البضاعة المتاحة للبيع
            goods_available = opening_inventory + net_purchases

            # تكلفة البضاعة المباعة (حساب دقيق بناءً على المبيعات الفعلية)
            cost_of_goods_sold = calculate_cost_of_goods_sold(self.db_manager, from_date, to_date)

            return {
                'opening_inventory': opening_inventory,
                'purchases': purchases_data['purchases'],
                'purchase_discounts': purchases_data['purchase_discounts'],
                'purchase_returns': purchase_returns,
                'net_purchases': net_purchases,
                'goods_available': goods_available,
                'closing_inventory': closing_inventory,
                'cost_of_goods_sold': cost_of_goods_sold
            }

        except Exception as e:
            print(f"خطأ في حساب التكاليف: {str(e)}")
            return {
                'opening_inventory': 0, 'purchases': 0, 'purchase_discounts': 0,
                'purchase_returns': 0, 'net_purchases': 0, 'goods_available': 0,
                'closing_inventory': 0, 'cost_of_goods_sold': 0
            }

    def calculate_expenses(self, from_date, to_date):
        """حساب المصروفات التشغيلية"""
        try:
            # في هذا التطبيق البسيط، سنحسب المصروفات كنسبة من المبيعات
            # يمكن تطوير هذا لاحقاً لإضافة جدول مصروفات منفصل

            # الحصول على إجمالي المبيعات
            sales_query = """
                SELECT COALESCE(SUM(total_amount), 0) as total_sales
                FROM sales_invoices
                WHERE DATE(invoice_date) BETWEEN ? AND ?
            """

            sales_result = self.db_manager.execute_query(sales_query, [from_date, to_date])
            total_sales = sales_result[0]['total_sales'] if sales_result else 0

            # تقدير المصروفات كنسب من المبيعات
            administrative = total_sales * 0.05  # 5% مصروفات إدارية
            selling = total_sales * 0.03         # 3% مصروفات بيعية
            general = total_sales * 0.02         # 2% مصروفات عمومية

            return {
                'administrative': administrative,
                'selling': selling,
                'general': general
            }

        except Exception as e:
            print(f"خطأ في حساب المصروفات: {str(e)}")
            return {'administrative': 0, 'selling': 0, 'general': 0}

    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة الطباعة قريباً")

    def export_report(self):
        """تصدير التقرير"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة التصدير قريباً")
