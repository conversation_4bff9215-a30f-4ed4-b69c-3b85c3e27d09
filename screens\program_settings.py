# -*- coding: utf-8 -*-
"""
شاشة إعدادات البرنامج
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import json
import os
from config.settings import COLORS, FONTS, COMPANY_INFO, DATABASE_PATH
from utils.database_manager import DatabaseManager
from utils.helpers import check_user_permission, show_permission_error, log_user_activity
from utils.arabic_support import ArabicSupport

class ProgramSettings:
    """كلاس إعدادات البرنامج"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'admin'):
            show_permission_error('إعدادات البرنامج')
            return
        
        self.settings_file = os.path.join(os.path.dirname(DATABASE_PATH), 'program_settings.json')
        self.load_current_settings()
        
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إعدادات البرنامج")
        self.window.geometry("800x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 800
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        self.current_settings = {
            'company': {
                'name': COMPANY_INFO.get('name', 'شركة المبيعات والمخازن'),
                'address': COMPANY_INFO.get('address', ''),
                'phone': COMPANY_INFO.get('phone', ''),
                'email': COMPANY_INFO.get('email', ''),
                'tax_number': COMPANY_INFO.get('tax_number', ''),
                'logo_path': COMPANY_INFO.get('logo_path', '')
            },
            'appearance': {
                'theme': 'default',
                'font_size': 'normal',
                'primary_color': COLORS.get('primary', '#2c3e50'),
                'success_color': COLORS.get('success', '#27ae60'),
                'danger_color': COLORS.get('danger', '#e74c3c'),
                'warning_color': COLORS.get('warning', '#f39c12'),
                'info_color': COLORS.get('info', '#3498db')
            },
            'system': {
                'auto_backup': True,
                'backup_interval': 7,  # أيام
                'max_backup_files': 10,
                'language': 'ar',
                'currency': 'ريال',
                'decimal_places': 2,
                'date_format': 'dd/mm/yyyy'
            },
            'security': {
                'session_timeout': 60,  # دقائق
                'password_min_length': 6,
                'require_strong_password': False,
                'max_login_attempts': 3,
                'lock_duration': 15  # دقائق
            },
            'printing': {
                'default_printer': '',
                'paper_size': 'A4',
                'margins': {'top': 20, 'bottom': 20, 'left': 20, 'right': 20},
                'print_logo': True,
                'print_colors': False
            }
        }
        
        # تحميل الإعدادات من الملف إذا كان موجوداً
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.merge_settings(saved_settings)
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {str(e)}")
                
    def merge_settings(self, saved_settings):
        """دمج الإعدادات المحفوظة مع الافتراضية"""
        for category, settings in saved_settings.items():
            if category in self.current_settings:
                for key, value in settings.items():
                    if key in self.current_settings[category]:
                        self.current_settings[category][key] = value
                        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إعدادات البرنامج",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار التبويبات
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill='both', expand=True, padx=20, pady=10)
        
        # تبويب معلومات الشركة
        self.create_company_tab(notebook)
        
        # تبويب المظهر
        self.create_appearance_tab(notebook)
        
        # تبويب النظام
        self.create_system_tab(notebook)
        
        # تبويب الأمان
        self.create_security_tab(notebook)
        
        # تبويب الطباعة
        self.create_printing_tab(notebook)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # زر الحفظ
        tk.Button(buttons_frame, text="حفظ الإعدادات", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.save_settings).pack(side=tk.RIGHT, padx=5)
        
        # زر الإلغاء
        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # زر استعادة الافتراضي
        tk.Button(buttons_frame, text="استعادة الافتراضي", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=15,
                 command=self.restore_defaults).pack(side=tk.LEFT, padx=5)
                 
    def create_company_tab(self, notebook):
        """إنشاء تبويب معلومات الشركة"""
        company_frame = ttk.Frame(notebook)
        notebook.add(company_frame, text="معلومات الشركة")
        
        # إطار المحتوى
        content_frame = tk.Frame(company_frame, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # اسم الشركة
        tk.Label(content_frame, text="اسم الشركة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        
        self.company_name_var = tk.StringVar(value=self.current_settings['company']['name'])
        tk.Entry(content_frame, textvariable=self.company_name_var, font=FONTS['normal'],
                width=50, justify='right').pack(pady=(0, 10))
        
        # عنوان الشركة
        tk.Label(content_frame, text="العنوان:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        
        self.company_address_var = tk.StringVar(value=self.current_settings['company']['address'])
        tk.Entry(content_frame, textvariable=self.company_address_var, font=FONTS['normal'],
                width=50, justify='right').pack(pady=(0, 10))
        
        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        
        self.company_phone_var = tk.StringVar(value=self.current_settings['company']['phone'])
        tk.Entry(content_frame, textvariable=self.company_phone_var, font=FONTS['normal'],
                width=50, justify='right').pack(pady=(0, 10))
        
        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        
        self.company_email_var = tk.StringVar(value=self.current_settings['company']['email'])
        tk.Entry(content_frame, textvariable=self.company_email_var, font=FONTS['normal'],
                width=50, justify='right').pack(pady=(0, 10))
        
        # الرقم الضريبي
        tk.Label(content_frame, text="الرقم الضريبي:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        
        self.company_tax_var = tk.StringVar(value=self.current_settings['company']['tax_number'])
        tk.Entry(content_frame, textvariable=self.company_tax_var, font=FONTS['normal'],
                width=50, justify='right').pack(pady=(0, 10))
        
        # شعار الشركة
        logo_frame = tk.Frame(content_frame, bg=COLORS['background'])
        logo_frame.pack(fill='x', pady=10)
        
        tk.Label(logo_frame, text="شعار الشركة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.logo_path_var = tk.StringVar(value=self.current_settings['company']['logo_path'])
        logo_entry = tk.Entry(logo_frame, textvariable=self.logo_path_var, font=FONTS['normal'],
                             width=40, justify='right')
        logo_entry.pack(side=tk.RIGHT, padx=5)
        
        tk.Button(logo_frame, text="تصفح", font=FONTS['button'],
                 bg=COLORS['info'], fg='white',
                 command=self.browse_logo).pack(side=tk.RIGHT, padx=5)
                 
    def create_appearance_tab(self, notebook):
        """إنشاء تبويب المظهر"""
        appearance_frame = ttk.Frame(notebook)
        notebook.add(appearance_frame, text="المظهر")
        
        # إطار المحتوى
        content_frame = tk.Frame(appearance_frame, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # حجم الخط
        tk.Label(content_frame, text="حجم الخط:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        
        self.font_size_var = tk.StringVar(value=self.current_settings['appearance']['font_size'])
        font_combo = ttk.Combobox(content_frame, textvariable=self.font_size_var,
                                 font=FONTS['normal'], width=20, state='readonly')
        font_combo['values'] = ['صغير', 'normal', 'كبير']
        font_combo.pack(pady=(0, 10))
        
        # الألوان
        colors_frame = tk.LabelFrame(content_frame, text="الألوان", font=FONTS['heading'],
                                    bg=COLORS['background'])
        colors_frame.pack(fill='x', pady=10)
        
        # اللون الأساسي
        self.create_color_picker(colors_frame, "اللون الأساسي:", 'primary_color')
        
        # لون النجاح
        self.create_color_picker(colors_frame, "لون النجاح:", 'success_color')
        
        # لون الخطر
        self.create_color_picker(colors_frame, "لون الخطر:", 'danger_color')
        
        # لون التحذير
        self.create_color_picker(colors_frame, "لون التحذير:", 'warning_color')
        
        # لون المعلومات
        self.create_color_picker(colors_frame, "لون المعلومات:", 'info_color')
        
    def create_color_picker(self, parent, label_text, color_key):
        """إنشاء منتقي لون"""
        color_frame = tk.Frame(parent, bg=COLORS['background'])
        color_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(color_frame, text=label_text, font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        # متغير اللون
        if not hasattr(self, f'{color_key}_var'):
            setattr(self, f'{color_key}_var', tk.StringVar(value=self.current_settings['appearance'][color_key]))
        
        color_var = getattr(self, f'{color_key}_var')
        
        # عرض اللون الحالي
        color_display = tk.Label(color_frame, text="     ", font=FONTS['normal'],
                               bg=color_var.get(), relief='solid', bd=1)
        color_display.pack(side=tk.RIGHT, padx=5)
        
        # زر اختيار اللون
        tk.Button(color_frame, text="اختيار", font=FONTS['small'],
                 bg=COLORS['info'], fg='white',
                 command=lambda: self.choose_color(color_var, color_display)).pack(side=tk.RIGHT, padx=5)

    def create_system_tab(self, notebook):
        """إنشاء تبويب النظام"""
        system_frame = ttk.Frame(notebook)
        notebook.add(system_frame, text="النظام")

        # إطار المحتوى
        content_frame = tk.Frame(system_frame, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # النسخ الاحتياطي التلقائي
        self.auto_backup_var = tk.BooleanVar(value=self.current_settings['system']['auto_backup'])
        tk.Checkbutton(content_frame, text="تفعيل النسخ الاحتياطي التلقائي",
                      variable=self.auto_backup_var, font=FONTS['normal'],
                      bg=COLORS['background']).pack(anchor='e', pady=5)

        # فترة النسخ الاحتياطي
        backup_frame = tk.Frame(content_frame, bg=COLORS['background'])
        backup_frame.pack(fill='x', pady=5)

        tk.Label(backup_frame, text="فترة النسخ الاحتياطي (أيام):", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.backup_interval_var = tk.StringVar(value=str(self.current_settings['system']['backup_interval']))
        tk.Entry(backup_frame, textvariable=self.backup_interval_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

        # عدد ملفات النسخ الاحتياطي
        max_backup_frame = tk.Frame(content_frame, bg=COLORS['background'])
        max_backup_frame.pack(fill='x', pady=5)

        tk.Label(max_backup_frame, text="عدد ملفات النسخ الاحتياطي المحفوظة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.max_backup_var = tk.StringVar(value=str(self.current_settings['system']['max_backup_files']))
        tk.Entry(max_backup_frame, textvariable=self.max_backup_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

        # العملة
        currency_frame = tk.Frame(content_frame, bg=COLORS['background'])
        currency_frame.pack(fill='x', pady=5)

        tk.Label(currency_frame, text="العملة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.currency_var = tk.StringVar(value=self.current_settings['system']['currency'])
        tk.Entry(currency_frame, textvariable=self.currency_var, font=FONTS['normal'],
                width=20, justify='right').pack(side=tk.RIGHT, padx=5)

        # عدد الخانات العشرية
        decimal_frame = tk.Frame(content_frame, bg=COLORS['background'])
        decimal_frame.pack(fill='x', pady=5)

        tk.Label(decimal_frame, text="عدد الخانات العشرية:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.decimal_places_var = tk.StringVar(value=str(self.current_settings['system']['decimal_places']))
        decimal_combo = ttk.Combobox(decimal_frame, textvariable=self.decimal_places_var,
                                    font=FONTS['normal'], width=10, state='readonly')
        decimal_combo['values'] = ['0', '1', '2', '3', '4']
        decimal_combo.pack(side=tk.RIGHT, padx=5)

        # تنسيق التاريخ
        date_frame = tk.Frame(content_frame, bg=COLORS['background'])
        date_frame.pack(fill='x', pady=5)

        tk.Label(date_frame, text="تنسيق التاريخ:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.date_format_var = tk.StringVar(value=self.current_settings['system']['date_format'])
        date_combo = ttk.Combobox(date_frame, textvariable=self.date_format_var,
                                 font=FONTS['normal'], width=15, state='readonly')
        date_combo['values'] = ['dd/mm/yyyy', 'mm/dd/yyyy', 'yyyy-mm-dd']
        date_combo.pack(side=tk.RIGHT, padx=5)

    def create_security_tab(self, notebook):
        """إنشاء تبويب الأمان"""
        security_frame = ttk.Frame(notebook)
        notebook.add(security_frame, text="الأمان")

        # إطار المحتوى
        content_frame = tk.Frame(security_frame, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # مهلة انتهاء الجلسة
        session_frame = tk.Frame(content_frame, bg=COLORS['background'])
        session_frame.pack(fill='x', pady=5)

        tk.Label(session_frame, text="مهلة انتهاء الجلسة (دقائق):", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.session_timeout_var = tk.StringVar(value=str(self.current_settings['security']['session_timeout']))
        tk.Entry(session_frame, textvariable=self.session_timeout_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

        # الحد الأدنى لطول كلمة المرور
        password_frame = tk.Frame(content_frame, bg=COLORS['background'])
        password_frame.pack(fill='x', pady=5)

        tk.Label(password_frame, text="الحد الأدنى لطول كلمة المرور:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.password_min_var = tk.StringVar(value=str(self.current_settings['security']['password_min_length']))
        tk.Entry(password_frame, textvariable=self.password_min_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

        # كلمة مرور قوية
        self.strong_password_var = tk.BooleanVar(value=self.current_settings['security']['require_strong_password'])
        tk.Checkbutton(content_frame, text="طلب كلمة مرور قوية (أحرف وأرقام ورموز)",
                      variable=self.strong_password_var, font=FONTS['normal'],
                      bg=COLORS['background']).pack(anchor='e', pady=5)

        # عدد محاولات تسجيل الدخول
        attempts_frame = tk.Frame(content_frame, bg=COLORS['background'])
        attempts_frame.pack(fill='x', pady=5)

        tk.Label(attempts_frame, text="عدد محاولات تسجيل الدخول المسموحة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.max_attempts_var = tk.StringVar(value=str(self.current_settings['security']['max_login_attempts']))
        tk.Entry(attempts_frame, textvariable=self.max_attempts_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

        # مدة القفل
        lock_frame = tk.Frame(content_frame, bg=COLORS['background'])
        lock_frame.pack(fill='x', pady=5)

        tk.Label(lock_frame, text="مدة قفل الحساب (دقائق):", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.lock_duration_var = tk.StringVar(value=str(self.current_settings['security']['lock_duration']))
        tk.Entry(lock_frame, textvariable=self.lock_duration_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

    def create_printing_tab(self, notebook):
        """إنشاء تبويب الطباعة"""
        printing_frame = ttk.Frame(notebook)
        notebook.add(printing_frame, text="الطباعة")

        # إطار المحتوى
        content_frame = tk.Frame(printing_frame, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # حجم الورق
        paper_frame = tk.Frame(content_frame, bg=COLORS['background'])
        paper_frame.pack(fill='x', pady=5)

        tk.Label(paper_frame, text="حجم الورق:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.paper_size_var = tk.StringVar(value=self.current_settings['printing']['paper_size'])
        paper_combo = ttk.Combobox(paper_frame, textvariable=self.paper_size_var,
                                  font=FONTS['normal'], width=15, state='readonly')
        paper_combo['values'] = ['A4', 'A5', 'Letter', 'Legal']
        paper_combo.pack(side=tk.RIGHT, padx=5)

        # طباعة الشعار
        self.print_logo_var = tk.BooleanVar(value=self.current_settings['printing']['print_logo'])
        tk.Checkbutton(content_frame, text="طباعة شعار الشركة",
                      variable=self.print_logo_var, font=FONTS['normal'],
                      bg=COLORS['background']).pack(anchor='e', pady=5)

        # طباعة ملونة
        self.print_colors_var = tk.BooleanVar(value=self.current_settings['printing']['print_colors'])
        tk.Checkbutton(content_frame, text="طباعة ملونة",
                      variable=self.print_colors_var, font=FONTS['normal'],
                      bg=COLORS['background']).pack(anchor='e', pady=5)

        # الهوامش
        margins_frame = tk.LabelFrame(content_frame, text="الهوامش (مم)", font=FONTS['heading'],
                                     bg=COLORS['background'])
        margins_frame.pack(fill='x', pady=10)

        # الهامش العلوي
        top_margin_frame = tk.Frame(margins_frame, bg=COLORS['background'])
        top_margin_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(top_margin_frame, text="الهامش العلوي:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.top_margin_var = tk.StringVar(value=str(self.current_settings['printing']['margins']['top']))
        tk.Entry(top_margin_frame, textvariable=self.top_margin_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

        # الهامش السفلي
        bottom_margin_frame = tk.Frame(margins_frame, bg=COLORS['background'])
        bottom_margin_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(bottom_margin_frame, text="الهامش السفلي:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.bottom_margin_var = tk.StringVar(value=str(self.current_settings['printing']['margins']['bottom']))
        tk.Entry(bottom_margin_frame, textvariable=self.bottom_margin_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

        # الهامش الأيمن
        right_margin_frame = tk.Frame(margins_frame, bg=COLORS['background'])
        right_margin_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(right_margin_frame, text="الهامش الأيمن:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.right_margin_var = tk.StringVar(value=str(self.current_settings['printing']['margins']['right']))
        tk.Entry(right_margin_frame, textvariable=self.right_margin_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

        # الهامش الأيسر
        left_margin_frame = tk.Frame(margins_frame, bg=COLORS['background'])
        left_margin_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(left_margin_frame, text="الهامش الأيسر:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.left_margin_var = tk.StringVar(value=str(self.current_settings['printing']['margins']['left']))
        tk.Entry(left_margin_frame, textvariable=self.left_margin_var, font=FONTS['normal'],
                width=10, justify='center').pack(side=tk.RIGHT, padx=5)

    def browse_logo(self):
        """تصفح ملف الشعار"""
        file_types = [
            ('ملفات الصور', '*.png *.jpg *.jpeg *.gif *.bmp'),
            ('جميع الملفات', '*.*')
        ]

        filename = filedialog.askopenfilename(
            title="اختيار شعار الشركة",
            filetypes=file_types
        )

        if filename:
            self.logo_path_var.set(filename)

    def choose_color(self, color_var, color_display):
        """اختيار لون"""
        color = colorchooser.askcolor(
            title="اختيار لون",
            initialcolor=color_var.get()
        )

        if color[1]:  # إذا تم اختيار لون
            color_var.set(color[1])
            color_display.config(bg=color[1])

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # تجميع الإعدادات من الواجهة
            new_settings = {
                'company': {
                    'name': self.company_name_var.get().strip(),
                    'address': self.company_address_var.get().strip(),
                    'phone': self.company_phone_var.get().strip(),
                    'email': self.company_email_var.get().strip(),
                    'tax_number': self.company_tax_var.get().strip(),
                    'logo_path': self.logo_path_var.get().strip()
                },
                'appearance': {
                    'theme': 'default',
                    'font_size': self.font_size_var.get(),
                    'primary_color': self.primary_color_var.get(),
                    'success_color': self.success_color_var.get(),
                    'danger_color': self.danger_color_var.get(),
                    'warning_color': self.warning_color_var.get(),
                    'info_color': self.info_color_var.get()
                },
                'system': {
                    'auto_backup': self.auto_backup_var.get(),
                    'backup_interval': int(self.backup_interval_var.get() or 7),
                    'max_backup_files': int(self.max_backup_var.get() or 10),
                    'language': 'ar',
                    'currency': self.currency_var.get().strip() or 'ريال',
                    'decimal_places': int(self.decimal_places_var.get() or 2),
                    'date_format': self.date_format_var.get()
                },
                'security': {
                    'session_timeout': int(self.session_timeout_var.get() or 60),
                    'password_min_length': int(self.password_min_var.get() or 6),
                    'require_strong_password': self.strong_password_var.get(),
                    'max_login_attempts': int(self.max_attempts_var.get() or 3),
                    'lock_duration': int(self.lock_duration_var.get() or 15)
                },
                'printing': {
                    'default_printer': '',
                    'paper_size': self.paper_size_var.get(),
                    'margins': {
                        'top': int(self.top_margin_var.get() or 20),
                        'bottom': int(self.bottom_margin_var.get() or 20),
                        'left': int(self.left_margin_var.get() or 20),
                        'right': int(self.right_margin_var.get() or 20)
                    },
                    'print_logo': self.print_logo_var.get(),
                    'print_colors': self.print_colors_var.get()
                }
            }

            # التحقق من صحة البيانات
            if not new_settings['company']['name']:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الشركة")
                return

            # حفظ الإعدادات في الملف
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, ensure_ascii=False, indent=4)

            # تحديث إعدادات البرنامج
            self.update_program_settings(new_settings)

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "تحديث إعدادات البرنامج",
                "تم تحديث إعدادات البرنامج بنجاح",
                "settings"
            )

            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح\nسيتم تطبيق بعض الإعدادات عند إعادة تشغيل البرنامج")
            self.window.destroy()

        except ValueError as e:
            messagebox.showerror("خطأ", "يرجى التأكد من صحة القيم الرقمية المدخلة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الإعدادات:\n{str(e)}")

    def update_program_settings(self, new_settings):
        """تحديث إعدادات البرنامج في الذاكرة"""
        try:
            # تحديث معلومات الشركة
            COMPANY_INFO.update(new_settings['company'])

            # تحديث الألوان
            COLORS.update({
                'primary': new_settings['appearance']['primary_color'],
                'success': new_settings['appearance']['success_color'],
                'danger': new_settings['appearance']['danger_color'],
                'warning': new_settings['appearance']['warning_color'],
                'info': new_settings['appearance']['info_color']
            })

        except Exception as e:
            print(f"خطأ في تحديث إعدادات البرنامج: {str(e)}")

    def restore_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        result = messagebox.askyesno(
            "تأكيد",
            "هل أنت متأكد من استعادة الإعدادات الافتراضية؟\nسيتم فقدان جميع الإعدادات الحالية."
        )

        if result:
            try:
                # حذف ملف الإعدادات
                if os.path.exists(self.settings_file):
                    os.remove(self.settings_file)

                # إعادة تحميل الإعدادات الافتراضية
                self.load_current_settings()

                # إعادة إنشاء النافذة
                self.window.destroy()
                ProgramSettings(self.parent, self.current_user)

                messagebox.showinfo("نجح", "تم استعادة الإعدادات الافتراضية بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في استعادة الإعدادات:\n{str(e)}")

    @staticmethod
    def load_settings():
        """تحميل الإعدادات المحفوظة (دالة ثابتة للاستخدام في أجزاء أخرى من البرنامج)"""
        settings_file = os.path.join(os.path.dirname(DATABASE_PATH), 'program_settings.json')

        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {str(e)}")

        return None
