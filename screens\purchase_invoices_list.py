# -*- coding: utf-8 -*-
"""
شاشة قائمة فواتير المشتريات
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import (get_date_range, check_user_permission, show_permission_error, 
                          log_user_activity, format_currency)
from utils.arabic_support import ArabicSupport

class PurchaseInvoicesList:
    """كلاس قائمة فواتير المشتريات"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'purchases_management'):
            show_permission_error('عرض قائمة فواتير المشتريات')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_suppliers()
        self.load_invoices()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("قائمة فواتير المشتريات")
        self.window.geometry("1400x800")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1400
        height = 800
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="قائمة فواتير المشتريات",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الفلاتر
        filters_frame = tk.LabelFrame(self.window, text="فلاتر البحث", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        filters_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول - فلاتر التاريخ والمورد
        row1_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # من تاريخ
        tk.Label(row1_frame, text="من تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.from_date_var = tk.StringVar(value=get_current_date())
        from_date_entry = tk.Entry(row1_frame, textvariable=self.from_date_var, 
                                  font=FONTS['normal'], width=12, justify='right')
        from_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # إلى تاريخ
        tk.Label(row1_frame, text="إلى تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.to_date_var = tk.StringVar(value=get_current_date())
        to_date_entry = tk.Entry(row1_frame, textvariable=self.to_date_var, 
                                font=FONTS['normal'], width=12, justify='right')
        to_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # المورد
        tk.Label(row1_frame, text="المورد:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.supplier_var = tk.StringVar()
        self.supplier_combo = ttk.Combobox(row1_frame, textvariable=self.supplier_var,
                                          font=FONTS['normal'], width=20, state='readonly')
        self.supplier_combo.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني - فلاتر الحالة والبحث
        row2_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        # حالة الدفع
        tk.Label(row2_frame, text="حالة الدفع:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.payment_status_var = tk.StringVar()
        payment_status_combo = ttk.Combobox(row2_frame, textvariable=self.payment_status_var,
                                           font=FONTS['normal'], width=15, state='readonly')
        payment_status_combo['values'] = ['الكل', 'معلق', 'مدفوع جزئياً', 'مدفوع', 'متأخر']
        payment_status_combo.set('الكل')
        payment_status_combo.pack(side=tk.RIGHT, padx=5)
        
        # البحث
        tk.Label(row2_frame, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(row2_frame, textvariable=self.search_var, 
                               font=FONTS['normal'], width=20)
        search_entry.pack(side=tk.RIGHT, padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # الصف الثالث - فترات سريعة وأزرار
        row3_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row3_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row3_frame, text="فترات سريعة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        quick_periods = [
            ('اليوم', 'today'),
            ('هذا الأسبوع', 'this_week'),
            ('هذا الشهر', 'this_month'),
            ('الشهر الماضي', 'last_month')
        ]
        
        for text, period in quick_periods:
            tk.Button(row3_frame, text=text, font=FONTS['small'],
                     bg=COLORS['info'], fg='white', 
                     command=lambda p=period: self.set_quick_period(p)).pack(side=tk.RIGHT, padx=2)
        
        # زر البحث
        tk.Button(row3_frame, text="بحث", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', 
                 command=self.load_invoices).pack(side=tk.RIGHT, padx=10)
        
        # زر مسح الفلاتر
        tk.Button(row3_frame, text="مسح الفلاتر", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', 
                 command=self.clear_filters).pack(side=tk.RIGHT, padx=5)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['heading'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء عناصر الإحصائيات
        self.create_statistics_widgets(stats_frame)
        
        # إطار قائمة الفواتير
        invoices_frame = tk.LabelFrame(self.window, text="قائمة الفواتير", 
                                      font=FONTS['heading'], bg=COLORS['background'])
        invoices_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء جدول الفواتير
        self.create_invoices_table(invoices_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # أزرار الإجراءات
        if check_user_permission(self.current_user['role'], 'purchases_create'):
            tk.Button(buttons_frame, text="فاتورة جديدة", font=FONTS['button'],
                     bg=COLORS['success'], fg='white', width=15,
                     command=self.new_invoice).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="عرض الفاتورة", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.view_invoice).pack(side=tk.LEFT, padx=5)
        
        if check_user_permission(self.current_user['role'], 'purchases_edit'):
            tk.Button(buttons_frame, text="تعديل", font=FONTS['button'],
                     bg=COLORS['warning'], fg='white', width=15,
                     command=self.edit_invoice).pack(side=tk.LEFT, padx=5)
        
        if check_user_permission(self.current_user['role'], 'purchases_delete'):
            tk.Button(buttons_frame, text="حذف", font=FONTS['button'],
                     bg=COLORS['danger'], fg='white', width=15,
                     command=self.delete_invoice).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', width=15,
                 command=self.print_invoice).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=15,
                 command=self.load_invoices).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def create_statistics_widgets(self, parent):
        """إنشاء عناصر الإحصائيات"""
        stats_container = tk.Frame(parent, bg=COLORS['background'])
        stats_container.pack(fill='x', padx=10, pady=10)
        
        self.stats_labels = {}
        stats_info = [
            ('total_invoices', 'إجمالي الفواتير', COLORS['primary']),
            ('total_amount', 'إجمالي المبلغ', COLORS['success']),
            ('paid_amount', 'المبلغ المدفوع', COLORS['info']),
            ('remaining_amount', 'المبلغ المتبقي', COLORS['danger'])
        ]
        
        for i, (key, label, color) in enumerate(stats_info):
            stat_frame = tk.Frame(stats_container, bg=COLORS['background'], 
                                 relief='raised', bd=1)
            stat_frame.pack(side=tk.RIGHT, padx=10, pady=5, fill='x', expand=True)
            
            tk.Label(stat_frame, text=label, font=FONTS['small'], 
                    bg=COLORS['background'], fg=COLORS['text']).pack(pady=(5, 0))
            
            self.stats_labels[key] = tk.Label(stat_frame, text="0", font=FONTS['heading'], 
                                            bg=COLORS['background'], fg=color)
            self.stats_labels[key].pack(pady=(0, 5))
            
    def create_invoices_table(self, parent):
        """إنشاء جدول الفواتير"""
        table_frame = tk.Frame(parent, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تحديد الأعمدة
        columns = ('رقم الفاتورة', 'التاريخ', 'المورد', 'المجموع الفرعي', 'الخصم', 
                  'الضريبة', 'المجموع الكلي', 'المدفوع', 'المتبقي', 'حالة الدفع', 'المستخدم')
        
        self.invoices_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة وعرضها
        column_widths = {
            'رقم الفاتورة': 120,
            'التاريخ': 100,
            'المورد': 150,
            'المجموع الفرعي': 100,
            'الخصم': 80,
            'الضريبة': 80,
            'المجموع الكلي': 100,
            'المدفوع': 100,
            'المتبقي': 100,
            'حالة الدفع': 100,
            'المستخدم': 120
        }
        
        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.invoices_tree.xview)
        self.invoices_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.invoices_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.invoices_tree.bind('<Double-1>', self.on_invoice_double_click)
        
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            query = "SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name"
            suppliers = self.db_manager.execute_query(query)
            
            supplier_list = ['الكل']
            for supplier in suppliers:
                supplier_list.append(f"{supplier['id']}: {supplier['name']}")
            
            self.supplier_combo['values'] = supplier_list
            self.supplier_combo.set('الكل')
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الموردين:\n{str(e)}")
            
    def set_quick_period(self, period):
        """تعيين فترة سريعة"""
        try:
            start_date, end_date = get_date_range(period)
            self.from_date_var.set(start_date.strftime('%Y-%m-%d'))
            self.to_date_var.set(end_date.strftime('%Y-%m-%d'))
            self.load_invoices()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين الفترة:\n{str(e)}")
            
    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.from_date_var.set(get_current_date())
        self.to_date_var.set(get_current_date())
        self.supplier_combo.set('الكل')
        self.payment_status_var.set('الكل')
        self.search_var.set('')
        self.load_invoices()
        
    def on_search_change(self, event=None):
        """معالج تغيير البحث"""
        # تأخير البحث لتجنب البحث مع كل حرف
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        self.search_timer = self.window.after(500, self.load_invoices)

    def load_invoices(self):
        """تحميل قائمة الفواتير"""
        try:
            # مسح البيانات الحالية
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)

            # بناء الاستعلام
            query = """
                SELECT pi.id, pi.invoice_number, pi.invoice_date, s.name as supplier_name,
                       pi.subtotal, pi.discount_amount, pi.tax_amount, pi.total_amount,
                       pi.paid_amount, pi.remaining_amount, pi.payment_status,
                       u.name as user_name
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                LEFT JOIN users u ON pi.user_id = u.id
                WHERE 1=1
            """

            params = []

            # فلتر التاريخ
            from_date = self.from_date_var.get()
            to_date = self.to_date_var.get()
            if from_date and to_date:
                query += " AND DATE(pi.invoice_date) BETWEEN ? AND ?"
                params.extend([from_date, to_date])

            # فلتر المورد
            supplier_filter = self.supplier_var.get()
            if supplier_filter and supplier_filter != 'الكل':
                supplier_id = int(supplier_filter.split(':')[0])
                query += " AND pi.supplier_id = ?"
                params.append(supplier_id)

            # فلتر حالة الدفع
            payment_status = self.payment_status_var.get()
            if payment_status and payment_status != 'الكل':
                status_map = {
                    'معلق': 'pending',
                    'مدفوع جزئياً': 'partial',
                    'مدفوع': 'paid',
                    'متأخر': 'overdue'
                }
                if payment_status in status_map:
                    query += " AND pi.payment_status = ?"
                    params.append(status_map[payment_status])

            # فلتر البحث
            search_term = self.search_var.get().strip()
            if search_term:
                query += " AND (pi.invoice_number LIKE ? OR s.name LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern])

            query += " ORDER BY pi.invoice_date DESC, pi.id DESC"

            # تنفيذ الاستعلام
            invoices = self.db_manager.execute_query(query, params)

            # عرض البيانات
            total_invoices = 0
            total_amount = 0
            paid_amount = 0
            remaining_amount = 0

            status_names = {
                'pending': 'معلق',
                'partial': 'مدفوع جزئياً',
                'paid': 'مدفوع',
                'overdue': 'متأخر'
            }

            for invoice in invoices:
                # تحديد لون الصف حسب حالة الدفع
                tags = []
                if invoice['payment_status'] == 'paid':
                    tags = ['paid']
                elif invoice['payment_status'] == 'overdue':
                    tags = ['overdue']
                elif invoice['payment_status'] == 'partial':
                    tags = ['partial']

                self.invoices_tree.insert('', 'end', values=(
                    invoice['invoice_number'],
                    invoice['invoice_date'],
                    invoice['supplier_name'] or 'مورد نقدي',
                    f"{invoice['subtotal']:.2f}",
                    f"{invoice['discount_amount']:.2f}",
                    f"{invoice['tax_amount']:.2f}",
                    f"{invoice['total_amount']:.2f}",
                    f"{invoice['paid_amount']:.2f}",
                    f"{invoice['remaining_amount']:.2f}",
                    status_names.get(invoice['payment_status'], invoice['payment_status']),
                    invoice['user_name'] or ''
                ), tags=tags)

                # تحديث الإحصائيات
                total_invoices += 1
                total_amount += invoice['total_amount']
                paid_amount += invoice['paid_amount']
                remaining_amount += invoice['remaining_amount']

            # تكوين ألوان الصفوف
            self.invoices_tree.tag_configure('paid', background='#d4edda')
            self.invoices_tree.tag_configure('overdue', background='#f8d7da')
            self.invoices_tree.tag_configure('partial', background='#fff3cd')

            # تحديث الإحصائيات
            self.update_statistics(total_invoices, total_amount, paid_amount, remaining_amount)

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "عرض قائمة فواتير المشتريات",
                f"تم عرض {total_invoices} فاتورة",
                "purchase_invoices"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفواتير:\n{str(e)}")

    def update_statistics(self, total_invoices, total_amount, paid_amount, remaining_amount):
        """تحديث الإحصائيات"""
        self.stats_labels['total_invoices'].config(text=str(total_invoices))
        self.stats_labels['total_amount'].config(text=f"{total_amount:,.2f}")
        self.stats_labels['paid_amount'].config(text=f"{paid_amount:,.2f}")
        self.stats_labels['remaining_amount'].config(text=f"{remaining_amount:,.2f}")

    def get_selected_invoice_id(self):
        """الحصول على معرف الفاتورة المحددة"""
        selection = self.invoices_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة أولاً")
            return None

        item = self.invoices_tree.item(selection[0])
        invoice_number = item['values'][0]

        # البحث عن معرف الفاتورة
        query = "SELECT id FROM purchase_invoices WHERE invoice_number = ?"
        result = self.db_manager.execute_query(query, [invoice_number])

        if result:
            return result[0]['id']
        return None

    def on_invoice_double_click(self, event):
        """معالج النقر المزدوج على الفاتورة"""
        self.view_invoice()

    def new_invoice(self):
        """إنشاء فاتورة جديدة"""
        from screens.purchases_management import PurchaseInvoiceDialog
        PurchaseInvoiceDialog(self.window, self.current_user, self.load_invoices)

    def view_invoice(self):
        """عرض تفاصيل الفاتورة"""
        invoice_id = self.get_selected_invoice_id()
        if invoice_id:
            try:
                # جلب بيانات الفاتورة مع بيانات المورد
                query = """
                    SELECT pi.*, s.name as supplier_name, s.phone as supplier_phone,
                           u.name as user_name
                    FROM purchase_invoices pi
                    LEFT JOIN suppliers s ON pi.supplier_id = s.id
                    LEFT JOIN users u ON pi.user_id = u.id
                    WHERE pi.id = ?
                """
                result = self.db_manager.execute_query(query, [invoice_id])

                if result:
                    invoice = result[0]

                    # جلب عناصر الفاتورة
                    items_query = """
                        SELECT pii.*, p.name as product_name, p.unit
                        FROM purchase_invoice_items pii
                        LEFT JOIN products p ON pii.product_id = p.id
                        WHERE pii.invoice_id = ?
                        ORDER BY pii.id
                    """
                    items = self.db_manager.execute_query(items_query, [invoice_id])

                    # إنشاء نافذة عرض التفاصيل
                    self.show_invoice_details(invoice, items)

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في عرض الفاتورة:\n{str(e)}")

    def show_invoice_details(self, invoice, items):
        """عرض نافذة تفاصيل الفاتورة"""
        details_window = tk.Toplevel(self.window)
        details_window.title(f"تفاصيل فاتورة الشراء - {invoice['invoice_number']}")
        details_window.geometry("800x600")
        details_window.configure(bg=COLORS['background'])
        details_window.transient(self.window)

        # توسيط النافذة
        details_window.update_idletasks()
        x = (details_window.winfo_screenwidth() // 2) - (400)
        y = (details_window.winfo_screenheight() // 2) - (300)
        details_window.geometry(f'800x600+{x}+{y}')

        # العنوان
        title_label = tk.Label(details_window,
                              text=f"فاتورة شراء رقم: {invoice['invoice_number']}",
                              font=FONTS['title'], bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)

        # إطار المعلومات الأساسية
        info_frame = tk.LabelFrame(details_window, text="معلومات الفاتورة",
                                  font=FONTS['heading'], bg=COLORS['background'])
        info_frame.pack(fill='x', padx=20, pady=10)

        # معلومات الفاتورة
        info_text = f"""
التاريخ: {invoice['invoice_date']}
المورد: {invoice['supplier_name'] or 'مورد نقدي'}
{f"هاتف المورد: {invoice['supplier_phone']}" if invoice['supplier_phone'] else ""}
المستخدم: {invoice['user_name']}

المجموع الفرعي: {invoice['subtotal']:.2f} ريال
الخصم: {invoice['discount_amount']:.2f} ريال
الضريبة: {invoice['tax_amount']:.2f} ريال
المجموع الكلي: {invoice['total_amount']:.2f} ريال
المدفوع: {invoice['paid_amount']:.2f} ريال
المتبقي: {invoice['remaining_amount']:.2f} ريال
حالة الدفع: {self.get_payment_status_text(invoice['payment_status'])}
        """

        info_label = tk.Label(info_frame, text=info_text.strip(),
                             font=FONTS['normal'], bg=COLORS['background'],
                             justify='right', anchor='e')
        info_label.pack(padx=20, pady=10)

        # إطار العناصر
        items_frame = tk.LabelFrame(details_window, text="عناصر الفاتورة",
                                   font=FONTS['heading'], bg=COLORS['background'])
        items_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول العناصر
        items_columns = ('المنتج', 'الكمية', 'الوحدة', 'السعر', 'الإجمالي')
        items_tree = ttk.Treeview(items_frame, columns=items_columns, show='headings', height=10)

        for col in items_columns:
            items_tree.heading(col, text=col)
            items_tree.column(col, width=120, anchor='center')

        # إضافة العناصر
        for item in items:
            items_tree.insert('', 'end', values=(
                item['product_name'] or f"منتج {item['product_id']}",
                f"{item['quantity']:.2f}",
                item['unit'] or 'قطعة',
                f"{item['unit_price']:.2f}",
                f"{item['total_price']:.2f}"
            ))

        items_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # زر الإغلاق
        tk.Button(details_window, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=details_window.destroy).pack(pady=10)

    def get_payment_status_text(self, status):
        """تحويل حالة الدفع إلى نص عربي"""
        status_map = {
            'pending': 'معلق',
            'partial': 'مدفوع جزئياً',
            'paid': 'مدفوع',
            'overdue': 'متأخر'
        }
        return status_map.get(status, status)

    def edit_invoice(self):
        """تعديل الفاتورة"""
        invoice_id = self.get_selected_invoice_id()
        if invoice_id:
            try:
                # التحقق من حالة الفاتورة
                query = "SELECT payment_status FROM purchase_invoices WHERE id = ?"
                result = self.db_manager.execute_query(query, [invoice_id])

                if result and result[0]['payment_status'] == 'paid':
                    messagebox.showwarning("تحذير", "لا يمكن تعديل فاتورة مدفوعة بالكامل")
                    return

                # فتح نافذة التعديل
                from screens.purchases_management import PurchaseInvoiceDialog
                PurchaseInvoiceDialog(self.window, self.current_user, self.load_invoices, invoice_id)

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في فتح نافذة التعديل:\n{str(e)}")

    def delete_invoice(self):
        """حذف الفاتورة"""
        invoice_id = self.get_selected_invoice_id()
        if not invoice_id:
            return

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الفاتورة؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                # حذف عناصر الفاتورة أولاً
                self.db_manager.execute_query("DELETE FROM purchase_invoice_items WHERE invoice_id = ?", [invoice_id])

                # حذف المدفوعات المرتبطة
                self.db_manager.execute_query("DELETE FROM payments WHERE invoice_type = 'purchase' AND invoice_id = ?", [invoice_id])

                # حذف الفاتورة
                self.db_manager.execute_query("DELETE FROM purchase_invoices WHERE id = ?", [invoice_id])

                # تسجيل العملية
                log_user_activity(
                    self.db_manager,
                    self.current_user['id'],
                    "حذف فاتورة شراء",
                    f"تم حذف فاتورة الشراء رقم {invoice_id}",
                    "purchase_invoices",
                    invoice_id
                )

                messagebox.showinfo("نجح", "تم حذف الفاتورة بنجاح")
                self.load_invoices()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف الفاتورة:\n{str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        invoice_id = self.get_selected_invoice_id()
        if invoice_id:
            try:
                # جلب بيانات الفاتورة للطباعة
                query = """
                    SELECT pi.*, s.name as supplier_name, s.phone as supplier_phone,
                           s.address as supplier_address, u.name as user_name
                    FROM purchase_invoices pi
                    LEFT JOIN suppliers s ON pi.supplier_id = s.id
                    LEFT JOIN users u ON pi.user_id = u.id
                    WHERE pi.id = ?
                """
                result = self.db_manager.execute_query(query, [invoice_id])

                if result:
                    invoice = result[0]

                    # جلب عناصر الفاتورة
                    items_query = """
                        SELECT pii.*, p.name as product_name, p.unit
                        FROM purchase_invoice_items pii
                        LEFT JOIN products p ON pii.product_id = p.id
                        WHERE pii.invoice_id = ?
                        ORDER BY pii.id
                    """
                    items = self.db_manager.execute_query(items_query, [invoice_id])

                    # إنشاء نافذة معاينة الطباعة
                    self.show_print_preview(invoice, items)

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في إعداد الطباعة:\n{str(e)}")

    def show_print_preview(self, invoice, items):
        """عرض معاينة الطباعة"""
        print_window = tk.Toplevel(self.window)
        print_window.title(f"معاينة طباعة فاتورة الشراء - {invoice['invoice_number']}")
        print_window.geometry("800x700")
        print_window.configure(bg='white')
        print_window.transient(self.window)

        # توسيط النافذة
        print_window.update_idletasks()
        x = (print_window.winfo_screenwidth() // 2) - (400)
        y = (print_window.winfo_screenheight() // 2) - (350)
        print_window.geometry(f'800x700+{x}+{y}')

        # إطار المحتوى
        content_frame = tk.Frame(print_window, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # إنشاء محتوى الفاتورة
        invoice_content = self.generate_invoice_content(invoice, items)

        # منطقة النص
        text_widget = tk.Text(content_frame, font=FONTS['normal'], bg='white',
                             fg='black', wrap=tk.WORD, state='disabled')
        text_widget.pack(fill='both', expand=True)

        # إدراج المحتوى
        text_widget.config(state='normal')
        text_widget.insert('1.0', invoice_content)
        text_widget.config(state='disabled')

        # أزرار التحكم
        buttons_frame = tk.Frame(print_window, bg='white')
        buttons_frame.pack(fill='x', pady=10)

        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=lambda: self.execute_print(text_widget)).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=print_window.destroy).pack(side=tk.RIGHT, padx=5)

    def generate_invoice_content(self, invoice, items):
        """إنشاء محتوى الفاتورة للطباعة"""
        content = f"""
{'='*60}
                    فاتورة شراء
{'='*60}

رقم الفاتورة: {invoice['invoice_number']}
التاريخ: {invoice['invoice_date']}
المستخدم: {invoice['user_name']}

معلومات المورد:
الاسم: {invoice['supplier_name'] or 'مورد نقدي'}
{f"الهاتف: {invoice['supplier_phone']}" if invoice['supplier_phone'] else ""}
{f"العنوان: {invoice['supplier_address']}" if invoice['supplier_address'] else ""}

{'='*60}
                    تفاصيل المشتريات
{'='*60}

"""

        # إضافة العناصر
        content += f"{'المنتج':<30} {'الكمية':<10} {'السعر':<10} {'الإجمالي':<10}\n"
        content += "-" * 60 + "\n"

        for item in items:
            product_name = item['product_name'] or f"منتج {item['product_id']}"
            content += f"{product_name:<30} {item['quantity']:<10.2f} {item['unit_price']:<10.2f} {item['total_price']:<10.2f}\n"

        content += "-" * 60 + "\n"

        # إضافة المجاميع
        content += f"""
المجموع الفرعي: {invoice['subtotal']:.2f} ريال
الخصم: {invoice['discount_amount']:.2f} ريال
الضريبة: {invoice['tax_amount']:.2f} ريال
{'='*30}
المجموع الكلي: {invoice['total_amount']:.2f} ريال

المدفوع: {invoice['paid_amount']:.2f} ريال
المتبقي: {invoice['remaining_amount']:.2f} ريال
حالة الدفع: {self.get_payment_status_text(invoice['payment_status'])}

{'='*60}
شكراً لتعاملكم معنا
{'='*60}
"""

        return content

    def execute_print(self, text_widget):
        """تنفيذ عملية الطباعة"""
        try:
            import subprocess
            import tempfile

            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                content = text_widget.get('1.0', tk.END)
                f.write(content)
                temp_file = f.name

            # فتح الملف للطباعة (Windows)
            subprocess.run(['notepad', '/p', temp_file], check=True)

            messagebox.showinfo("نجح", "تم إرسال الفاتورة للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في الطباعة:\n{str(e)}")
