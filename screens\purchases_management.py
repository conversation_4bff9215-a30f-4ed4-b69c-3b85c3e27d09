# -*- coding: utf-8 -*-
"""
شاشة إدارة المشتريات
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from config.settings import COLORS, FONTS, INVOICE_SETTINGS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import format_currency, calculate_tax, calculate_discount, is_valid_number
from utils.arabic_support import ArabicSupport

class PurchasesManagement:
    """كلاس إدارة المشتريات"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        self.setup_window()
        self.create_widgets()
        self.load_invoices()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة المشتريات")
        self.window.geometry("1200x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1200
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة المشتريات",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)
        
        # الأزرار
        tk.Button(buttons_frame, text="فاتورة شراء جديدة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=self.new_purchase_invoice).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="عرض الفاتورة", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', command=self.view_invoice).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="طباعة الفاتورة", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', command=self.print_invoice).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="حذف الفاتورة", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=self.delete_invoice).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', command=self.load_invoices).pack(side=tk.RIGHT, padx=5)
        
        # إطار البحث والفلترة
        filter_frame = tk.Frame(self.window, bg=COLORS['background'])
        filter_frame.pack(pady=5)
        
        # البحث
        tk.Label(filter_frame, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = tk.Entry(filter_frame, textvariable=self.search_var, 
                               font=FONTS['normal'], width=20, justify='right')
        search_entry.pack(side=tk.RIGHT, padx=5)
        
        # فلتر حالة الدفع
        tk.Label(filter_frame, text="حالة الدفع:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.payment_status_var = tk.StringVar(value='الكل')
        status_combo = ttk.Combobox(filter_frame, textvariable=self.payment_status_var, 
                                   font=FONTS['normal'], width=15, state='readonly')
        status_combo['values'] = ['الكل', 'معلق', 'مدفوع جزئياً', 'مدفوع', 'متأخر']
        status_combo.pack(side=tk.RIGHT, padx=5)
        status_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # إطار جدول الفواتير
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview للفواتير
        columns = ('رقم الفاتورة', 'المورد', 'التاريخ', 'المجموع', 'المدفوع', 'المتبقي', 'حالة الدفع', 'المستخدم')
        self.invoices_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        # تحديد عناوين الأعمدة
        for col in columns:
            self.invoices_tree.heading(col, text=col)
            if col in ['المجموع', 'المدفوع', 'المتبقي']:
                self.invoices_tree.column(col, width=100, anchor='center')
            elif col == 'التاريخ':
                self.invoices_tree.column(col, width=100, anchor='center')
            else:
                self.invoices_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب العناصر
        self.invoices_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج بعرض الفاتورة
        self.invoices_tree.bind('<Double-1>', lambda e: self.view_invoice())
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['normal'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        self.stats_labels = {}
        stats_info = [
            ('total_invoices', 'إجمالي الفواتير'),
            ('total_amount', 'إجمالي المشتريات'),
            ('paid_amount', 'المبلغ المدفوع'),
            ('pending_amount', 'المبلغ المعلق')
        ]
        
        for i, (key, label) in enumerate(stats_info):
            frame = tk.Frame(stats_frame, bg=COLORS['background'])
            frame.pack(side=tk.RIGHT, padx=10, pady=5)
            
            tk.Label(frame, text=f"{label}:", font=FONTS['small'], 
                    bg=COLORS['background']).pack()
            
            self.stats_labels[key] = tk.Label(frame, text="0", font=FONTS['normal'], 
                                            bg=COLORS['background'], fg=COLORS['primary'])
            self.stats_labels[key].pack()
        
        # تحديث الإحصائيات
        self.update_statistics()
        
    def load_invoices(self):
        """تحميل قائمة الفواتير"""
        try:
            # مسح البيانات الحالية
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)
                
            # جلب الفواتير من قاعدة البيانات
            query = """
                SELECT pi.invoice_number, s.name as supplier_name, pi.invoice_date,
                       pi.total_amount, pi.paid_amount, pi.remaining_amount,
                       pi.payment_status, u.name as user_name, pi.id
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                LEFT JOIN users u ON pi.user_id = u.id
                ORDER BY pi.created_at DESC
            """
            invoices = self.db_manager.execute_query(query)
            
            # إضافة الفواتير إلى الجدول
            for invoice in invoices:
                # تحويل حالة الدفع إلى العربية
                status_map = {
                    'pending': 'معلق',
                    'partial': 'مدفوع جزئياً',
                    'paid': 'مدفوع',
                    'overdue': 'متأخر'
                }
                status_text = status_map.get(invoice['payment_status'], 'غير محدد')
                
                self.invoices_tree.insert('', 'end', values=(
                    invoice['invoice_number'],
                    invoice['supplier_name'] or 'مورد نقدي',
                    invoice['invoice_date'],
                    f"{invoice['total_amount']:.2f}",
                    f"{invoice['paid_amount']:.2f}",
                    f"{invoice['remaining_amount']:.2f}",
                    status_text,
                    invoice['user_name']
                ), tags=(invoice['id'],))
                
            # تحديث الإحصائيات
            self.update_statistics()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفواتير:\n{str(e)}")
            
    def on_search_change(self, *args):
        """معالج تغيير البحث"""
        self.apply_filters()
        
    def on_filter_change(self, *args):
        """معالج تغيير الفلتر"""
        self.apply_filters()
        
    def apply_filters(self):
        """تطبيق الفلاتر والبحث"""
        search_term = self.search_var.get().strip()
        payment_status = self.payment_status_var.get()
        
        try:
            # مسح البيانات الحالية
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)
                
            # بناء الاستعلام
            query = """
                SELECT pi.invoice_number, s.name as supplier_name, pi.invoice_date,
                       pi.total_amount, pi.paid_amount, pi.remaining_amount,
                       pi.payment_status, u.name as user_name, pi.id
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                LEFT JOIN users u ON pi.user_id = u.id
                WHERE 1=1
            """
            params = []
            
            # إضافة شرط البحث
            if search_term:
                query += " AND (pi.invoice_number LIKE ? OR s.name LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern])
            
            # إضافة شرط حالة الدفع
            if payment_status != 'الكل':
                status_map = {
                    'معلق': 'pending',
                    'مدفوع جزئياً': 'partial',
                    'مدفوع': 'paid',
                    'متأخر': 'overdue'
                }
                if payment_status in status_map:
                    query += " AND pi.payment_status = ?"
                    params.append(status_map[payment_status])
            
            query += " ORDER BY pi.created_at DESC"
            
            invoices = self.db_manager.execute_query(query, params)
            
            # إضافة النتائج إلى الجدول
            for invoice in invoices:
                status_map = {
                    'pending': 'معلق',
                    'partial': 'مدفوع جزئياً',
                    'paid': 'مدفوع',
                    'overdue': 'متأخر'
                }
                status_text = status_map.get(invoice['payment_status'], 'غير محدد')
                
                self.invoices_tree.insert('', 'end', values=(
                    invoice['invoice_number'],
                    invoice['supplier_name'] or 'مورد نقدي',
                    invoice['invoice_date'],
                    f"{invoice['total_amount']:.2f}",
                    f"{invoice['paid_amount']:.2f}",
                    f"{invoice['remaining_amount']:.2f}",
                    status_text,
                    invoice['user_name']
                ), tags=(invoice['id'],))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تطبيق الفلاتر:\n{str(e)}")
            
    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # إحصائيات عامة
            query = """
                SELECT 
                    COUNT(*) as total_invoices,
                    COALESCE(SUM(total_amount), 0) as total_amount,
                    COALESCE(SUM(paid_amount), 0) as paid_amount,
                    COALESCE(SUM(remaining_amount), 0) as pending_amount
                FROM purchase_invoices
                WHERE DATE(created_at) = DATE('now')
            """
            result = self.db_manager.execute_query(query)
            
            if result:
                stats = result[0]
                self.stats_labels['total_invoices'].config(text=str(stats['total_invoices']))
                self.stats_labels['total_amount'].config(text=f"{stats['total_amount']:.2f}")
                self.stats_labels['paid_amount'].config(text=f"{stats['paid_amount']:.2f}")
                self.stats_labels['pending_amount'].config(text=f"{stats['pending_amount']:.2f}")
                
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
            
    def get_selected_invoice(self):
        """الحصول على الفاتورة المحددة"""
        selection = self.invoices_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة من القائمة")
            return None
            
        item = self.invoices_tree.item(selection[0])
        invoice_id = item['tags'][0] if item['tags'] else None
        
        if invoice_id:
            # جلب بيانات الفاتورة الكاملة
            query = "SELECT * FROM purchase_invoices WHERE id = ?"
            invoices = self.db_manager.execute_query(query, (invoice_id,))
            
            if invoices:
                return dict(invoices[0])
        return None
        
    def new_purchase_invoice(self):
        """إنشاء فاتورة شراء جديدة"""
        PurchaseInvoiceDialog(self.window, self.current_user, self.load_invoices)
        
    def view_invoice(self):
        """عرض تفاصيل الفاتورة"""
        invoice = self.get_selected_invoice()
        if invoice:
            messagebox.showinfo("قريباً", f"سيتم عرض تفاصيل الفاتورة رقم {invoice['invoice_number']} قريباً")
            
    def print_invoice(self):
        """طباعة الفاتورة"""
        invoice = self.get_selected_invoice()
        if invoice:
            messagebox.showinfo("قريباً", f"سيتم طباعة الفاتورة رقم {invoice['invoice_number']} قريباً")
            
    def delete_invoice(self):
        """حذف الفاتورة"""
        invoice = self.get_selected_invoice()
        if not invoice:
            return
            
        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", 
                                   f"هل أنت متأكد من حذف الفاتورة رقم {invoice['invoice_number']}؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه.")
        
        if result:
            try:
                # حذف تفاصيل الفاتورة أولاً
                self.db_manager.execute_query("DELETE FROM purchase_invoice_items WHERE invoice_id = ?", (invoice['id'],))
                
                # حذف الفاتورة
                self.db_manager.execute_query("DELETE FROM purchase_invoices WHERE id = ?", (invoice['id'],))
                
                messagebox.showinfo("نجح", "تم حذف الفاتورة بنجاح")
                self.load_invoices()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف الفاتورة:\n{str(e)}")

class PurchaseInvoiceDialog:
    """حوار إنشاء فاتورة شراء"""

    def __init__(self, parent, current_user, callback=None):
        self.parent = parent
        self.current_user = current_user
        self.callback = callback
        self.db_manager = DatabaseManager()
        self.invoice_items = []

        self.setup_window()
        self.create_widgets()
        self.load_suppliers()
        self.load_products()
        self.generate_invoice_number()

    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("فاتورة شراء جديدة")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="فاتورة شراء جديدة",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)

        # إطار معلومات الفاتورة
        info_frame = tk.LabelFrame(self.window, text="معلومات الفاتورة",
                                  font=FONTS['heading'], bg=COLORS['background'])
        info_frame.pack(fill='x', padx=20, pady=(0, 10))

        # الصف الأول
        row1_frame = tk.Frame(info_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)

        # رقم الفاتورة
        tk.Label(row1_frame, text="رقم الفاتورة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.invoice_number_var = tk.StringVar()
        invoice_number_entry = tk.Entry(row1_frame, textvariable=self.invoice_number_var,
                                       font=FONTS['normal'], width=15, justify='right', state='readonly')
        invoice_number_entry.pack(side=tk.RIGHT, padx=5)

        # التاريخ
        tk.Label(row1_frame, text="التاريخ:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))

        self.invoice_date_var = tk.StringVar(value=get_current_date())
        date_entry = tk.Entry(row1_frame, textvariable=self.invoice_date_var,
                             font=FONTS['normal'], width=12, justify='right')
        date_entry.pack(side=tk.RIGHT, padx=5)

        # الصف الثاني
        row2_frame = tk.Frame(info_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)

        # المورد
        tk.Label(row2_frame, text="المورد:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.supplier_var = tk.StringVar()
        self.supplier_combo = ttk.Combobox(row2_frame, textvariable=self.supplier_var,
                                          font=FONTS['normal'], width=25, state='readonly')
        self.supplier_combo.pack(side=tk.RIGHT, padx=5)

        # إطار الأصناف (مشابه لفاتورة المبيعات)
        items_frame = tk.LabelFrame(self.window, text="أصناف الفاتورة",
                                   font=FONTS['heading'], bg=COLORS['background'])
        items_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))

        # إطار إضافة صنف
        add_item_frame = tk.Frame(items_frame, bg=COLORS['background'])
        add_item_frame.pack(fill='x', padx=10, pady=5)

        # المنتج
        tk.Label(add_item_frame, text="المنتج:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(add_item_frame, textvariable=self.product_var,
                                         font=FONTS['normal'], width=20, state='readonly')
        self.product_combo.pack(side=tk.RIGHT, padx=5)
        self.product_combo.bind('<<ComboboxSelected>>', self.on_product_selected)

        # الكمية
        tk.Label(add_item_frame, text="الكمية:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(10, 5))

        self.quantity_var = tk.StringVar(value='1')
        quantity_entry = tk.Entry(add_item_frame, textvariable=self.quantity_var,
                                 font=FONTS['normal'], width=8, justify='right')
        quantity_entry.pack(side=tk.RIGHT, padx=5)

        # السعر
        tk.Label(add_item_frame, text="سعر الشراء:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(10, 5))

        self.price_var = tk.StringVar()
        price_entry = tk.Entry(add_item_frame, textvariable=self.price_var,
                              font=FONTS['normal'], width=10, justify='right')
        price_entry.pack(side=tk.RIGHT, padx=5)

        # زر إضافة
        tk.Button(add_item_frame, text="إضافة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=self.add_item).pack(side=tk.RIGHT, padx=10)

        # جدول الأصناف
        table_frame = tk.Frame(items_frame, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('المنتج', 'الكمية', 'سعر الشراء', 'الخصم', 'المجموع')
        self.items_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.items_tree.heading(col, text=col)
            if col in ['الكمية', 'سعر الشراء', 'الخصم', 'المجموع']:
                self.items_tree.column(col, width=80, anchor='center')
            else:
                self.items_tree.column(col, width=200, anchor='center')

        # شريط التمرير للجدول
        items_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)

        self.items_tree.pack(side='left', fill='both', expand=True)
        items_scrollbar.pack(side='right', fill='y')

        # أزرار إدارة الأصناف
        items_buttons_frame = tk.Frame(items_frame, bg=COLORS['background'])
        items_buttons_frame.pack(fill='x', padx=10, pady=5)

        tk.Button(items_buttons_frame, text="حذف الصنف", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=self.remove_item).pack(side=tk.RIGHT, padx=5)

        # إطار المجاميع
        totals_frame = tk.LabelFrame(self.window, text="المجاميع",
                                    font=FONTS['heading'], bg=COLORS['background'])
        totals_frame.pack(fill='x', padx=20, pady=(0, 10))

        # المتغيرات للمجاميع
        self.subtotal_var = tk.StringVar(value='0.00')
        self.discount_var = tk.StringVar(value='0')
        self.tax_var = tk.StringVar(value=str(INVOICE_SETTINGS['tax_rate']))
        self.total_var = tk.StringVar(value='0.00')

        # الصف الأول من المجاميع
        totals_row1 = tk.Frame(totals_frame, bg=COLORS['background'])
        totals_row1.pack(fill='x', padx=10, pady=5)

        # المجموع الفرعي
        tk.Label(totals_row1, text="المجموع الفرعي:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        tk.Label(totals_row1, textvariable=self.subtotal_var, font=FONTS['normal'],
                bg=COLORS['background'], fg=COLORS['primary']).pack(side=tk.RIGHT, padx=5)

        # الخصم
        tk.Label(totals_row1, text="الخصم (%):", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        discount_entry = tk.Entry(totals_row1, textvariable=self.discount_var,
                                 font=FONTS['normal'], width=8, justify='right')
        discount_entry.pack(side=tk.RIGHT, padx=5)
        discount_entry.bind('<KeyRelease>', self.calculate_totals)

        # الصف الثاني من المجاميع
        totals_row2 = tk.Frame(totals_frame, bg=COLORS['background'])
        totals_row2.pack(fill='x', padx=10, pady=5)

        # الضريبة
        tk.Label(totals_row2, text="الضريبة (%):", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        tax_entry = tk.Entry(totals_row2, textvariable=self.tax_var,
                            font=FONTS['normal'], width=8, justify='right')
        tax_entry.pack(side=tk.RIGHT, padx=5)
        tax_entry.bind('<KeyRelease>', self.calculate_totals)

        # المجموع الكلي
        tk.Label(totals_row2, text="المجموع الكلي:", font=FONTS['heading'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        tk.Label(totals_row2, textvariable=self.total_var, font=FONTS['heading'],
                bg=COLORS['background'], fg=COLORS['success']).pack(side=tk.RIGHT, padx=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)

        tk.Button(buttons_frame, text="حفظ الفاتورة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=self.save_invoice).pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            query = "SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name"
            suppliers = self.db_manager.execute_query(query)

            supplier_values = ['مورد نقدي']
            supplier_values.extend([f"{supplier['id']}:{supplier['name']}" for supplier in suppliers])

            self.supplier_combo['values'] = supplier_values
            self.supplier_combo.set('مورد نقدي')

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الموردين:\n{str(e)}")

    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            query = """
                SELECT id, name, cost_price
                FROM products
                WHERE is_active = 1
                ORDER BY name
            """
            products = self.db_manager.execute_query(query)

            product_values = [f"{product['id']}:{product['name']}" for product in products]

            self.product_combo['values'] = product_values
            self.products_data = {product['id']: product for product in products}

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المنتجات:\n{str(e)}")

    def generate_invoice_number(self):
        """إنشاء رقم فاتورة جديد"""
        try:
            invoice_number = self.db_manager.get_next_invoice_number('purchase')
            self.invoice_number_var.set(invoice_number)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء رقم الفاتورة:\n{str(e)}")

    def on_product_selected(self, event=None):
        """معالج اختيار المنتج"""
        try:
            product_selection = self.product_var.get()
            if product_selection:
                product_id = int(product_selection.split(':')[0])
                if product_id in self.products_data:
                    product = self.products_data[product_id]
                    self.price_var.set(str(product['cost_price']))
        except Exception as e:
            print(f"خطأ في اختيار المنتج: {str(e)}")

    def add_item(self):
        """إضافة صنف إلى الفاتورة"""
        try:
            # التحقق من البيانات
            if not self.product_var.get():
                messagebox.showerror("خطأ", "يرجى اختيار منتج")
                return

            if not is_valid_number(self.quantity_var.get(), 0.1):
                messagebox.showerror("خطأ", "الكمية غير صحيحة")
                return

            if not is_valid_number(self.price_var.get(), 0):
                messagebox.showerror("خطأ", "السعر غير صحيح")
                return

            # استخراج معرف المنتج
            product_id = int(self.product_var.get().split(':')[0])
            product = self.products_data[product_id]

            quantity = float(self.quantity_var.get())
            price = float(self.price_var.get())

            # التحقق من عدم تكرار المنتج
            for item in self.invoice_items:
                if item['product_id'] == product_id:
                    messagebox.showerror("خطأ", "هذا المنتج موجود مسبقاً في الفاتورة")
                    return

            # إضافة الصنف
            total = quantity * price
            item = {
                'product_id': product_id,
                'product_name': product['name'],
                'quantity': quantity,
                'price': price,
                'discount': 0,
                'total': total
            }

            self.invoice_items.append(item)

            # إضافة إلى الجدول
            self.items_tree.insert('', 'end', values=(
                product['name'],
                f"{quantity:.2f}",
                f"{price:.2f}",
                "0.00",
                f"{total:.2f}"
            ))

            # مسح الحقول
            self.product_var.set('')
            self.quantity_var.set('1')
            self.price_var.set('')

            # إعادة حساب المجاميع
            self.calculate_totals()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إضافة الصنف:\n{str(e)}")

    def remove_item(self):
        """حذف صنف من الفاتورة"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف لحذفه")
            return

        # الحصول على فهرس الصنف
        item_index = self.items_tree.index(selection[0])

        # حذف من القائمة والجدول
        del self.invoice_items[item_index]
        self.items_tree.delete(selection[0])

        # إعادة حساب المجاميع
        self.calculate_totals()

    def calculate_totals(self, event=None):
        """حساب المجاميع"""
        try:
            # حساب المجموع الفرعي
            subtotal = sum(item['total'] for item in self.invoice_items)
            self.subtotal_var.set(f"{subtotal:.2f}")

            # حساب الخصم
            discount_rate = float(self.discount_var.get() or 0)
            discount_amount = calculate_discount(subtotal, discount_rate)

            # حساب الضريبة
            tax_rate = float(self.tax_var.get() or 0)
            taxable_amount = subtotal - discount_amount
            tax_amount = calculate_tax(taxable_amount, tax_rate)

            # حساب المجموع الكلي
            total = taxable_amount + tax_amount
            self.total_var.set(f"{total:.2f}")

        except Exception as e:
            print(f"خطأ في حساب المجاميع: {str(e)}")

    def save_invoice(self):
        """حفظ فاتورة الشراء"""
        try:
            # التحقق من البيانات
            if not self.invoice_items:
                messagebox.showerror("خطأ", "يرجى إضافة أصناف إلى الفاتورة")
                return

            if not self.invoice_date_var.get():
                messagebox.showerror("خطأ", "يرجى إدخال تاريخ الفاتورة")
                return

            # استخراج معرف المورد
            supplier_id = None
            if self.supplier_var.get() != 'مورد نقدي':
                supplier_id = int(self.supplier_var.get().split(':')[0])

            # حساب المجاميع النهائية
            subtotal = sum(item['total'] for item in self.invoice_items)
            discount_rate = float(self.discount_var.get() or 0)
            discount_amount = calculate_discount(subtotal, discount_rate)
            tax_rate = float(self.tax_var.get() or 0)
            taxable_amount = subtotal - discount_amount
            tax_amount = calculate_tax(taxable_amount, tax_rate)
            total_amount = taxable_amount + tax_amount

            # حفظ الفاتورة
            invoice_query = """
                INSERT INTO purchase_invoices (
                    invoice_number, supplier_id, user_id, invoice_date,
                    subtotal, discount_amount, tax_amount, total_amount,
                    paid_amount, remaining_amount, payment_status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            invoice_params = (
                self.invoice_number_var.get(),
                supplier_id,
                self.current_user['id'],
                self.invoice_date_var.get(),
                subtotal,
                discount_amount,
                tax_amount,
                total_amount,
                0,  # المبلغ المدفوع (صفر في البداية)
                total_amount,  # المبلغ المتبقي
                'pending'  # حالة الدفع
            )

            # تنفيذ الاستعلام والحصول على معرف الفاتورة
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            try:
                cursor.execute(invoice_query, invoice_params)
                invoice_id = cursor.lastrowid

                # حفظ تفاصيل الفاتورة
                for item in self.invoice_items:
                    item_query = """
                        INSERT INTO purchase_invoice_items (
                            invoice_id, product_id, quantity, unit_price,
                            discount_amount, total_amount
                        ) VALUES (?, ?, ?, ?, ?, ?)
                    """

                    item_params = (
                        invoice_id,
                        item['product_id'],
                        item['quantity'],
                        item['price'],
                        item['discount'],
                        item['total']
                    )

                    cursor.execute(item_query, item_params)

                    # تحديث المخزون (إضافة للمخزون في حالة الشراء)
                    update_stock_query = """
                        UPDATE products
                        SET stock_quantity = stock_quantity + ?, cost_price = ?
                        WHERE id = ?
                    """
                    cursor.execute(update_stock_query, (item['quantity'], item['price'], item['product_id']))

                    # إضافة حركة مخزون
                    movement_query = """
                        INSERT INTO inventory_movements (
                            product_id, movement_type, quantity, reference_type,
                            reference_id, user_id, movement_date
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """

                    movement_params = (
                        item['product_id'],
                        'in',  # دخول للمخزون
                        item['quantity'],
                        'purchase',
                        invoice_id,
                        self.current_user['id'],
                        self.invoice_date_var.get()
                    )

                    cursor.execute(movement_query, movement_params)

                conn.commit()
                messagebox.showinfo("نجح", f"تم حفظ فاتورة الشراء رقم {self.invoice_number_var.get()} بنجاح")

                if self.callback:
                    self.callback()

                self.window.destroy()

            except Exception as e:
                conn.rollback()
                raise e
            finally:
                conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الفاتورة:\n{str(e)}")
