# -*- coding: utf-8 -*-
"""
شاشة تقارير المشتريات
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import (format_currency, get_date_range, format_date, 
                          check_user_permission, show_permission_error, log_user_activity)
from utils.arabic_support import ArabicSupport

class PurchasesReports:
    """كلاس تقارير المشتريات"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'reports_view'):
            show_permission_error('عرض تقارير المشتريات')
            return
        
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تقارير المشتريات")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="تقارير المشتريات",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار فلاتر التاريخ
        date_frame = tk.LabelFrame(self.window, text="فترة التقرير", 
                                  font=FONTS['heading'], bg=COLORS['background'])
        date_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول
        row1_frame = tk.Frame(date_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # من تاريخ
        tk.Label(row1_frame, text="من تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.from_date_var = tk.StringVar(value=get_current_date())
        from_date_entry = tk.Entry(row1_frame, textvariable=self.from_date_var, 
                                  font=FONTS['normal'], width=12, justify='right')
        from_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # إلى تاريخ
        tk.Label(row1_frame, text="إلى تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.to_date_var = tk.StringVar(value=get_current_date())
        to_date_entry = tk.Entry(row1_frame, textvariable=self.to_date_var, 
                                font=FONTS['normal'], width=12, justify='right')
        to_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني - فترات سريعة
        row2_frame = tk.Frame(date_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row2_frame, text="فترات سريعة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        quick_periods = [
            ('اليوم', 'today'),
            ('أمس', 'yesterday'),
            ('هذا الأسبوع', 'this_week'),
            ('الأسبوع الماضي', 'last_week'),
            ('هذا الشهر', 'this_month'),
            ('الشهر الماضي', 'last_month')
        ]
        
        for text, period in quick_periods:
            tk.Button(row2_frame, text=text, font=FONTS['small'],
                     bg=COLORS['info'], fg='white', 
                     command=lambda p=period: self.set_quick_period(p)).pack(side=tk.RIGHT, padx=2)
        
        # إطار أنواع التقارير
        reports_frame = tk.LabelFrame(self.window, text="أنواع تقارير المشتريات", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        reports_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء الأزرار في شبكة
        reports_grid = tk.Frame(reports_frame, bg=COLORS['background'])
        reports_grid.pack(expand=True, padx=20, pady=20)
        
        # الصف الأول من التقارير
        row1_reports = tk.Frame(reports_grid, bg=COLORS['background'])
        row1_reports.pack(fill='x', pady=10)
        
        tk.Button(row1_reports, text="تقرير المشتريات التفصيلي", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=25, height=3,
                 command=self.purchases_detailed_report).pack(side=tk.RIGHT, padx=10)
        
        tk.Button(row1_reports, text="تقرير المشتريات الإجمالي", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=25, height=3,
                 command=self.purchases_summary_report).pack(side=tk.RIGHT, padx=10)
        
        # الصف الثاني من التقارير
        row2_reports = tk.Frame(reports_grid, bg=COLORS['background'])
        row2_reports.pack(fill='x', pady=10)
        
        tk.Button(row2_reports, text="تقرير الموردين", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', width=25, height=3,
                 command=self.suppliers_report).pack(side=tk.RIGHT, padx=10)
        
        tk.Button(row2_reports, text="تقرير المشتريات حسب المنتج", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=25, height=3,
                 command=self.purchases_by_product_report).pack(side=tk.RIGHT, padx=10)
        
        # الصف الثالث من التقارير
        row3_reports = tk.Frame(reports_grid, bg=COLORS['background'])
        row3_reports.pack(fill='x', pady=10)
        
        tk.Button(row3_reports, text="تقرير المشتريات حسب المورد", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=25, height=3,
                 command=self.purchases_by_supplier_report).pack(side=tk.RIGHT, padx=10)
        
        tk.Button(row3_reports, text="تقرير المديونيات للموردين", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=25, height=3,
                 command=self.suppliers_debts_report).pack(side=tk.RIGHT, padx=10)
        
        # إطار الإحصائيات السريعة
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['normal'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        self.stats_labels = {}
        stats_info = [
            ('total_purchases', 'إجمالي المشتريات'),
            ('total_invoices', 'عدد الفواتير'),
            ('total_suppliers', 'عدد الموردين'),
            ('avg_invoice', 'متوسط الفاتورة')
        ]
        
        stats_row = tk.Frame(stats_frame, bg=COLORS['background'])
        stats_row.pack(pady=10)
        
        for key, label in stats_info:
            frame = tk.Frame(stats_row, bg=COLORS['background'])
            frame.pack(side=tk.RIGHT, padx=15)
            
            tk.Label(frame, text=f"{label}:", font=FONTS['small'], 
                    bg=COLORS['background']).pack()
            
            self.stats_labels[key] = tk.Label(frame, text="0", font=FONTS['normal'], 
                                            bg=COLORS['background'], fg=COLORS['primary'])
            self.stats_labels[key].pack()
        
        # تحديث الإحصائيات عند فتح النافذة
        self.update_statistics()
        
        # أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(buttons_frame, text="تحديث الإحصائيات", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.update_statistics).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def set_quick_period(self, period):
        """تعيين فترة سريعة"""
        try:
            start_date, end_date = get_date_range(period)
            self.from_date_var.set(start_date.strftime('%Y-%m-%d'))
            self.to_date_var.set(end_date.strftime('%Y-%m-%d'))
            self.update_statistics()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين الفترة:\n{str(e)}")
            
    def get_date_filter(self):
        """الحصول على فلتر التاريخ"""
        return self.from_date_var.get(), self.to_date_var.get()
        
    def update_statistics(self):
        """تحديث الإحصائيات السريعة"""
        try:
            from_date, to_date = self.get_date_filter()
            
            # إجمالي المشتريات
            query = """
                SELECT SUM(total_amount) as total_purchases,
                       COUNT(*) as total_invoices,
                       COUNT(DISTINCT supplier_id) as total_suppliers,
                       AVG(total_amount) as avg_invoice
                FROM purchase_invoices
                WHERE DATE(invoice_date) BETWEEN ? AND ?
            """
            
            result = self.db_manager.execute_query(query, [from_date, to_date])
            
            if result and result[0]:
                data = result[0]
                self.stats_labels['total_purchases'].config(text=f"{data['total_purchases'] or 0:.2f}")
                self.stats_labels['total_invoices'].config(text=str(data['total_invoices'] or 0))
                self.stats_labels['total_suppliers'].config(text=str(data['total_suppliers'] or 0))
                self.stats_labels['avg_invoice'].config(text=f"{data['avg_invoice'] or 0:.2f}")
            else:
                for key in self.stats_labels:
                    self.stats_labels[key].config(text="0")
                    
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
            
    def show_report_data(self, title, data, columns):
        """عرض بيانات التقرير في نافذة منفصلة"""
        # تسجيل العملية
        try:
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "عرض تقرير",
                f"عرض {title}",
                "reports"
            )
        except:
            pass
            
        report_window = tk.Toplevel(self.window)
        report_window.title(title)
        report_window.geometry("1200x700")
        report_window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(report_window)
        
        # العنوان
        title_label = tk.Label(report_window, text=title, font=FONTS['title'],
                              bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)
        
        # الفترة
        from_date, to_date = self.get_date_filter()
        period_label = tk.Label(report_window, text=f"من {from_date} إلى {to_date}",
                               font=FONTS['normal'], bg=COLORS['background'])
        period_label.pack(pady=5)
        
        # إطار الجدول
        table_frame = tk.Frame(report_window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=25)
        
        for col in columns:
            tree.heading(col, text=col)
            if 'مبلغ' in col or 'سعر' in col or 'قيمة' in col or 'إجمالي' in col:
                tree.column(col, width=120, anchor='center')
            elif 'تاريخ' in col:
                tree.column(col, width=100, anchor='center')
            else:
                tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # إضافة البيانات
        for row in data:
            tree.insert('', 'end', values=row)
        
        # ترتيب العناصر
        tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # أزرار
        buttons_frame = tk.Frame(report_window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)
        
        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', 
                 command=lambda: self.print_report(title, data, columns)).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تصدير Excel", font=FONTS['button'],
                 bg=COLORS['success'], fg='white',
                 command=lambda: self.export_to_excel(title, data, columns)).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white',
                 command=report_window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def print_report(self, title, data, columns):
        """طباعة التقرير"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة الطباعة قريباً")
        
    def export_to_excel(self, title, data, columns):
        """تصدير التقرير إلى Excel"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة التصدير قريباً")

    # تقارير المشتريات
    def purchases_detailed_report(self):
        """تقرير المشتريات التفصيلي"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT pi.invoice_number as 'رقم الفاتورة',
                       s.name as 'المورد',
                       pi.invoice_date as 'التاريخ',
                       p.name as 'المنتج',
                       pii.quantity as 'الكمية',
                       pii.unit_price as 'السعر',
                       pii.total_amount as 'المجموع',
                       u.name as 'المستخدم'
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                LEFT JOIN purchase_invoice_items pii ON pi.id = pii.invoice_id
                LEFT JOIN products p ON pii.product_id = p.id
                LEFT JOIN users u ON pi.user_id = u.id
                WHERE DATE(pi.invoice_date) BETWEEN ? AND ?
                ORDER BY pi.invoice_date DESC, pi.invoice_number
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            total_amount = 0
            for row in data:
                amount = row['المجموع'] or 0
                total_amount += amount

                display_data.append([
                    row['رقم الفاتورة'],
                    row['المورد'] or 'مورد نقدي',
                    format_date(row['التاريخ']),
                    row['المنتج'],
                    f"{row['الكمية']:.2f}",
                    f"{row['السعر']:.2f}",
                    f"{amount:.2f}",
                    row['المستخدم']
                ])

            # إضافة صف الإجمالي
            display_data.append([
                "الإجمالي", "", "", "", "", "", f"{total_amount:.2f}", ""
            ])

            columns = ['رقم الفاتورة', 'المورد', 'التاريخ', 'المنتج', 'الكمية', 'السعر', 'المجموع', 'المستخدم']
            self.show_report_data("تقرير المشتريات التفصيلي", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def purchases_summary_report(self):
        """تقرير المشتريات الإجمالي"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT DATE(pi.invoice_date) as 'التاريخ',
                       COUNT(pi.id) as 'عدد الفواتير',
                       SUM(pi.subtotal) as 'المجموع الفرعي',
                       SUM(pi.discount_amount) as 'الخصم',
                       SUM(pi.tax_amount) as 'الضريبة',
                       SUM(pi.total_amount) as 'المجموع الكلي',
                       SUM(pi.paid_amount) as 'المدفوع',
                       SUM(pi.remaining_amount) as 'المتبقي'
                FROM purchase_invoices pi
                WHERE DATE(pi.invoice_date) BETWEEN ? AND ?
                GROUP BY DATE(pi.invoice_date)
                ORDER BY DATE(pi.invoice_date) DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            totals = {
                'invoices': 0, 'subtotal': 0, 'discount': 0,
                'tax': 0, 'total': 0, 'paid': 0, 'remaining': 0
            }

            for row in data:
                totals['invoices'] += row['عدد الفواتير'] or 0
                totals['subtotal'] += row['المجموع الفرعي'] or 0
                totals['discount'] += row['الخصم'] or 0
                totals['tax'] += row['الضريبة'] or 0
                totals['total'] += row['المجموع الكلي'] or 0
                totals['paid'] += row['المدفوع'] or 0
                totals['remaining'] += row['المتبقي'] or 0

                display_data.append([
                    format_date(row['التاريخ']),
                    str(row['عدد الفواتير']),
                    f"{row['المجموع الفرعي']:.2f}",
                    f"{row['الخصم']:.2f}",
                    f"{row['الضريبة']:.2f}",
                    f"{row['المجموع الكلي']:.2f}",
                    f"{row['المدفوع']:.2f}",
                    f"{row['المتبقي']:.2f}"
                ])

            # إضافة صف الإجمالي
            display_data.append([
                "الإجمالي",
                str(totals['invoices']),
                f"{totals['subtotal']:.2f}",
                f"{totals['discount']:.2f}",
                f"{totals['tax']:.2f}",
                f"{totals['total']:.2f}",
                f"{totals['paid']:.2f}",
                f"{totals['remaining']:.2f}"
            ])

            columns = ['التاريخ', 'عدد الفواتير', 'المجموع الفرعي', 'الخصم', 'الضريبة', 'المجموع الكلي', 'المدفوع', 'المتبقي']
            self.show_report_data("تقرير المشتريات الإجمالي", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def suppliers_report(self):
        """تقرير الموردين"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT s.name as 'اسم المورد',
                       s.phone as 'الهاتف',
                       s.email as 'البريد الإلكتروني',
                       COUNT(pi.id) as 'عدد الفواتير',
                       SUM(pi.total_amount) as 'إجمالي المشتريات',
                       SUM(pi.paid_amount) as 'المدفوع',
                       SUM(pi.remaining_amount) as 'المتبقي',
                       s.current_balance as 'الرصيد الحالي'
                FROM suppliers s
                LEFT JOIN purchase_invoices pi ON s.id = pi.supplier_id
                    AND DATE(pi.invoice_date) BETWEEN ? AND ?
                WHERE s.is_active = 1
                GROUP BY s.id, s.name, s.phone, s.email, s.current_balance
                ORDER BY SUM(pi.total_amount) DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            total_purchases = 0
            total_paid = 0
            total_remaining = 0

            for row in data:
                purchases = row['إجمالي المشتريات'] or 0
                paid = row['المدفوع'] or 0
                remaining = row['المتبقي'] or 0

                total_purchases += purchases
                total_paid += paid
                total_remaining += remaining

                display_data.append([
                    row['اسم المورد'],
                    row['الهاتف'] or '',
                    row['البريد الإلكتروني'] or '',
                    str(row['عدد الفواتير'] or 0),
                    f"{purchases:.2f}",
                    f"{paid:.2f}",
                    f"{remaining:.2f}",
                    f"{row['الرصيد الحالي']:.2f}"
                ])

            # إضافة صف الإجمالي
            display_data.append([
                "الإجمالي", "", "", "",
                f"{total_purchases:.2f}",
                f"{total_paid:.2f}",
                f"{total_remaining:.2f}",
                ""
            ])

            columns = ['اسم المورد', 'الهاتف', 'البريد الإلكتروني', 'عدد الفواتير', 'إجمالي المشتريات', 'المدفوع', 'المتبقي', 'الرصيد الحالي']
            self.show_report_data("تقرير الموردين", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def purchases_by_product_report(self):
        """تقرير المشتريات حسب المنتج"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT p.name as 'اسم المنتج',
                       c.name as 'الفئة',
                       p.unit as 'الوحدة',
                       SUM(pii.quantity) as 'إجمالي الكمية',
                       AVG(pii.unit_price) as 'متوسط السعر',
                       SUM(pii.total_amount) as 'إجمالي القيمة',
                       COUNT(DISTINCT pi.id) as 'عدد الفواتير',
                       COUNT(DISTINCT pi.supplier_id) as 'عدد الموردين'
                FROM purchase_invoice_items pii
                JOIN purchase_invoices pi ON pii.invoice_id = pi.id
                JOIN products p ON pii.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE DATE(pi.invoice_date) BETWEEN ? AND ?
                GROUP BY p.id, p.name, c.name, p.unit
                ORDER BY SUM(pii.total_amount) DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            total_quantity = 0
            total_value = 0

            for row in data:
                quantity = row['إجمالي الكمية'] or 0
                value = row['إجمالي القيمة'] or 0

                total_quantity += quantity
                total_value += value

                display_data.append([
                    row['اسم المنتج'],
                    row['الفئة'] or 'غير محدد',
                    row['الوحدة'],
                    f"{quantity:.2f}",
                    f"{row['متوسط السعر']:.2f}",
                    f"{value:.2f}",
                    str(row['عدد الفواتير']),
                    str(row['عدد الموردين'])
                ])

            # إضافة صف الإجمالي
            display_data.append([
                "الإجمالي", "", "",
                f"{total_quantity:.2f}", "",
                f"{total_value:.2f}", "", ""
            ])

            columns = ['اسم المنتج', 'الفئة', 'الوحدة', 'إجمالي الكمية', 'متوسط السعر', 'إجمالي القيمة', 'عدد الفواتير', 'عدد الموردين']
            self.show_report_data("تقرير المشتريات حسب المنتج", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def purchases_by_supplier_report(self):
        """تقرير المشتريات حسب المورد"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT s.name as 'اسم المورد',
                       s.phone as 'الهاتف',
                       COUNT(pi.id) as 'عدد الفواتير',
                       SUM(pi.subtotal) as 'المجموع الفرعي',
                       SUM(pi.discount_amount) as 'إجمالي الخصم',
                       SUM(pi.tax_amount) as 'إجمالي الضريبة',
                       SUM(pi.total_amount) as 'إجمالي المشتريات',
                       AVG(pi.total_amount) as 'متوسط الفاتورة',
                       MIN(pi.invoice_date) as 'أول فاتورة',
                       MAX(pi.invoice_date) as 'آخر فاتورة'
                FROM suppliers s
                JOIN purchase_invoices pi ON s.id = pi.supplier_id
                WHERE DATE(pi.invoice_date) BETWEEN ? AND ?
                GROUP BY s.id, s.name, s.phone
                ORDER BY SUM(pi.total_amount) DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            totals = {
                'invoices': 0, 'subtotal': 0, 'discount': 0,
                'tax': 0, 'total': 0
            }

            for row in data:
                totals['invoices'] += row['عدد الفواتير']
                totals['subtotal'] += row['المجموع الفرعي'] or 0
                totals['discount'] += row['إجمالي الخصم'] or 0
                totals['tax'] += row['إجمالي الضريبة'] or 0
                totals['total'] += row['إجمالي المشتريات'] or 0

                display_data.append([
                    row['اسم المورد'],
                    row['الهاتف'] or '',
                    str(row['عدد الفواتير']),
                    f"{row['المجموع الفرعي']:.2f}",
                    f"{row['إجمالي الخصم']:.2f}",
                    f"{row['إجمالي الضريبة']:.2f}",
                    f"{row['إجمالي المشتريات']:.2f}",
                    f"{row['متوسط الفاتورة']:.2f}",
                    format_date(row['أول فاتورة']),
                    format_date(row['آخر فاتورة'])
                ])

            # إضافة صف الإجمالي
            display_data.append([
                "الإجمالي", "",
                str(totals['invoices']),
                f"{totals['subtotal']:.2f}",
                f"{totals['discount']:.2f}",
                f"{totals['tax']:.2f}",
                f"{totals['total']:.2f}",
                "", "", ""
            ])

            columns = ['اسم المورد', 'الهاتف', 'عدد الفواتير', 'المجموع الفرعي', 'إجمالي الخصم', 'إجمالي الضريبة', 'إجمالي المشتريات', 'متوسط الفاتورة', 'أول فاتورة', 'آخر فاتورة']
            self.show_report_data("تقرير المشتريات حسب المورد", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def suppliers_debts_report(self):
        """تقرير المديونيات للموردين"""
        try:
            query = """
                SELECT s.name as 'اسم المورد',
                       s.phone as 'الهاتف',
                       s.email as 'البريد الإلكتروني',
                       s.address as 'العنوان',
                       SUM(pi.remaining_amount) as 'المبلغ المستحق',
                       COUNT(pi.id) as 'عدد الفواتير المستحقة',
                       MIN(pi.invoice_date) as 'أقدم فاتورة',
                       MAX(pi.invoice_date) as 'أحدث فاتورة',
                       s.current_balance as 'الرصيد الحالي'
                FROM suppliers s
                JOIN purchase_invoices pi ON s.id = pi.supplier_id
                WHERE pi.remaining_amount > 0 AND s.is_active = 1
                GROUP BY s.id, s.name, s.phone, s.email, s.address, s.current_balance
                ORDER BY SUM(pi.remaining_amount) DESC
            """

            data = self.db_manager.execute_query(query)

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد مديونيات للموردين")
                return

            # تحويل البيانات للعرض
            display_data = []
            total_debt = 0
            total_invoices = 0

            for row in data:
                debt = row['المبلغ المستحق'] or 0
                invoices = row['عدد الفواتير المستحقة'] or 0

                total_debt += debt
                total_invoices += invoices

                display_data.append([
                    row['اسم المورد'],
                    row['الهاتف'] or '',
                    row['البريد الإلكتروني'] or '',
                    row['العنوان'] or '',
                    f"{debt:.2f}",
                    str(invoices),
                    format_date(row['أقدم فاتورة']),
                    format_date(row['أحدث فاتورة']),
                    f"{row['الرصيد الحالي']:.2f}"
                ])

            # إضافة صف الإجمالي
            display_data.append([
                "الإجمالي", "", "", "",
                f"{total_debt:.2f}",
                str(total_invoices),
                "", "", ""
            ])

            columns = ['اسم المورد', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'المبلغ المستحق', 'عدد الفواتير المستحقة', 'أقدم فاتورة', 'أحدث فاتورة', 'الرصيد الحالي']
            self.show_report_data("تقرير المديونيات للموردين", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")
