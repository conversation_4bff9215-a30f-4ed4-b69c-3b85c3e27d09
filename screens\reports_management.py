# -*- coding: utf-8 -*-
"""
شاشة إدارة التقارير
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import format_currency, get_date_range, format_date
from utils.arabic_support import ArabicSupport

class ReportsManagement:
    """كلاس إدارة التقارير"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("التقارير")
        self.window.geometry("1000x600")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1000
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="التقارير",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار فلاتر التاريخ
        date_frame = tk.LabelFrame(self.window, text="فترة التقرير", 
                                  font=FONTS['heading'], bg=COLORS['background'])
        date_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول
        row1_frame = tk.Frame(date_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # من تاريخ
        tk.Label(row1_frame, text="من تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.from_date_var = tk.StringVar(value=get_current_date())
        from_date_entry = tk.Entry(row1_frame, textvariable=self.from_date_var, 
                                  font=FONTS['normal'], width=12, justify='right')
        from_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # إلى تاريخ
        tk.Label(row1_frame, text="إلى تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.to_date_var = tk.StringVar(value=get_current_date())
        to_date_entry = tk.Entry(row1_frame, textvariable=self.to_date_var, 
                                font=FONTS['normal'], width=12, justify='right')
        to_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني - فترات سريعة
        row2_frame = tk.Frame(date_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row2_frame, text="فترات سريعة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        quick_periods = [
            ('اليوم', 'today'),
            ('أمس', 'yesterday'),
            ('هذا الأسبوع', 'this_week'),
            ('الأسبوع الماضي', 'last_week'),
            ('هذا الشهر', 'this_month'),
            ('الشهر الماضي', 'last_month'),
            ('هذا العام', 'this_year')
        ]
        
        for text, period in quick_periods:
            tk.Button(row2_frame, text=text, font=FONTS['small'],
                     bg=COLORS['info'], fg='white', 
                     command=lambda p=period: self.set_quick_period(p)).pack(side=tk.RIGHT, padx=2)
        
        # إطار أنواع التقارير
        reports_frame = tk.LabelFrame(self.window, text="أنواع التقارير", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        reports_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء الأزرار في شبكة
        reports_grid = tk.Frame(reports_frame, bg=COLORS['background'])
        reports_grid.pack(expand=True, padx=20, pady=20)
        
        # تقارير المبيعات
        sales_frame = tk.LabelFrame(reports_grid, text="تقارير المبيعات", 
                                   font=FONTS['normal'], bg=COLORS['background'])
        sales_frame.grid(row=0, column=0, padx=10, pady=10, sticky='nsew')
        
        tk.Button(sales_frame, text="تقرير المبيعات التفصيلي", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=20, height=2,
                 command=self.sales_detailed_report).pack(pady=5)
        
        tk.Button(sales_frame, text="تقرير المبيعات الإجمالي", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=20, height=2,
                 command=self.sales_summary_report).pack(pady=5)
        
        tk.Button(sales_frame, text="تقرير العملاء", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=20, height=2,
                 command=self.customers_report).pack(pady=5)
        
        # تقارير المشتريات
        purchases_frame = tk.LabelFrame(reports_grid, text="تقارير المشتريات", 
                                       font=FONTS['normal'], bg=COLORS['background'])
        purchases_frame.grid(row=0, column=1, padx=10, pady=10, sticky='nsew')
        
        tk.Button(purchases_frame, text="تقرير المشتريات التفصيلي", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=20, height=2,
                 command=self.purchases_detailed_report).pack(pady=5)
        
        tk.Button(purchases_frame, text="تقرير المشتريات الإجمالي", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=20, height=2,
                 command=self.purchases_summary_report).pack(pady=5)
        
        tk.Button(purchases_frame, text="تقرير الموردين", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=20, height=2,
                 command=self.suppliers_report).pack(pady=5)
        
        # تقارير المخزون
        inventory_frame = tk.LabelFrame(reports_grid, text="تقارير المخزون", 
                                       font=FONTS['normal'], bg=COLORS['background'])
        inventory_frame.grid(row=1, column=0, padx=10, pady=10, sticky='nsew')
        
        tk.Button(inventory_frame, text="تقرير المخزون الحالي", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=20, height=2,
                 command=self.inventory_current_report).pack(pady=5)
        
        tk.Button(inventory_frame, text="تقرير حركات المخزون", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=20, height=2,
                 command=self.inventory_movements_report).pack(pady=5)
        
        tk.Button(inventory_frame, text="تقرير المخزون المنخفض", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=20, height=2,
                 command=self.low_stock_report).pack(pady=5)
        
        # التقارير المالية
        financial_frame = tk.LabelFrame(reports_grid, text="التقارير المالية", 
                                       font=FONTS['normal'], bg=COLORS['background'])
        financial_frame.grid(row=1, column=1, padx=10, pady=10, sticky='nsew')
        
        tk.Button(financial_frame, text="تقرير الأرباح والخسائر", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=20, height=2,
                 command=self.profit_loss_report).pack(pady=5)
        
        tk.Button(financial_frame, text="تقرير التدفق النقدي", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=20, height=2,
                 command=self.cash_flow_report).pack(pady=5)
        
        tk.Button(financial_frame, text="تقرير المديونيات", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', width=20, height=2,
                 command=self.debts_report).pack(pady=5)
        
        # تكوين الشبكة
        reports_grid.grid_columnconfigure(0, weight=1)
        reports_grid.grid_columnconfigure(1, weight=1)
        reports_grid.grid_rowconfigure(0, weight=1)
        reports_grid.grid_rowconfigure(1, weight=1)
        
    def set_quick_period(self, period):
        """تعيين فترة سريعة"""
        try:
            start_date, end_date = get_date_range(period)
            self.from_date_var.set(start_date.strftime('%Y-%m-%d'))
            self.to_date_var.set(end_date.strftime('%Y-%m-%d'))
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين الفترة:\n{str(e)}")
            
    def get_date_filter(self):
        """الحصول على فلتر التاريخ"""
        return self.from_date_var.get(), self.to_date_var.get()
        
    def show_report_data(self, title, data, columns):
        """عرض بيانات التقرير في نافذة منفصلة"""
        report_window = tk.Toplevel(self.window)
        report_window.title(title)
        report_window.geometry("800x600")
        report_window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(report_window)
        
        # العنوان
        title_label = tk.Label(report_window, text=title, font=FONTS['title'],
                              bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)
        
        # الفترة
        from_date, to_date = self.get_date_filter()
        period_label = tk.Label(report_window, text=f"من {from_date} إلى {to_date}",
                               font=FONTS['normal'], bg=COLORS['background'])
        period_label.pack(pady=5)
        
        # إطار الجدول
        table_frame = tk.Frame(report_window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # إضافة البيانات
        for row in data:
            tree.insert('', 'end', values=row)
        
        # ترتيب العناصر
        tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # أزرار
        buttons_frame = tk.Frame(report_window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)
        
        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', 
                 command=lambda: self.print_report(title, data, columns)).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تصدير Excel", font=FONTS['button'],
                 bg=COLORS['success'], fg='white',
                 command=lambda: self.export_to_excel(title, data, columns)).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white',
                 command=report_window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def print_report(self, title, data, columns):
        """طباعة التقرير"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة الطباعة قريباً")
        
    def export_to_excel(self, title, data, columns):
        """تصدير التقرير إلى Excel"""
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة التصدير قريباً")

    # تقارير المبيعات
    def sales_detailed_report(self):
        """تقرير المبيعات التفصيلي"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT si.invoice_number as 'رقم الفاتورة',
                       c.name as 'العميل',
                       si.invoice_date as 'التاريخ',
                       p.name as 'المنتج',
                       sii.quantity as 'الكمية',
                       sii.unit_price as 'السعر',
                       sii.total_amount as 'المجموع',
                       u.name as 'المستخدم'
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                LEFT JOIN sales_invoice_items sii ON si.id = sii.invoice_id
                LEFT JOIN products p ON sii.product_id = p.id
                LEFT JOIN users u ON si.user_id = u.id
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
                ORDER BY si.invoice_date DESC, si.invoice_number
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            for row in data:
                display_data.append([
                    row['رقم الفاتورة'],
                    row['العميل'] or 'عميل نقدي',
                    format_date(row['التاريخ']),
                    row['المنتج'],
                    f"{row['الكمية']:.2f}",
                    f"{row['السعر']:.2f}",
                    f"{row['المجموع']:.2f}",
                    row['المستخدم']
                ])

            columns = ['رقم الفاتورة', 'العميل', 'التاريخ', 'المنتج', 'الكمية', 'السعر', 'المجموع', 'المستخدم']
            self.show_report_data("تقرير المبيعات التفصيلي", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def sales_summary_report(self):
        """تقرير المبيعات الإجمالي"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT DATE(si.invoice_date) as 'التاريخ',
                       COUNT(si.id) as 'عدد الفواتير',
                       SUM(si.subtotal) as 'المجموع الفرعي',
                       SUM(si.discount_amount) as 'الخصم',
                       SUM(si.tax_amount) as 'الضريبة',
                       SUM(si.total_amount) as 'المجموع الكلي',
                       SUM(si.paid_amount) as 'المدفوع',
                       SUM(si.remaining_amount) as 'المتبقي'
                FROM sales_invoices si
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
                GROUP BY DATE(si.invoice_date)
                ORDER BY DATE(si.invoice_date) DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            for row in data:
                display_data.append([
                    format_date(row['التاريخ']),
                    str(row['عدد الفواتير']),
                    f"{row['المجموع الفرعي']:.2f}",
                    f"{row['الخصم']:.2f}",
                    f"{row['الضريبة']:.2f}",
                    f"{row['المجموع الكلي']:.2f}",
                    f"{row['المدفوع']:.2f}",
                    f"{row['المتبقي']:.2f}"
                ])

            columns = ['التاريخ', 'عدد الفواتير', 'المجموع الفرعي', 'الخصم', 'الضريبة', 'المجموع الكلي', 'المدفوع', 'المتبقي']
            self.show_report_data("تقرير المبيعات الإجمالي", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def customers_report(self):
        """تقرير العملاء"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT c.name as 'اسم العميل',
                       COUNT(si.id) as 'عدد الفواتير',
                       SUM(si.total_amount) as 'إجمالي المبيعات',
                       SUM(si.paid_amount) as 'المدفوع',
                       SUM(si.remaining_amount) as 'المتبقي',
                       c.credit_limit as 'حد الائتمان',
                       c.current_balance as 'الرصيد الحالي'
                FROM customers c
                LEFT JOIN sales_invoices si ON c.id = si.customer_id
                    AND DATE(si.invoice_date) BETWEEN ? AND ?
                WHERE c.is_active = 1
                GROUP BY c.id, c.name, c.credit_limit, c.current_balance
                ORDER BY SUM(si.total_amount) DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            for row in data:
                display_data.append([
                    row['اسم العميل'],
                    str(row['عدد الفواتير'] or 0),
                    f"{row['إجمالي المبيعات'] or 0:.2f}",
                    f"{row['المدفوع'] or 0:.2f}",
                    f"{row['المتبقي'] or 0:.2f}",
                    f"{row['حد الائتمان']:.2f}",
                    f"{row['الرصيد الحالي']:.2f}"
                ])

            columns = ['اسم العميل', 'عدد الفواتير', 'إجمالي المبيعات', 'المدفوع', 'المتبقي', 'حد الائتمان', 'الرصيد الحالي']
            self.show_report_data("تقرير العملاء", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    # تقارير المشتريات
    def purchases_detailed_report(self):
        """تقرير المشتريات التفصيلي"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT pi.invoice_number as 'رقم الفاتورة',
                       s.name as 'المورد',
                       pi.invoice_date as 'التاريخ',
                       p.name as 'المنتج',
                       pii.quantity as 'الكمية',
                       pii.unit_price as 'السعر',
                       pii.total_amount as 'المجموع',
                       u.name as 'المستخدم'
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                LEFT JOIN purchase_invoice_items pii ON pi.id = pii.invoice_id
                LEFT JOIN products p ON pii.product_id = p.id
                LEFT JOIN users u ON pi.user_id = u.id
                WHERE DATE(pi.invoice_date) BETWEEN ? AND ?
                ORDER BY pi.invoice_date DESC, pi.invoice_number
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            for row in data:
                display_data.append([
                    row['رقم الفاتورة'],
                    row['المورد'] or 'مورد نقدي',
                    format_date(row['التاريخ']),
                    row['المنتج'],
                    f"{row['الكمية']:.2f}",
                    f"{row['السعر']:.2f}",
                    f"{row['المجموع']:.2f}",
                    row['المستخدم']
                ])

            columns = ['رقم الفاتورة', 'المورد', 'التاريخ', 'المنتج', 'الكمية', 'السعر', 'المجموع', 'المستخدم']
            self.show_report_data("تقرير المشتريات التفصيلي", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def purchases_summary_report(self):
        """تقرير المشتريات الإجمالي"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT DATE(pi.invoice_date) as 'التاريخ',
                       COUNT(pi.id) as 'عدد الفواتير',
                       SUM(pi.subtotal) as 'المجموع الفرعي',
                       SUM(pi.discount_amount) as 'الخصم',
                       SUM(pi.tax_amount) as 'الضريبة',
                       SUM(pi.total_amount) as 'المجموع الكلي',
                       SUM(pi.paid_amount) as 'المدفوع',
                       SUM(pi.remaining_amount) as 'المتبقي'
                FROM purchase_invoices pi
                WHERE DATE(pi.invoice_date) BETWEEN ? AND ?
                GROUP BY DATE(pi.invoice_date)
                ORDER BY DATE(pi.invoice_date) DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            for row in data:
                display_data.append([
                    format_date(row['التاريخ']),
                    str(row['عدد الفواتير']),
                    f"{row['المجموع الفرعي']:.2f}",
                    f"{row['الخصم']:.2f}",
                    f"{row['الضريبة']:.2f}",
                    f"{row['المجموع الكلي']:.2f}",
                    f"{row['المدفوع']:.2f}",
                    f"{row['المتبقي']:.2f}"
                ])

            columns = ['التاريخ', 'عدد الفواتير', 'المجموع الفرعي', 'الخصم', 'الضريبة', 'المجموع الكلي', 'المدفوع', 'المتبقي']
            self.show_report_data("تقرير المشتريات الإجمالي", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def suppliers_report(self):
        """تقرير الموردين"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT s.name as 'اسم المورد',
                       COUNT(pi.id) as 'عدد الفواتير',
                       SUM(pi.total_amount) as 'إجمالي المشتريات',
                       SUM(pi.paid_amount) as 'المدفوع',
                       SUM(pi.remaining_amount) as 'المتبقي',
                       s.current_balance as 'الرصيد الحالي'
                FROM suppliers s
                LEFT JOIN purchase_invoices pi ON s.id = pi.supplier_id
                    AND DATE(pi.invoice_date) BETWEEN ? AND ?
                WHERE s.is_active = 1
                GROUP BY s.id, s.name, s.current_balance
                ORDER BY SUM(pi.total_amount) DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد بيانات في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            for row in data:
                display_data.append([
                    row['اسم المورد'],
                    str(row['عدد الفواتير'] or 0),
                    f"{row['إجمالي المشتريات'] or 0:.2f}",
                    f"{row['المدفوع'] or 0:.2f}",
                    f"{row['المتبقي'] or 0:.2f}",
                    f"{row['الرصيد الحالي']:.2f}"
                ])

            columns = ['اسم المورد', 'عدد الفواتير', 'إجمالي المشتريات', 'المدفوع', 'المتبقي', 'الرصيد الحالي']
            self.show_report_data("تقرير الموردين", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    # تقارير المخزون
    def inventory_current_report(self):
        """تقرير المخزون الحالي"""
        try:
            query = """
                SELECT p.name as 'اسم المنتج',
                       c.name as 'الفئة',
                       p.unit as 'الوحدة',
                       p.stock_quantity as 'الكمية الحالية',
                       p.min_stock_level as 'الحد الأدنى',
                       p.cost_price as 'سعر التكلفة',
                       p.selling_price as 'سعر البيع',
                       (p.stock_quantity * p.cost_price) as 'قيمة المخزون'
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = 1
                ORDER BY p.name
            """

            data = self.db_manager.execute_query(query)

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد منتجات")
                return

            # تحويل البيانات للعرض
            display_data = []
            total_value = 0
            for row in data:
                inventory_value = row['قيمة المخزون'] or 0
                total_value += inventory_value

                display_data.append([
                    row['اسم المنتج'],
                    row['الفئة'] or 'غير محدد',
                    row['الوحدة'],
                    f"{row['الكمية الحالية']:.2f}",
                    f"{row['الحد الأدنى']:.2f}",
                    f"{row['سعر التكلفة']:.2f}",
                    f"{row['سعر البيع']:.2f}",
                    f"{inventory_value:.2f}"
                ])

            # إضافة صف الإجمالي
            display_data.append([
                "الإجمالي", "", "", "", "", "", "", f"{total_value:.2f}"
            ])

            columns = ['اسم المنتج', 'الفئة', 'الوحدة', 'الكمية الحالية', 'الحد الأدنى', 'سعر التكلفة', 'سعر البيع', 'قيمة المخزون']
            self.show_report_data("تقرير المخزون الحالي", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def inventory_movements_report(self):
        """تقرير حركات المخزون"""
        try:
            from_date, to_date = self.get_date_filter()

            query = """
                SELECT p.name as 'المنتج',
                       im.movement_type as 'نوع الحركة',
                       im.quantity as 'الكمية',
                       im.reference_type as 'نوع المرجع',
                       im.movement_date as 'التاريخ',
                       u.name as 'المستخدم',
                       im.notes as 'ملاحظات'
                FROM inventory_movements im
                LEFT JOIN products p ON im.product_id = p.id
                LEFT JOIN users u ON im.user_id = u.id
                WHERE DATE(im.movement_date) BETWEEN ? AND ?
                ORDER BY im.movement_date DESC, im.created_at DESC
            """

            data = self.db_manager.execute_query(query, (from_date, to_date))

            if not data:
                messagebox.showinfo("تنبيه", "لا توجد حركات مخزون في الفترة المحددة")
                return

            # تحويل البيانات للعرض
            display_data = []
            for row in data:
                movement_type_map = {
                    'in': 'دخول',
                    'out': 'خروج',
                    'adjustment': 'تعديل'
                }

                reference_type_map = {
                    'sales': 'مبيعات',
                    'purchase': 'مشتريات',
                    'adjustment': 'تعديل'
                }

                display_data.append([
                    row['المنتج'],
                    movement_type_map.get(row['نوع الحركة'], row['نوع الحركة']),
                    f"{row['الكمية']:.2f}",
                    reference_type_map.get(row['نوع المرجع'], row['نوع المرجع'] or ''),
                    format_date(row['التاريخ']),
                    row['المستخدم'],
                    row['ملاحظات'] or ''
                ])

            columns = ['المنتج', 'نوع الحركة', 'الكمية', 'نوع المرجع', 'التاريخ', 'المستخدم', 'ملاحظات']
            self.show_report_data("تقرير حركات المخزون", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def low_stock_report(self):
        """تقرير المخزون المنخفض"""
        try:
            query = """
                SELECT p.name as 'اسم المنتج',
                       c.name as 'الفئة',
                       p.stock_quantity as 'الكمية الحالية',
                       p.min_stock_level as 'الحد الأدنى',
                       (p.min_stock_level - p.stock_quantity) as 'النقص',
                       p.cost_price as 'سعر التكلفة',
                       ((p.min_stock_level - p.stock_quantity) * p.cost_price) as 'قيمة النقص'
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = 1 AND p.stock_quantity <= p.min_stock_level
                ORDER BY (p.min_stock_level - p.stock_quantity) DESC
            """

            data = self.db_manager.execute_query(query)

            if not data:
                messagebox.showinfo("تنبيه", "جميع المنتجات فوق الحد الأدنى")
                return

            # تحويل البيانات للعرض
            display_data = []
            total_shortage_value = 0
            for row in data:
                shortage_value = row['قيمة النقص'] or 0
                total_shortage_value += shortage_value

                display_data.append([
                    row['اسم المنتج'],
                    row['الفئة'] or 'غير محدد',
                    f"{row['الكمية الحالية']:.2f}",
                    f"{row['الحد الأدنى']:.2f}",
                    f"{row['النقص']:.2f}",
                    f"{row['سعر التكلفة']:.2f}",
                    f"{shortage_value:.2f}"
                ])

            # إضافة صف الإجمالي
            display_data.append([
                "الإجمالي", "", "", "", "", "", f"{total_shortage_value:.2f}"
            ])

            columns = ['اسم المنتج', 'الفئة', 'الكمية الحالية', 'الحد الأدنى', 'النقص', 'سعر التكلفة', 'قيمة النقص']
            self.show_report_data("تقرير المخزون المنخفض", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    # التقارير المالية
    def profit_loss_report(self):
        """تقرير الأرباح والخسائر"""
        try:
            from_date, to_date = self.get_date_filter()

            # حساب المبيعات
            sales_query = """
                SELECT SUM(sii.quantity * (sii.unit_price - p.cost_price)) as profit
                FROM sales_invoice_items sii
                JOIN sales_invoices si ON sii.invoice_id = si.id
                JOIN products p ON sii.product_id = p.id
                WHERE DATE(si.invoice_date) BETWEEN ? AND ?
            """

            sales_data = self.db_manager.execute_query(sales_query, (from_date, to_date))
            total_profit = sales_data[0]['profit'] if sales_data and sales_data[0]['profit'] else 0

            # حساب إجمالي المبيعات
            sales_total_query = """
                SELECT SUM(total_amount) as total_sales
                FROM sales_invoices
                WHERE DATE(invoice_date) BETWEEN ? AND ?
            """

            sales_total_data = self.db_manager.execute_query(sales_total_query, (from_date, to_date))
            total_sales = sales_total_data[0]['total_sales'] if sales_total_data and sales_total_data[0]['total_sales'] else 0

            # حساب إجمالي المشتريات
            purchases_query = """
                SELECT SUM(total_amount) as total_purchases
                FROM purchase_invoices
                WHERE DATE(invoice_date) BETWEEN ? AND ?
            """

            purchases_data = self.db_manager.execute_query(purchases_query, (from_date, to_date))
            total_purchases = purchases_data[0]['total_purchases'] if purchases_data and purchases_data[0]['total_purchases'] else 0

            # إعداد البيانات للعرض
            display_data = [
                ["إجمالي المبيعات", f"{total_sales:.2f}"],
                ["إجمالي المشتريات", f"{total_purchases:.2f}"],
                ["إجمالي الربح", f"{total_profit:.2f}"],
                ["نسبة الربح", f"{(total_profit/total_sales*100) if total_sales > 0 else 0:.2f}%"]
            ]

            columns = ['البيان', 'المبلغ']
            self.show_report_data("تقرير الأرباح والخسائر", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")

    def cash_flow_report(self):
        """تقرير التدفق النقدي"""
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير التدفق النقدي قريباً")

    def debts_report(self):
        """تقرير المديونيات"""
        try:
            # مديونيات العملاء
            customers_query = """
                SELECT c.name as 'العميل',
                       SUM(si.remaining_amount) as 'المبلغ المستحق',
                       c.credit_limit as 'حد الائتمان',
                       c.phone as 'الهاتف'
                FROM customers c
                JOIN sales_invoices si ON c.id = si.customer_id
                WHERE si.remaining_amount > 0 AND c.is_active = 1
                GROUP BY c.id, c.name, c.credit_limit, c.phone
                ORDER BY SUM(si.remaining_amount) DESC
            """

            customers_data = self.db_manager.execute_query(customers_query)

            # مديونيات الموردين
            suppliers_query = """
                SELECT s.name as 'المورد',
                       SUM(pi.remaining_amount) as 'المبلغ المستحق',
                       s.phone as 'الهاتف'
                FROM suppliers s
                JOIN purchase_invoices pi ON s.id = pi.supplier_id
                WHERE pi.remaining_amount > 0 AND s.is_active = 1
                GROUP BY s.id, s.name, s.phone
                ORDER BY SUM(pi.remaining_amount) DESC
            """

            suppliers_data = self.db_manager.execute_query(suppliers_query)

            # دمج البيانات
            display_data = []

            # إضافة عنوان العملاء
            display_data.append(["=== مديونيات العملاء ===", "", "", ""])

            total_customers_debt = 0
            for row in customers_data:
                debt = row['المبلغ المستحق'] or 0
                total_customers_debt += debt
                display_data.append([
                    row['العميل'],
                    f"{debt:.2f}",
                    f"{row['حد الائتمان']:.2f}",
                    row['الهاتف'] or ''
                ])

            display_data.append(["إجمالي مديونيات العملاء", f"{total_customers_debt:.2f}", "", ""])
            display_data.append(["", "", "", ""])

            # إضافة عنوان الموردين
            display_data.append(["=== مديونيات الموردين ===", "", "", ""])

            total_suppliers_debt = 0
            for row in suppliers_data:
                debt = row['المبلغ المستحق'] or 0
                total_suppliers_debt += debt
                display_data.append([
                    row['المورد'],
                    f"{debt:.2f}",
                    "",
                    row['الهاتف'] or ''
                ])

            display_data.append(["إجمالي مديونيات الموردين", f"{total_suppliers_debt:.2f}", "", ""])

            columns = ['الاسم', 'المبلغ المستحق', 'حد الائتمان', 'الهاتف']
            self.show_report_data("تقرير المديونيات", display_data, columns)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")
