# -*- coding: utf-8 -*-
"""
شاشة استعادة النسخة الاحتياطية
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import zipfile
import json
from datetime import datetime
from config.settings import COLORS, FONTS
from utils.backup_manager import BackupManager
from utils.database_manager import DatabaseManager
from utils.helpers import check_user_permission, show_permission_error, log_user_activity, format_file_size
from utils.arabic_support import ArabicSupport

class RestoreBackup:
    """كلاس استعادة النسخة الاحتياطية"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        self.backup_manager = BackupManager(self.db_manager)
        self.selected_backup_path = None
        self.backup_info = {}
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'admin'):
            show_permission_error('استعادة النسخة الاحتياطية')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_available_backups()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("استعادة النسخة الاحتياطية")
        self.window.geometry("800x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 800
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="استعادة النسخة الاحتياطية",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # تحذير مهم
        warning_frame = tk.Frame(self.window, bg=COLORS['danger'], relief='raised', bd=2)
        warning_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        warning_label = tk.Label(
            warning_frame,
            text="⚠️ تحذير مهم: استعادة النسخة الاحتياطية ستستبدل جميع البيانات الحالية!\nسيتم إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة.",
            font=FONTS['normal'],
            bg=COLORS['danger'],
            fg='white',
            justify='center'
        )
        warning_label.pack(pady=10)
        
        # إطار اختيار النسخة الاحتياطية
        selection_frame = tk.LabelFrame(self.window, text="اختيار النسخة الاحتياطية", 
                                       font=FONTS['heading'], bg=COLORS['background'])
        selection_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # أزرار اختيار المصدر
        source_frame = tk.Frame(selection_frame, bg=COLORS['background'])
        source_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(source_frame, text="اختيار من ملف", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=20,
                 command=self.select_from_file).pack(side=tk.LEFT, padx=5)
        
        tk.Button(source_frame, text="اختيار من النسخ المحفوظة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=20,
                 command=self.select_from_saved).pack(side=tk.LEFT, padx=5)
        
        # إطار معلومات النسخة المختارة
        info_frame = tk.LabelFrame(self.window, text="معلومات النسخة الاحتياطية المختارة", 
                                  font=FONTS['heading'], bg=COLORS['background'])
        info_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء عناصر معلومات النسخة
        self.create_backup_info_widgets(info_frame)
        
        # إطار خيارات الاستعادة
        options_frame = tk.LabelFrame(self.window, text="خيارات الاستعادة", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        options_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # خيارات الاستعادة
        self.create_restore_options(options_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # زر الاستعادة
        self.restore_button = tk.Button(buttons_frame, text="استعادة النسخة الاحتياطية", 
                                       font=FONTS['button'], bg=COLORS['warning'], fg='white', 
                                       width=25, state='disabled', command=self.restore_backup)
        self.restore_button.pack(side=tk.RIGHT, padx=5)
        
        # زر الإلغاء
        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # زر معاينة النسخة
        self.preview_button = tk.Button(buttons_frame, text="معاينة النسخة", 
                                       font=FONTS['button'], bg=COLORS['info'], fg='white', 
                                       width=15, state='disabled', command=self.preview_backup)
        self.preview_button.pack(side=tk.LEFT, padx=5)
        
    def create_backup_info_widgets(self, parent):
        """إنشاء عناصر معلومات النسخة الاحتياطية"""
        info_container = tk.Frame(parent, bg=COLORS['background'])
        info_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إطار المعلومات الأساسية
        basic_info_frame = tk.Frame(info_container, bg=COLORS['background'])
        basic_info_frame.pack(fill='x', pady=(0, 10))
        
        # اسم الملف
        file_frame = tk.Frame(basic_info_frame, bg=COLORS['background'])
        file_frame.pack(fill='x', pady=2)
        
        tk.Label(file_frame, text="اسم الملف:", font=FONTS['normal'],
                bg=COLORS['background'], width=15, anchor='e').pack(side=tk.RIGHT, padx=5)
        
        self.file_name_label = tk.Label(file_frame, text="لم يتم اختيار ملف", 
                                       font=FONTS['normal'], bg=COLORS['background'], 
                                       fg=COLORS['text'], anchor='w')
        self.file_name_label.pack(side=tk.RIGHT, padx=5, fill='x', expand=True)
        
        # حجم الملف
        size_frame = tk.Frame(basic_info_frame, bg=COLORS['background'])
        size_frame.pack(fill='x', pady=2)
        
        tk.Label(size_frame, text="حجم الملف:", font=FONTS['normal'],
                bg=COLORS['background'], width=15, anchor='e').pack(side=tk.RIGHT, padx=5)
        
        self.file_size_label = tk.Label(size_frame, text="-", 
                                       font=FONTS['normal'], bg=COLORS['background'], 
                                       fg=COLORS['text'], anchor='w')
        self.file_size_label.pack(side=tk.RIGHT, padx=5, fill='x', expand=True)
        
        # تاريخ الإنشاء
        date_frame = tk.Frame(basic_info_frame, bg=COLORS['background'])
        date_frame.pack(fill='x', pady=2)
        
        tk.Label(date_frame, text="تاريخ الإنشاء:", font=FONTS['normal'],
                bg=COLORS['background'], width=15, anchor='e').pack(side=tk.RIGHT, padx=5)
        
        self.creation_date_label = tk.Label(date_frame, text="-", 
                                           font=FONTS['normal'], bg=COLORS['background'], 
                                           fg=COLORS['text'], anchor='w')
        self.creation_date_label.pack(side=tk.RIGHT, padx=5, fill='x', expand=True)
        
        # نوع النسخة الاحتياطية
        type_frame = tk.Frame(basic_info_frame, bg=COLORS['background'])
        type_frame.pack(fill='x', pady=2)
        
        tk.Label(type_frame, text="نوع النسخة:", font=FONTS['normal'],
                bg=COLORS['background'], width=15, anchor='e').pack(side=tk.RIGHT, padx=5)
        
        self.backup_type_label = tk.Label(type_frame, text="-", 
                                         font=FONTS['normal'], bg=COLORS['background'], 
                                         fg=COLORS['text'], anchor='w')
        self.backup_type_label.pack(side=tk.RIGHT, padx=5, fill='x', expand=True)
        
        # إطار المحتويات
        contents_frame = tk.LabelFrame(info_container, text="محتويات النسخة الاحتياطية", 
                                      font=FONTS['normal'], bg=COLORS['background'])
        contents_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        # قائمة المحتويات
        self.contents_text = tk.Text(contents_frame, font=FONTS['small'], 
                                    height=8, width=70, wrap=tk.WORD,
                                    bg='white', fg=COLORS['text'])
        
        contents_scrollbar = ttk.Scrollbar(contents_frame, orient='vertical', 
                                          command=self.contents_text.yview)
        self.contents_text.configure(yscrollcommand=contents_scrollbar.set)
        
        self.contents_text.pack(side=tk.LEFT, fill='both', expand=True, padx=(10, 0), pady=10)
        contents_scrollbar.pack(side=tk.RIGHT, fill='y', padx=(0, 10), pady=10)
        
    def create_restore_options(self, parent):
        """إنشاء خيارات الاستعادة"""
        options_container = tk.Frame(parent, bg=COLORS['background'])
        options_container.pack(fill='x', padx=10, pady=10)
        
        # خيار إنشاء نسخة احتياطية قبل الاستعادة
        self.create_backup_before_var = tk.BooleanVar(value=True)
        tk.Checkbutton(options_container, text="إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة (مستحسن)",
                      variable=self.create_backup_before_var, font=FONTS['normal'],
                      bg=COLORS['background']).pack(anchor='e', pady=2)
        
        # خيار إعادة تشغيل البرنامج
        self.restart_program_var = tk.BooleanVar(value=True)
        tk.Checkbutton(options_container, text="إعادة تشغيل البرنامج تلقائياً بعد الاستعادة",
                      variable=self.restart_program_var, font=FONTS['normal'],
                      bg=COLORS['background']).pack(anchor='e', pady=2)
        
        # خيار التحقق من سلامة النسخة
        self.verify_backup_var = tk.BooleanVar(value=True)
        tk.Checkbutton(options_container, text="التحقق من سلامة النسخة الاحتياطية قبل الاستعادة",
                      variable=self.verify_backup_var, font=FONTS['normal'],
                      bg=COLORS['background']).pack(anchor='e', pady=2)
        
    def load_available_backups(self):
        """تحميل النسخ الاحتياطية المتاحة"""
        try:
            self.available_backups = self.backup_manager.get_backup_list()
        except Exception as e:
            self.available_backups = []
            print(f"خطأ في تحميل النسخ الاحتياطية: {str(e)}")
            
    def select_from_file(self):
        """اختيار نسخة احتياطية من ملف"""
        file_path = filedialog.askopenfilename(
            title="اختيار النسخة الاحتياطية",
            filetypes=[
                ("ملفات النسخ الاحتياطي", "*.zip"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if file_path:
            self.selected_backup_path = file_path
            self.load_backup_info()
            
    def select_from_saved(self):
        """اختيار نسخة احتياطية من النسخ المحفوظة"""
        if not self.available_backups:
            messagebox.showinfo("معلومات", "لا توجد نسخ احتياطية محفوظة")
            return
            
        # إنشاء نافذة اختيار النسخة
        selection_window = tk.Toplevel(self.window)
        selection_window.title("اختيار النسخة الاحتياطية")
        selection_window.geometry("600x400")
        selection_window.configure(bg=COLORS['background'])
        selection_window.transient(self.window)
        selection_window.grab_set()
        
        # توسيط النافذة
        selection_window.update_idletasks()
        x = (selection_window.winfo_screenwidth() // 2) - (300)
        y = (selection_window.winfo_screenheight() // 2) - (200)
        selection_window.geometry(f'600x400+{x}+{y}')
        
        # العنوان
        tk.Label(selection_window, text="اختيار النسخة الاحتياطية", 
                font=FONTS['heading'], bg=COLORS['background']).pack(pady=10)
        
        # قائمة النسخ الاحتياطية
        list_frame = tk.Frame(selection_window, bg=COLORS['background'])
        list_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        columns = ('اسم الملف', 'التاريخ', 'النوع', 'الحجم')
        backup_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            backup_tree.heading(col, text=col)
            backup_tree.column(col, width=120, anchor='center')
        
        # إضافة النسخ الاحتياطية
        for backup in self.available_backups:
            backup_type_map = {
                'manual': 'يدوي',
                'automatic': 'تلقائي',
                'auto': 'تلقائي',
                'pre_restore': 'قبل الاستعادة'
            }
            backup_type = backup_type_map.get(backup['backup_type'], backup['backup_type'])
            date_str = backup['created_at'].strftime('%Y-%m-%d %H:%M')
            size_str = format_file_size(backup['size'])
            
            backup_tree.insert('', 'end', values=(
                backup['filename'],
                date_str,
                backup_type,
                size_str
            ))
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=backup_tree.yview)
        backup_tree.configure(yscrollcommand=scrollbar.set)
        
        backup_tree.pack(side=tk.LEFT, fill='both', expand=True)
        scrollbar.pack(side=tk.RIGHT, fill='y')
        
        # أزرار التحكم
        buttons_frame = tk.Frame(selection_window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        def select_backup():
            selection = backup_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية")
                return
                
            item = backup_tree.item(selection[0])
            filename = item['values'][0]
            
            # البحث عن النسخة الاحتياطية
            for backup in self.available_backups:
                if backup['filename'] == filename:
                    self.selected_backup_path = backup['filepath']
                    self.load_backup_info()
                    selection_window.destroy()
                    break
        
        tk.Button(buttons_frame, text="اختيار", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=select_backup).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=selection_window.destroy).pack(side=tk.RIGHT, padx=5)

    def load_backup_info(self):
        """تحميل معلومات النسخة الاحتياطية المختارة"""
        if not self.selected_backup_path or not os.path.exists(self.selected_backup_path):
            self.clear_backup_info()
            return

        try:
            # معلومات الملف الأساسية
            file_name = os.path.basename(self.selected_backup_path)
            file_size = os.path.getsize(self.selected_backup_path)
            file_time = datetime.fromtimestamp(os.path.getmtime(self.selected_backup_path))

            # تحديث المعلومات الأساسية
            self.file_name_label.config(text=file_name)
            self.file_size_label.config(text=format_file_size(file_size))
            self.creation_date_label.config(text=file_time.strftime('%Y-%m-%d %H:%M:%S'))

            # التحقق من صحة الملف
            if not zipfile.is_zipfile(self.selected_backup_path):
                self.show_error_info("الملف المختار ليس نسخة احتياطية صحيحة")
                return

            # قراءة محتويات النسخة الاحتياطية
            with zipfile.ZipFile(self.selected_backup_path, 'r') as backup_zip:
                file_list = backup_zip.namelist()

                # قراءة معلومات النسخة الاحتياطية
                self.backup_info = {}
                if 'backup_info.json' in file_list:
                    info_data = backup_zip.read('backup_info.json')
                    self.backup_info = json.loads(info_data.decode('utf-8'))

                # تحديد نوع النسخة الاحتياطية
                backup_type_map = {
                    'manual': 'يدوي',
                    'automatic': 'تلقائي',
                    'auto': 'تلقائي',
                    'pre_restore': 'قبل الاستعادة'
                }
                backup_type = backup_type_map.get(
                    self.backup_info.get('backup_type', 'unknown'),
                    'غير محدد'
                )
                self.backup_type_label.config(text=backup_type)

                # عرض محتويات النسخة الاحتياطية
                self.display_backup_contents(file_list)

            # تفعيل الأزرار
            self.restore_button.config(state='normal')
            self.preview_button.config(state='normal')

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "اختيار نسخة احتياطية للاستعادة",
                f"تم اختيار النسخة: {file_name}",
                "restore"
            )

        except Exception as e:
            self.show_error_info(f"خطأ في قراءة النسخة الاحتياطية: {str(e)}")

    def display_backup_contents(self, file_list):
        """عرض محتويات النسخة الاحتياطية"""
        self.contents_text.delete('1.0', tk.END)

        contents_info = "محتويات النسخة الاحتياطية:\n"
        contents_info += "=" * 40 + "\n\n"

        # معلومات النسخة الاحتياطية
        if self.backup_info:
            contents_info += "معلومات النسخة:\n"
            contents_info += f"• تاريخ الإنشاء: {self.backup_info.get('created_at', 'غير محدد')}\n"
            contents_info += f"• نوع النسخة: {self.backup_info.get('backup_type', 'غير محدد')}\n"
            contents_info += f"• حجم قاعدة البيانات: {format_file_size(self.backup_info.get('database_size', 0))}\n"
            contents_info += f"• إصدار البرنامج: {self.backup_info.get('version', 'غير محدد')}\n\n"

        # قائمة الملفات
        contents_info += "الملفات المضمنة:\n"
        for file_name in file_list:
            if file_name == 'database.db':
                contents_info += "✓ قاعدة البيانات الرئيسية\n"
            elif file_name == 'program_settings.json':
                contents_info += "✓ إعدادات البرنامج\n"
            elif file_name == 'backup_info.json':
                contents_info += "✓ معلومات النسخة الاحتياطية\n"
            else:
                contents_info += f"• {file_name}\n"

        # التحقق من اكتمال النسخة الاحتياطية
        contents_info += "\nحالة النسخة الاحتياطية:\n"
        if 'database.db' in file_list:
            contents_info += "✓ قاعدة البيانات موجودة\n"
        else:
            contents_info += "✗ قاعدة البيانات مفقودة\n"

        if 'backup_info.json' in file_list:
            contents_info += "✓ معلومات النسخة موجودة\n"
        else:
            contents_info += "⚠ معلومات النسخة مفقودة\n"

        self.contents_text.insert('1.0', contents_info)

    def clear_backup_info(self):
        """مسح معلومات النسخة الاحتياطية"""
        self.file_name_label.config(text="لم يتم اختيار ملف")
        self.file_size_label.config(text="-")
        self.creation_date_label.config(text="-")
        self.backup_type_label.config(text="-")
        self.contents_text.delete('1.0', tk.END)
        self.contents_text.insert('1.0', "لم يتم اختيار نسخة احتياطية")

        # تعطيل الأزرار
        self.restore_button.config(state='disabled')
        self.preview_button.config(state='disabled')

    def show_error_info(self, error_message):
        """عرض رسالة خطأ في معلومات النسخة الاحتياطية"""
        self.file_name_label.config(text="خطأ في الملف", fg=COLORS['danger'])
        self.file_size_label.config(text="-")
        self.creation_date_label.config(text="-")
        self.backup_type_label.config(text="غير صحيح", fg=COLORS['danger'])

        self.contents_text.delete('1.0', tk.END)
        self.contents_text.insert('1.0', f"خطأ: {error_message}")

        # تعطيل الأزرار
        self.restore_button.config(state='disabled')
        self.preview_button.config(state='disabled')

    def preview_backup(self):
        """معاينة النسخة الاحتياطية"""
        if not self.selected_backup_path:
            return

        try:
            # إنشاء نافذة المعاينة
            preview_window = tk.Toplevel(self.window)
            preview_window.title(f"معاينة النسخة الاحتياطية - {os.path.basename(self.selected_backup_path)}")
            preview_window.geometry("700x500")
            preview_window.configure(bg=COLORS['background'])
            preview_window.transient(self.window)

            # توسيط النافذة
            preview_window.update_idletasks()
            x = (preview_window.winfo_screenwidth() // 2) - (350)
            y = (preview_window.winfo_screenheight() // 2) - (250)
            preview_window.geometry(f'700x500+{x}+{y}')

            # العنوان
            tk.Label(preview_window, text="معاينة النسخة الاحتياطية",
                    font=FONTS['heading'], bg=COLORS['background']).pack(pady=10)

            # إطار المعلومات التفصيلية
            details_frame = tk.LabelFrame(preview_window, text="معلومات تفصيلية",
                                         font=FONTS['normal'], bg=COLORS['background'])
            details_frame.pack(fill='both', expand=True, padx=20, pady=10)

            # نص المعلومات التفصيلية
            details_text = tk.Text(details_frame, font=FONTS['small'],
                                  wrap=tk.WORD, bg='white', fg=COLORS['text'])

            details_scrollbar = ttk.Scrollbar(details_frame, orient='vertical',
                                             command=details_text.yview)
            details_text.configure(yscrollcommand=details_scrollbar.set)

            # تحميل المعلومات التفصيلية
            detailed_info = self.get_detailed_backup_info()
            details_text.insert('1.0', detailed_info)
            details_text.config(state='disabled')

            details_text.pack(side=tk.LEFT, fill='both', expand=True, padx=(10, 0), pady=10)
            details_scrollbar.pack(side=tk.RIGHT, fill='y', padx=(0, 10), pady=10)

            # زر الإغلاق
            tk.Button(preview_window, text="إغلاق", font=FONTS['button'],
                     bg=COLORS['danger'], fg='white', width=15,
                     command=preview_window.destroy).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في معاينة النسخة الاحتياطية:\n{str(e)}")

    def get_detailed_backup_info(self):
        """الحصول على معلومات تفصيلية عن النسخة الاحتياطية"""
        try:
            info = f"معلومات تفصيلية عن النسخة الاحتياطية\n"
            info += "=" * 50 + "\n\n"

            # معلومات الملف
            info += "معلومات الملف:\n"
            info += f"• المسار: {self.selected_backup_path}\n"
            info += f"• الحجم: {format_file_size(os.path.getsize(self.selected_backup_path))}\n"
            info += f"• تاريخ التعديل: {datetime.fromtimestamp(os.path.getmtime(self.selected_backup_path)).strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            # معلومات النسخة الاحتياطية
            if self.backup_info:
                info += "معلومات النسخة الاحتياطية:\n"
                for key, value in self.backup_info.items():
                    if key == 'created_at':
                        info += f"• تاريخ الإنشاء: {value}\n"
                    elif key == 'backup_type':
                        backup_type_map = {
                            'manual': 'يدوي',
                            'automatic': 'تلقائي',
                            'auto': 'تلقائي',
                            'pre_restore': 'قبل الاستعادة'
                        }
                        info += f"• نوع النسخة: {backup_type_map.get(value, value)}\n"
                    elif key == 'database_size':
                        info += f"• حجم قاعدة البيانات: {format_file_size(value)}\n"
                    elif key == 'version':
                        info += f"• إصدار البرنامج: {value}\n"
                    elif key == 'user_id':
                        info += f"• معرف المستخدم: {value}\n"
                info += "\n"

            # فحص محتويات النسخة الاحتياطية
            with zipfile.ZipFile(self.selected_backup_path, 'r') as backup_zip:
                info += "محتويات النسخة الاحتياطية:\n"
                for file_info in backup_zip.filelist:
                    info += f"• {file_info.filename}\n"
                    info += f"  - الحجم الأصلي: {format_file_size(file_info.file_size)}\n"
                    info += f"  - الحجم المضغوط: {format_file_size(file_info.compress_size)}\n"
                    info += f"  - نسبة الضغط: {((file_info.file_size - file_info.compress_size) / file_info.file_size * 100):.1f}%\n"
                    info += f"  - تاريخ التعديل: {datetime(*file_info.date_time).strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            return info

        except Exception as e:
            return f"خطأ في قراءة معلومات النسخة الاحتياطية:\n{str(e)}"

    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        if not self.selected_backup_path:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية أولاً")
            return

        # تأكيد الاستعادة
        result = messagebox.askyesno(
            "تأكيد الاستعادة",
            f"هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\n"
            f"الملف: {os.path.basename(self.selected_backup_path)}\n"
            f"التاريخ: {self.backup_info.get('created_at', 'غير محدد')}\n\n"
            f"⚠️ تحذير: سيتم استبدال جميع البيانات الحالية!\n"
            f"هذا الإجراء لا يمكن التراجع عنه."
        )

        if not result:
            return

        try:
            # إنشاء نافذة التقدم
            progress_window = self.create_progress_window()

            # تحديث التقدم
            self.update_progress(progress_window, "بدء عملية الاستعادة...", 10)

            # التحقق من سلامة النسخة الاحتياطية
            if self.verify_backup_var.get():
                self.update_progress(progress_window, "التحقق من سلامة النسخة الاحتياطية...", 20)
                if not self.verify_backup_integrity():
                    progress_window.destroy()
                    messagebox.showerror("خطأ", "النسخة الاحتياطية تالفة أو غير صحيحة")
                    return

            # إنشاء نسخة احتياطية من البيانات الحالية
            if self.create_backup_before_var.get():
                self.update_progress(progress_window, "إنشاء نسخة احتياطية من البيانات الحالية...", 30)
                success, backup_path = self.backup_manager.create_backup(
                    self.current_user['id'], 'pre_restore', show_dialog=False
                )
                if not success:
                    progress_window.destroy()
                    messagebox.showerror("خطأ", f"فشل في إنشاء نسخة احتياطية من البيانات الحالية:\n{backup_path}")
                    return

            # استعادة النسخة الاحتياطية
            self.update_progress(progress_window, "استعادة النسخة الاحتياطية...", 50)
            success, result = self.backup_manager.restore_backup(
                self.current_user['id'], self.selected_backup_path
            )

            if success:
                self.update_progress(progress_window, "تم الانتهاء من الاستعادة بنجاح", 100)

                # تسجيل العملية
                log_user_activity(
                    self.db_manager,
                    self.current_user['id'],
                    "استعادة نسخة احتياطية",
                    f"تم استعادة النسخة: {os.path.basename(self.selected_backup_path)}",
                    "restore"
                )

                progress_window.destroy()

                # رسالة النجاح
                messagebox.showinfo("نجح", f"{result}\n\nتم الانتهاء من استعادة النسخة الاحتياطية بنجاح!")

                # إعادة تشغيل البرنامج إذا كان مطلوباً
                if self.restart_program_var.get():
                    messagebox.showinfo("إعادة التشغيل", "سيتم إعادة تشغيل البرنامج لتطبيق التغييرات")
                    self.window.destroy()
                    self.parent.quit()
                else:
                    self.window.destroy()

            else:
                progress_window.destroy()
                if result != "تم إلغاء العملية":
                    messagebox.showerror("خطأ", result)

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()
            messagebox.showerror("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية:\n{str(e)}")

    def verify_backup_integrity(self):
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            # التحقق من أن الملف هو ZIP صحيح
            if not zipfile.is_zipfile(self.selected_backup_path):
                return False

            # التحقق من محتويات النسخة الاحتياطية
            with zipfile.ZipFile(self.selected_backup_path, 'r') as backup_zip:
                # التحقق من وجود قاعدة البيانات
                if 'database.db' not in backup_zip.namelist():
                    return False

                # اختبار استخراج قاعدة البيانات
                backup_zip.testzip()

            return True

        except Exception as e:
            print(f"خطأ في التحقق من سلامة النسخة الاحتياطية: {str(e)}")
            return False

    def create_progress_window(self):
        """إنشاء نافذة التقدم"""
        progress_window = tk.Toplevel(self.window)
        progress_window.title("استعادة النسخة الاحتياطية")
        progress_window.geometry("400x150")
        progress_window.configure(bg=COLORS['background'])
        progress_window.transient(self.window)
        progress_window.grab_set()

        # توسيط النافذة
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (200)
        y = (progress_window.winfo_screenheight() // 2) - (75)
        progress_window.geometry(f'400x150+{x}+{y}')

        # العنوان
        tk.Label(progress_window, text="جاري استعادة النسخة الاحتياطية...",
                font=FONTS['heading'], bg=COLORS['background']).pack(pady=20)

        # شريط التقدم
        progress_window.progress_bar = ttk.Progressbar(progress_window, length=300, mode='determinate')
        progress_window.progress_bar.pack(pady=10)

        # تسمية الحالة
        progress_window.status_label = tk.Label(progress_window, text="بدء العملية...",
                                               font=FONTS['normal'], bg=COLORS['background'])
        progress_window.status_label.pack(pady=5)

        progress_window.update()
        return progress_window

    def update_progress(self, progress_window, status, percentage):
        """تحديث شريط التقدم"""
        try:
            progress_window.progress_bar['value'] = percentage
            progress_window.status_label.config(text=status)
            progress_window.update()
        except:
            pass
