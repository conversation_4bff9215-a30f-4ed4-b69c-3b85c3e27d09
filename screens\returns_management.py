# -*- coding: utf-8 -*-
"""
شاشة إدارة المرتجعات
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import (get_date_range, check_user_permission, show_permission_error, 
                          log_user_activity, format_currency)
from utils.arabic_support import ArabicSupport

class ReturnsManagement:
    """كلاس إدارة المرتجعات"""
    
    def __init__(self, parent, current_user, return_type='sales'):
        self.parent = parent
        self.current_user = current_user
        self.return_type = return_type  # 'sales' أو 'purchase'
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        permission_key = 'sales_view' if return_type == 'sales' else 'purchases_management'
        if not check_user_permission(current_user['role'], permission_key):
            show_permission_error(f'عرض مرتجعات {"المبيعات" if return_type == "sales" else "المشتريات"}')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_returns()
        
    def setup_window(self):
        """إعداد النافذة"""
        title = f"مرتجعات {'المبيعات' if self.return_type == 'sales' else 'المشتريات'}"
        self.window = tk.Toplevel(self.parent)
        self.window.title(title)
        self.window.geometry("1400x800")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1400
        height = 800
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_text = f"مرتجعات {'المبيعات' if self.return_type == 'sales' else 'المشتريات'}"
        title_label = tk.Label(
            self.window,
            text=title_text,
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الفلاتر
        filters_frame = tk.LabelFrame(self.window, text="فلاتر البحث", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        filters_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول - فلاتر التاريخ
        row1_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # من تاريخ
        tk.Label(row1_frame, text="من تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.from_date_var = tk.StringVar(value=get_current_date())
        from_date_entry = tk.Entry(row1_frame, textvariable=self.from_date_var, 
                                  font=FONTS['normal'], width=12, justify='right')
        from_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # إلى تاريخ
        tk.Label(row1_frame, text="إلى تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.to_date_var = tk.StringVar(value=get_current_date())
        to_date_entry = tk.Entry(row1_frame, textvariable=self.to_date_var, 
                                font=FONTS['normal'], width=12, justify='right')
        to_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني - فلاتر الحالة والبحث
        row2_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        # حالة المرتجع
        tk.Label(row2_frame, text="حالة المرتجع:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.status_var = tk.StringVar()
        status_combo = ttk.Combobox(row2_frame, textvariable=self.status_var,
                                   font=FONTS['normal'], width=15, state='readonly')
        status_combo['values'] = ['الكل', 'معلق', 'مقبول', 'مرفوض', 'مكتمل']
        status_combo.set('الكل')
        status_combo.pack(side=tk.RIGHT, padx=5)
        
        # البحث
        tk.Label(row2_frame, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(row2_frame, textvariable=self.search_var, 
                               font=FONTS['normal'], width=20)
        search_entry.pack(side=tk.RIGHT, padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # الصف الثالث - فترات سريعة وأزرار
        row3_frame = tk.Frame(filters_frame, bg=COLORS['background'])
        row3_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row3_frame, text="فترات سريعة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        quick_periods = [
            ('اليوم', 'today'),
            ('هذا الأسبوع', 'this_week'),
            ('هذا الشهر', 'this_month'),
            ('الشهر الماضي', 'last_month')
        ]
        
        for text, period in quick_periods:
            tk.Button(row3_frame, text=text, font=FONTS['small'],
                     bg=COLORS['info'], fg='white', 
                     command=lambda p=period: self.set_quick_period(p)).pack(side=tk.RIGHT, padx=2)
        
        # زر البحث
        tk.Button(row3_frame, text="بحث", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', 
                 command=self.load_returns).pack(side=tk.RIGHT, padx=10)
        
        # زر مسح الفلاتر
        tk.Button(row3_frame, text="مسح الفلاتر", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', 
                 command=self.clear_filters).pack(side=tk.RIGHT, padx=5)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(self.window, text="إحصائيات سريعة", 
                                   font=FONTS['heading'], bg=COLORS['background'])
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء عناصر الإحصائيات
        self.create_statistics_widgets(stats_frame)
        
        # إطار قائمة المرتجعات
        returns_frame = tk.LabelFrame(self.window, text="قائمة المرتجعات", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        returns_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء جدول المرتجعات
        self.create_returns_table(returns_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # أزرار الإجراءات
        create_permission = 'sales_create' if self.return_type == 'sales' else 'purchases_create'
        if check_user_permission(self.current_user['role'], create_permission):
            tk.Button(buttons_frame, text="مرتجع جديد", font=FONTS['button'],
                     bg=COLORS['success'], fg='white', width=15,
                     command=self.new_return).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="عرض المرتجع", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.view_return).pack(side=tk.LEFT, padx=5)
        
        edit_permission = 'sales_edit' if self.return_type == 'sales' else 'purchases_edit'
        if check_user_permission(self.current_user['role'], edit_permission):
            tk.Button(buttons_frame, text="تعديل الحالة", font=FONTS['button'],
                     bg=COLORS['warning'], fg='white', width=15,
                     command=self.edit_status).pack(side=tk.LEFT, padx=5)
        
        delete_permission = 'sales_delete' if self.return_type == 'sales' else 'purchases_delete'
        if check_user_permission(self.current_user['role'], delete_permission):
            tk.Button(buttons_frame, text="حذف", font=FONTS['button'],
                     bg=COLORS['danger'], fg='white', width=15,
                     command=self.delete_return).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', width=15,
                 command=self.print_return).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', width=15,
                 command=self.load_returns).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def create_statistics_widgets(self, parent):
        """إنشاء عناصر الإحصائيات"""
        stats_container = tk.Frame(parent, bg=COLORS['background'])
        stats_container.pack(fill='x', padx=10, pady=10)
        
        self.stats_labels = {}
        stats_info = [
            ('total_returns', 'إجمالي المرتجعات', COLORS['primary']),
            ('total_amount', 'إجمالي المبلغ', COLORS['success']),
            ('pending_returns', 'المرتجعات المعلقة', COLORS['warning']),
            ('completed_returns', 'المرتجعات المكتملة', COLORS['info'])
        ]
        
        for i, (key, label, color) in enumerate(stats_info):
            stat_frame = tk.Frame(stats_container, bg=COLORS['background'], 
                                 relief='raised', bd=1)
            stat_frame.pack(side=tk.RIGHT, padx=10, pady=5, fill='x', expand=True)
            
            tk.Label(stat_frame, text=label, font=FONTS['small'], 
                    bg=COLORS['background'], fg=COLORS['text']).pack(pady=(5, 0))
            
            self.stats_labels[key] = tk.Label(stat_frame, text="0", font=FONTS['heading'], 
                                            bg=COLORS['background'], fg=color)
            self.stats_labels[key].pack(pady=(0, 5))
            
    def create_returns_table(self, parent):
        """إنشاء جدول المرتجعات"""
        table_frame = tk.Frame(parent, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تحديد الأعمدة
        if self.return_type == 'sales':
            columns = ('رقم المرتجع', 'التاريخ', 'رقم الفاتورة', 'العميل', 'المنتج', 
                      'الكمية', 'السبب', 'المبلغ', 'الحالة', 'المستخدم')
        else:
            columns = ('رقم المرتجع', 'التاريخ', 'رقم الفاتورة', 'المورد', 'المنتج', 
                      'الكمية', 'السبب', 'المبلغ', 'الحالة', 'المستخدم')
        
        self.returns_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة وعرضها
        column_widths = {
            'رقم المرتجع': 120,
            'التاريخ': 100,
            'رقم الفاتورة': 120,
            'العميل': 150,
            'المورد': 150,
            'المنتج': 150,
            'الكمية': 80,
            'السبب': 120,
            'المبلغ': 100,
            'الحالة': 100,
            'المستخدم': 120
        }
        
        for col in columns:
            self.returns_tree.heading(col, text=col)
            self.returns_tree.column(col, width=column_widths.get(col, 100), anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.returns_tree.yview)
        self.returns_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.returns_tree.xview)
        self.returns_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.returns_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.returns_tree.bind('<Double-1>', self.on_return_double_click)

    def set_quick_period(self, period):
        """تعيين فترة سريعة"""
        try:
            start_date, end_date = get_date_range(period)
            self.from_date_var.set(start_date.strftime('%Y-%m-%d'))
            self.to_date_var.set(end_date.strftime('%Y-%m-%d'))
            self.load_returns()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين الفترة:\n{str(e)}")

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.from_date_var.set(get_current_date())
        self.to_date_var.set(get_current_date())
        self.status_var.set('الكل')
        self.search_var.set('')
        self.load_returns()

    def on_search_change(self, event=None):
        """معالج تغيير البحث"""
        # تأخير البحث لتجنب البحث مع كل حرف
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        self.search_timer = self.window.after(500, self.load_returns)

    def load_returns(self):
        """تحميل قائمة المرتجعات"""
        try:
            # مسح البيانات الحالية
            for item in self.returns_tree.get_children():
                self.returns_tree.delete(item)

            # بناء الاستعلام حسب نوع المرتجع
            if self.return_type == 'sales':
                query = """
                    SELECT r.id, r.return_number, r.return_date, r.invoice_number,
                           c.name as customer_name, p.name as product_name,
                           r.quantity, r.reason, r.amount, r.status,
                           u.name as user_name
                    FROM returns r
                    LEFT JOIN customers c ON r.customer_id = c.id
                    LEFT JOIN products p ON r.product_id = p.id
                    LEFT JOIN users u ON r.user_id = u.id
                    WHERE r.return_type = 'sales'
                """
            else:
                query = """
                    SELECT r.id, r.return_number, r.return_date, r.invoice_number,
                           s.name as supplier_name, p.name as product_name,
                           r.quantity, r.reason, r.amount, r.status,
                           u.name as user_name
                    FROM returns r
                    LEFT JOIN suppliers s ON r.supplier_id = s.id
                    LEFT JOIN products p ON r.product_id = p.id
                    LEFT JOIN users u ON r.user_id = u.id
                    WHERE r.return_type = 'purchase'
                """

            params = []

            # فلتر التاريخ
            from_date = self.from_date_var.get()
            to_date = self.to_date_var.get()
            if from_date and to_date:
                query += " AND DATE(r.return_date) BETWEEN ? AND ?"
                params.extend([from_date, to_date])

            # فلتر الحالة
            status_filter = self.status_var.get()
            if status_filter and status_filter != 'الكل':
                status_map = {
                    'معلق': 'pending',
                    'مقبول': 'approved',
                    'مرفوض': 'rejected',
                    'مكتمل': 'completed'
                }
                if status_filter in status_map:
                    query += " AND r.status = ?"
                    params.append(status_map[status_filter])

            # فلتر البحث
            search_term = self.search_var.get().strip()
            if search_term:
                if self.return_type == 'sales':
                    query += " AND (r.return_number LIKE ? OR r.invoice_number LIKE ? OR c.name LIKE ? OR p.name LIKE ?)"
                else:
                    query += " AND (r.return_number LIKE ? OR r.invoice_number LIKE ? OR s.name LIKE ? OR p.name LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern, search_pattern, search_pattern])

            query += " ORDER BY r.return_date DESC, r.id DESC"

            # تنفيذ الاستعلام
            returns = self.db_manager.execute_query(query, params)

            # عرض البيانات
            total_returns = 0
            total_amount = 0
            pending_returns = 0
            completed_returns = 0

            status_names = {
                'pending': 'معلق',
                'approved': 'مقبول',
                'rejected': 'مرفوض',
                'completed': 'مكتمل'
            }

            for return_item in returns:
                # تحديد لون الصف حسب الحالة
                tags = []
                if return_item['status'] == 'completed':
                    tags = ['completed']
                elif return_item['status'] == 'rejected':
                    tags = ['rejected']
                elif return_item['status'] == 'approved':
                    tags = ['approved']

                # تحديد اسم العميل/المورد
                if self.return_type == 'sales':
                    customer_supplier = return_item['customer_name'] or 'عميل نقدي'
                else:
                    customer_supplier = return_item['supplier_name'] or 'مورد نقدي'

                self.returns_tree.insert('', 'end', values=(
                    return_item['return_number'],
                    return_item['return_date'],
                    return_item['invoice_number'],
                    customer_supplier,
                    return_item['product_name'] or 'غير محدد',
                    f"{return_item['quantity']:.2f}",
                    return_item['reason'] or 'غير محدد',
                    f"{return_item['amount']:.2f}",
                    status_names.get(return_item['status'], return_item['status']),
                    return_item['user_name'] or ''
                ), tags=tags)

                # تحديث الإحصائيات
                total_returns += 1
                total_amount += return_item['amount']
                if return_item['status'] == 'pending':
                    pending_returns += 1
                elif return_item['status'] == 'completed':
                    completed_returns += 1

            # تكوين ألوان الصفوف
            self.returns_tree.tag_configure('completed', background='#d4edda')
            self.returns_tree.tag_configure('rejected', background='#f8d7da')
            self.returns_tree.tag_configure('approved', background='#d1ecf1')

            # تحديث الإحصائيات
            self.update_statistics(total_returns, total_amount, pending_returns, completed_returns)

            # تسجيل العملية
            return_type_ar = 'المبيعات' if self.return_type == 'sales' else 'المشتريات'
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                f"عرض مرتجعات {return_type_ar}",
                f"تم عرض {total_returns} مرتجع",
                "returns"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المرتجعات:\n{str(e)}")

    def update_statistics(self, total_returns, total_amount, pending_returns, completed_returns):
        """تحديث الإحصائيات"""
        self.stats_labels['total_returns'].config(text=str(total_returns))
        self.stats_labels['total_amount'].config(text=f"{total_amount:,.2f}")
        self.stats_labels['pending_returns'].config(text=str(pending_returns))
        self.stats_labels['completed_returns'].config(text=str(completed_returns))

    def get_selected_return_id(self):
        """الحصول على معرف المرتجع المحدد"""
        selection = self.returns_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مرتجع أولاً")
            return None

        item = self.returns_tree.item(selection[0])
        return_number = item['values'][0]

        # البحث عن معرف المرتجع
        query = "SELECT id FROM returns WHERE return_number = ?"
        result = self.db_manager.execute_query(query, [return_number])

        if result:
            return result[0]['id']
        return None

    def on_return_double_click(self, event):
        """معالج النقر المزدوج على المرتجع"""
        self.view_return()

    def new_return(self):
        """إنشاء مرتجع جديد"""
        NewReturnDialog(self.window, self.current_user, self.return_type, self.load_returns)

    def view_return(self):
        """عرض تفاصيل المرتجع"""
        return_id = self.get_selected_return_id()
        if return_id:
            # جلب بيانات المرتجع
            query = """
                SELECT r.*,
                       CASE
                           WHEN r.return_type = 'sales' THEN c.name
                           ELSE s.name
                       END as customer_supplier_name,
                       p.name as product_name, u.name as user_name
                FROM returns r
                LEFT JOIN customers c ON r.customer_id = c.id
                LEFT JOIN suppliers s ON r.supplier_id = s.id
                LEFT JOIN products p ON r.product_id = p.id
                LEFT JOIN users u ON r.user_id = u.id
                WHERE r.id = ?
            """
            result = self.db_manager.execute_query(query, [return_id])
            if result:
                return_data = result[0]
                self.show_return_details(return_data)

    def show_return_details(self, return_data):
        """عرض تفاصيل المرتجع في نافذة منفصلة"""
        details_window = tk.Toplevel(self.window)
        details_window.title(f"تفاصيل المرتجع - {return_data['return_number']}")
        details_window.geometry("600x500")
        details_window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(details_window)

        # العنوان
        title_label = tk.Label(details_window, text=f"تفاصيل المرتجع - {return_data['return_number']}",
                              font=FONTS['title'], bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)

        # معلومات المرتجع
        info_frame = tk.LabelFrame(details_window, text="معلومات المرتجع",
                                  font=FONTS['heading'], bg=COLORS['background'])
        info_frame.pack(fill='x', padx=20, pady=10)

        customer_supplier_label = "العميل" if self.return_type == 'sales' else "المورد"
        customer_supplier_name = return_data['customer_supplier_name'] or ('عميل نقدي' if self.return_type == 'sales' else 'مورد نقدي')

        status_names = {
            'pending': 'معلق',
            'approved': 'مقبول',
            'rejected': 'مرفوض',
            'completed': 'مكتمل'
        }

        info_text = f"""
رقم المرتجع: {return_data['return_number']}
التاريخ: {return_data['return_date']}
رقم الفاتورة: {return_data['invoice_number']}
{customer_supplier_label}: {customer_supplier_name}
المنتج: {return_data['product_name'] or 'غير محدد'}
الكمية: {return_data['quantity']:.2f}
السبب: {return_data['reason'] or 'غير محدد'}
المبلغ: {return_data['amount']:.2f}
الحالة: {status_names.get(return_data['status'], return_data['status'])}
المستخدم: {return_data['user_name'] or 'غير محدد'}
الملاحظات: {return_data['notes'] or 'لا توجد ملاحظات'}
        """

        tk.Label(info_frame, text=info_text, font=FONTS['normal'],
                bg=COLORS['background'], justify='right').pack(padx=10, pady=10)

        # زر الإغلاق
        tk.Button(details_window, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white',
                 command=details_window.destroy).pack(pady=10)

    def edit_status(self):
        """تعديل حالة المرتجع"""
        return_id = self.get_selected_return_id()
        if return_id:
            EditReturnStatusDialog(self.window, self.current_user, return_id, self.load_returns)

    def delete_return(self):
        """حذف المرتجع"""
        return_id = self.get_selected_return_id()
        if not return_id:
            return

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المرتجع؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                # حذف المرتجع
                self.db_manager.execute_query("DELETE FROM returns WHERE id = ?", [return_id])

                # تسجيل العملية
                return_type_ar = 'المبيعات' if self.return_type == 'sales' else 'المشتريات'
                log_user_activity(
                    self.db_manager,
                    self.current_user['id'],
                    f"حذف مرتجع {return_type_ar}",
                    f"تم حذف المرتجع رقم {return_id}",
                    "returns",
                    return_id
                )

                messagebox.showinfo("نجح", "تم حذف المرتجع بنجاح")
                self.load_returns()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف المرتجع:\n{str(e)}")

    def print_return(self):
        """طباعة المرتجع"""
        return_id = self.get_selected_return_id()
        if return_id:
            try:
                # جلب بيانات المرتجع للطباعة
                query = """
                    SELECT r.*,
                           CASE
                               WHEN r.return_type = 'sales' THEN c.name
                               ELSE s.name
                           END as customer_supplier_name,
                           CASE
                               WHEN r.return_type = 'sales' THEN c.phone
                               ELSE s.phone
                           END as customer_supplier_phone,
                           CASE
                               WHEN r.return_type = 'sales' THEN c.address
                               ELSE s.address
                           END as customer_supplier_address,
                           p.name as product_name, p.unit as product_unit,
                           u.name as user_name
                    FROM returns r
                    LEFT JOIN customers c ON r.customer_id = c.id
                    LEFT JOIN suppliers s ON r.supplier_id = s.id
                    LEFT JOIN products p ON r.product_id = p.id
                    LEFT JOIN users u ON r.user_id = u.id
                    WHERE r.id = ?
                """
                result = self.db_manager.execute_query(query, [return_id])

                if result:
                    return_data = result[0]
                    # إنشاء نافذة معاينة الطباعة
                    self.show_print_preview(return_data)

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في إعداد الطباعة:\n{str(e)}")

    def show_print_preview(self, return_data):
        """عرض معاينة الطباعة"""
        print_window = tk.Toplevel(self.window)
        print_window.title(f"معاينة طباعة المرتجع - {return_data['return_number']}")
        print_window.geometry("800x700")
        print_window.configure(bg='white')
        print_window.transient(self.window)

        # توسيط النافذة
        print_window.update_idletasks()
        x = (print_window.winfo_screenwidth() // 2) - (400)
        y = (print_window.winfo_screenheight() // 2) - (350)
        print_window.geometry(f'800x700+{x}+{y}')

        # إطار المحتوى
        content_frame = tk.Frame(print_window, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # إنشاء محتوى المرتجع
        return_content = self.generate_return_content(return_data)

        # منطقة النص
        text_widget = tk.Text(content_frame, font=FONTS['normal'], bg='white',
                             fg='black', wrap=tk.WORD, state='disabled')
        text_widget.pack(fill='both', expand=True)

        # إدراج المحتوى
        text_widget.config(state='normal')
        text_widget.insert('1.0', return_content)
        text_widget.config(state='disabled')

        # أزرار التحكم
        buttons_frame = tk.Frame(print_window, bg='white')
        buttons_frame.pack(fill='x', pady=10)

        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=lambda: self.execute_print(text_widget)).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=print_window.destroy).pack(side=tk.RIGHT, padx=5)

    def generate_return_content(self, return_data):
        """إنشاء محتوى المرتجع للطباعة"""
        return_type_ar = 'المبيعات' if return_data['return_type'] == 'sales' else 'المشتريات'
        customer_supplier_label = 'العميل' if return_data['return_type'] == 'sales' else 'المورد'
        customer_supplier_name = return_data['customer_supplier_name'] or ('عميل نقدي' if return_data['return_type'] == 'sales' else 'مورد نقدي')

        status_names = {
            'pending': 'معلق',
            'approved': 'مقبول',
            'rejected': 'مرفوض',
            'completed': 'مكتمل'
        }

        content = f"""
{'='*60}
                    مرتجع {return_type_ar}
{'='*60}

رقم المرتجع: {return_data['return_number']}
التاريخ: {return_data['return_date']}
المستخدم: {return_data['user_name']}

معلومات {customer_supplier_label}:
الاسم: {customer_supplier_name}
{f"الهاتف: {return_data['customer_supplier_phone']}" if return_data['customer_supplier_phone'] else ""}
{f"العنوان: {return_data['customer_supplier_address']}" if return_data['customer_supplier_address'] else ""}

{'='*60}
                    تفاصيل المرتجع
{'='*60}

رقم الفاتورة: {return_data['invoice_number']}
المنتج: {return_data['product_name'] or 'غير محدد'}
الكمية المرتجعة: {return_data['quantity']:.2f} {return_data['product_unit'] or 'وحدة'}
سبب المرتجع: {return_data['reason'] or 'غير محدد'}
المبلغ: {return_data['amount']:.2f} ريال
الحالة: {status_names.get(return_data['status'], return_data['status'])}

{f"ملاحظات: {return_data['notes']}" if return_data['notes'] else ""}

{'='*60}
شكراً لتعاملكم معنا
{'='*60}
"""

        return content

    def execute_print(self, text_widget):
        """تنفيذ عملية الطباعة"""
        try:
            import subprocess
            import tempfile

            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                content = text_widget.get('1.0', tk.END)
                f.write(content)
                temp_file = f.name

            # فتح الملف للطباعة (Windows)
            subprocess.run(['notepad', '/p', temp_file], check=True)

            messagebox.showinfo("نجح", "تم إرسال المرتجع للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في الطباعة:\n{str(e)}")

class NewReturnDialog:
    """حوار إنشاء مرتجع جديد"""

    def __init__(self, parent, current_user, return_type, callback=None):
        self.parent = parent
        self.current_user = current_user
        self.return_type = return_type
        self.callback = callback
        self.db_manager = DatabaseManager()

        self.setup_window()
        self.create_widgets()
        self.load_invoices()

    def setup_window(self):
        """إعداد النافذة"""
        title = f"مرتجع {'مبيعات' if self.return_type == 'sales' else 'مشتريات'} جديد"
        self.window = tk.Toplevel(self.parent)
        self.window.title(title)
        self.window.geometry("600x500")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 600
        height = 500
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_text = f"مرتجع {'مبيعات' if self.return_type == 'sales' else 'مشتريات'} جديد"
        title_label = tk.Label(self.window, text=title_text, font=FONTS['title'],
                              bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)

        # إطار البيانات
        data_frame = tk.LabelFrame(self.window, text="بيانات المرتجع",
                                  font=FONTS['heading'], bg=COLORS['background'])
        data_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # رقم المرتجع
        row1 = tk.Frame(data_frame, bg=COLORS['background'])
        row1.pack(fill='x', padx=10, pady=5)

        tk.Label(row1, text="رقم المرتجع:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.return_number_var = tk.StringVar()
        self.generate_return_number()
        tk.Entry(row1, textvariable=self.return_number_var, font=FONTS['normal'],
                width=20, state='readonly').pack(side=tk.RIGHT, padx=5)

        # التاريخ
        row2 = tk.Frame(data_frame, bg=COLORS['background'])
        row2.pack(fill='x', padx=10, pady=5)

        tk.Label(row2, text="التاريخ:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.return_date_var = tk.StringVar(value=get_current_date())
        tk.Entry(row2, textvariable=self.return_date_var, font=FONTS['normal'],
                width=20).pack(side=tk.RIGHT, padx=5)

        # رقم الفاتورة
        row3 = tk.Frame(data_frame, bg=COLORS['background'])
        row3.pack(fill='x', padx=10, pady=5)

        tk.Label(row3, text="رقم الفاتورة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.invoice_var = tk.StringVar()
        self.invoice_combo = ttk.Combobox(row3, textvariable=self.invoice_var,
                                         font=FONTS['normal'], width=25, state='readonly')
        self.invoice_combo.pack(side=tk.RIGHT, padx=5)
        self.invoice_combo.bind('<<ComboboxSelected>>', self.on_invoice_selected)

        # المنتج
        row4 = tk.Frame(data_frame, bg=COLORS['background'])
        row4.pack(fill='x', padx=10, pady=5)

        tk.Label(row4, text="المنتج:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(row4, textvariable=self.product_var,
                                         font=FONTS['normal'], width=25, state='readonly')
        self.product_combo.pack(side=tk.RIGHT, padx=5)

        # الكمية
        row5 = tk.Frame(data_frame, bg=COLORS['background'])
        row5.pack(fill='x', padx=10, pady=5)

        tk.Label(row5, text="الكمية:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.quantity_var = tk.StringVar(value='1')
        tk.Entry(row5, textvariable=self.quantity_var, font=FONTS['normal'],
                width=20).pack(side=tk.RIGHT, padx=5)

        # السبب
        row6 = tk.Frame(data_frame, bg=COLORS['background'])
        row6.pack(fill='x', padx=10, pady=5)

        tk.Label(row6, text="السبب:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.reason_var = tk.StringVar()
        reason_combo = ttk.Combobox(row6, textvariable=self.reason_var,
                                   font=FONTS['normal'], width=25)
        reason_combo['values'] = ['منتج معيب', 'خطأ في الطلب', 'تلف أثناء النقل', 'عدم مطابقة المواصفات', 'أخرى']
        reason_combo.pack(side=tk.RIGHT, padx=5)

        # المبلغ
        row7 = tk.Frame(data_frame, bg=COLORS['background'])
        row7.pack(fill='x', padx=10, pady=5)

        tk.Label(row7, text="المبلغ:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.amount_var = tk.StringVar()
        tk.Entry(row7, textvariable=self.amount_var, font=FONTS['normal'],
                width=20).pack(side=tk.RIGHT, padx=5)

        # الملاحظات
        row8 = tk.Frame(data_frame, bg=COLORS['background'])
        row8.pack(fill='x', padx=10, pady=5)

        tk.Label(row8, text="الملاحظات:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.notes_var = tk.StringVar()
        tk.Entry(row8, textvariable=self.notes_var, font=FONTS['normal'],
                width=40).pack(side=tk.RIGHT, padx=5)

        # أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.save_return).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def generate_return_number(self):
        """توليد رقم مرتجع جديد"""
        try:
            # جلب آخر رقم مرتجع
            query = "SELECT return_number FROM returns WHERE return_type = ? ORDER BY id DESC LIMIT 1"
            result = self.db_manager.execute_query(query, [self.return_type])

            if result:
                last_number = result[0]['return_number']
                # استخراج الرقم من النص
                if last_number.startswith('RET-'):
                    number_part = int(last_number.split('-')[1]) + 1
                else:
                    number_part = 1
            else:
                number_part = 1

            new_number = f"RET-{number_part:06d}"
            self.return_number_var.set(new_number)

        except Exception as e:
            self.return_number_var.set("RET-000001")

    def load_invoices(self):
        """تحميل قائمة الفواتير"""
        try:
            if self.return_type == 'sales':
                query = """
                    SELECT si.id, si.invoice_number, c.name as customer_name
                    FROM sales_invoices si
                    LEFT JOIN customers c ON si.customer_id = c.id
                    ORDER BY si.invoice_date DESC
                """
            else:
                query = """
                    SELECT pi.id, pi.invoice_number, s.name as supplier_name
                    FROM purchase_invoices pi
                    LEFT JOIN suppliers s ON pi.supplier_id = s.id
                    ORDER BY pi.invoice_date DESC
                """

            invoices = self.db_manager.execute_query(query)

            invoice_list = []
            for invoice in invoices:
                if self.return_type == 'sales':
                    customer_name = invoice['customer_name'] or 'عميل نقدي'
                    invoice_list.append(f"{invoice['invoice_number']} - {customer_name}")
                else:
                    supplier_name = invoice['supplier_name'] or 'مورد نقدي'
                    invoice_list.append(f"{invoice['invoice_number']} - {supplier_name}")

            self.invoice_combo['values'] = invoice_list

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفواتير:\n{str(e)}")

    def on_invoice_selected(self, event=None):
        """معالج اختيار الفاتورة"""
        if not self.invoice_var.get():
            return

        try:
            invoice_number = self.invoice_var.get().split(' - ')[0]

            # جلب عناصر الفاتورة
            if self.return_type == 'sales':
                query = """
                    SELECT sii.product_id, p.name as product_name, sii.quantity, sii.unit_price
                    FROM sales_invoice_items sii
                    JOIN products p ON sii.product_id = p.id
                    JOIN sales_invoices si ON sii.invoice_id = si.id
                    WHERE si.invoice_number = ?
                """
            else:
                query = """
                    SELECT pii.product_id, p.name as product_name, pii.quantity, pii.unit_price
                    FROM purchase_invoice_items pii
                    JOIN products p ON pii.product_id = p.id
                    JOIN purchase_invoices pi ON pii.invoice_id = pi.id
                    WHERE pi.invoice_number = ?
                """

            items = self.db_manager.execute_query(query, [invoice_number])

            product_list = []
            for item in items:
                product_list.append(f"{item['product_id']}: {item['product_name']} - الكمية: {item['quantity']:.2f} - السعر: {item['unit_price']:.2f}")

            self.product_combo['values'] = product_list

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في جلب عناصر الفاتورة:\n{str(e)}")

    def save_return(self):
        """حفظ المرتجع"""
        try:
            # التحقق من البيانات
            if not self.invoice_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار فاتورة")
                return

            if not self.product_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار منتج")
                return

            if not self.quantity_var.get() or float(self.quantity_var.get()) <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
                return

            if not self.reason_var.get():
                messagebox.showwarning("تحذير", "يرجى إدخال سبب المرتجع")
                return

            if not self.amount_var.get() or float(self.amount_var.get()) <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return

            # استخراج البيانات
            invoice_number = self.invoice_var.get().split(' - ')[0]
            product_id = int(self.product_var.get().split(':')[0])
            quantity = float(self.quantity_var.get())
            amount = float(self.amount_var.get())

            # جلب معرف العميل/المورد من الفاتورة
            if self.return_type == 'sales':
                query = "SELECT customer_id FROM sales_invoices WHERE invoice_number = ?"
                result = self.db_manager.execute_query(query, [invoice_number])
                customer_supplier_id = result[0]['customer_id'] if result else None
            else:
                query = "SELECT supplier_id FROM purchase_invoices WHERE invoice_number = ?"
                result = self.db_manager.execute_query(query, [invoice_number])
                customer_supplier_id = result[0]['supplier_id'] if result else None

            # إدراج المرتجع
            insert_query = """
                INSERT INTO returns (
                    return_number, return_date, return_type, invoice_number,
                    customer_id, supplier_id, product_id, quantity, reason,
                    amount, status, notes, user_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = [
                self.return_number_var.get(),
                self.return_date_var.get(),
                self.return_type,
                invoice_number,
                customer_supplier_id if self.return_type == 'sales' else None,
                customer_supplier_id if self.return_type == 'purchase' else None,
                product_id,
                quantity,
                self.reason_var.get(),
                amount,
                'pending',  # الحالة الافتراضية
                self.notes_var.get(),
                self.current_user['id'],
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ]

            self.db_manager.execute_query(insert_query, params)

            # تسجيل العملية
            return_type_ar = 'المبيعات' if self.return_type == 'sales' else 'المشتريات'
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                f"إنشاء مرتجع {return_type_ar}",
                f"رقم المرتجع: {self.return_number_var.get()}, المبلغ: {amount:.2f}",
                "returns"
            )

            messagebox.showinfo("نجح", "تم حفظ المرتجع بنجاح")

            if self.callback:
                self.callback()

            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ المرتجع:\n{str(e)}")

class EditReturnStatusDialog:
    """حوار تعديل حالة المرتجع"""

    def __init__(self, parent, current_user, return_id, callback=None):
        self.parent = parent
        self.current_user = current_user
        self.return_id = return_id
        self.callback = callback
        self.db_manager = DatabaseManager()

        self.setup_window()
        self.create_widgets()
        self.load_return_data()

    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تعديل حالة المرتجع")
        self.window.geometry("400x300")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 400
        height = 300
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = tk.Label(self.window, text="تعديل حالة المرتجع", font=FONTS['title'],
                              bg=COLORS['background'], fg=COLORS['primary'])
        title_label.pack(pady=10)

        # إطار البيانات
        data_frame = tk.LabelFrame(self.window, text="بيانات المرتجع",
                                  font=FONTS['heading'], bg=COLORS['background'])
        data_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # رقم المرتجع
        row1 = tk.Frame(data_frame, bg=COLORS['background'])
        row1.pack(fill='x', padx=10, pady=5)

        tk.Label(row1, text="رقم المرتجع:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.return_number_label = tk.Label(row1, text="", font=FONTS['normal'],
                                           bg=COLORS['background'], fg=COLORS['primary'])
        self.return_number_label.pack(side=tk.RIGHT, padx=5)

        # الحالة الحالية
        row2 = tk.Frame(data_frame, bg=COLORS['background'])
        row2.pack(fill='x', padx=10, pady=5)

        tk.Label(row2, text="الحالة الحالية:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.current_status_label = tk.Label(row2, text="", font=FONTS['normal'],
                                            bg=COLORS['background'], fg=COLORS['info'])
        self.current_status_label.pack(side=tk.RIGHT, padx=5)

        # الحالة الجديدة
        row3 = tk.Frame(data_frame, bg=COLORS['background'])
        row3.pack(fill='x', padx=10, pady=5)

        tk.Label(row3, text="الحالة الجديدة:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.new_status_var = tk.StringVar()
        status_combo = ttk.Combobox(row3, textvariable=self.new_status_var,
                                   font=FONTS['normal'], width=20, state='readonly')
        status_combo['values'] = ['معلق', 'مقبول', 'مرفوض', 'مكتمل']
        status_combo.pack(side=tk.RIGHT, padx=5)

        # ملاحظات
        row4 = tk.Frame(data_frame, bg=COLORS['background'])
        row4.pack(fill='x', padx=10, pady=5)

        tk.Label(row4, text="ملاحظات:", font=FONTS['normal'],
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

        self.notes_var = tk.StringVar()
        tk.Entry(row4, textvariable=self.notes_var, font=FONTS['normal'],
                width=30).pack(side=tk.RIGHT, padx=5)

        # أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)

        tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.save_status).pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def load_return_data(self):
        """تحميل بيانات المرتجع"""
        try:
            query = "SELECT return_number, status, notes FROM returns WHERE id = ?"
            result = self.db_manager.execute_query(query, [self.return_id])

            if result:
                return_data = result[0]
                self.return_number_label.config(text=return_data['return_number'])

                status_names = {
                    'pending': 'معلق',
                    'approved': 'مقبول',
                    'rejected': 'مرفوض',
                    'completed': 'مكتمل'
                }

                current_status = status_names.get(return_data['status'], return_data['status'])
                self.current_status_label.config(text=current_status)
                self.new_status_var.set(current_status)
                self.notes_var.set(return_data['notes'] or '')

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات المرتجع:\n{str(e)}")

    def save_status(self):
        """حفظ الحالة الجديدة"""
        try:
            if not self.new_status_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار الحالة الجديدة")
                return

            # تحويل الحالة إلى الإنجليزية
            status_map = {
                'معلق': 'pending',
                'مقبول': 'approved',
                'مرفوض': 'rejected',
                'مكتمل': 'completed'
            }

            new_status = status_map.get(self.new_status_var.get())

            # تحديث الحالة
            update_query = "UPDATE returns SET status = ?, notes = ? WHERE id = ?"
            self.db_manager.execute_query(update_query, [new_status, self.notes_var.get(), self.return_id])

            # تسجيل العملية
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "تعديل حالة مرتجع",
                f"المرتجع رقم {self.return_id} - الحالة الجديدة: {self.new_status_var.get()}",
                "returns",
                self.return_id
            )

            messagebox.showinfo("نجح", "تم تحديث حالة المرتجع بنجاح")

            if self.callback:
                self.callback()

            self.window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحديث الحالة:\n{str(e)}")
