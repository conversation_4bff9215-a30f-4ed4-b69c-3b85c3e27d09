# -*- coding: utf-8 -*-
"""
شاشة كشف حساب المورد
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from config.settings import COLORS, FONTS, get_current_date
from utils.database_manager import DatabaseManager
from utils.helpers import (get_date_range, check_user_permission, show_permission_error, 
                          log_user_activity, format_currency)
from utils.arabic_support import ArabicSupport

class SupplierStatement:
    """كلاس كشف حساب المورد"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'suppliers_management'):
            show_permission_error('عرض كشف حساب المورد')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_suppliers()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("كشف حساب المورد")
        self.window.geometry("1400x800")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1400
        height = 800
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="كشف حساب المورد",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار اختيار المورد والفترة
        selection_frame = tk.LabelFrame(self.window, text="اختيار المورد والفترة", 
                                       font=FONTS['heading'], bg=COLORS['background'])
        selection_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # الصف الأول - اختيار المورد
        row1_frame = tk.Frame(selection_frame, bg=COLORS['background'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row1_frame, text="المورد:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.supplier_var = tk.StringVar()
        self.supplier_combo = ttk.Combobox(row1_frame, textvariable=self.supplier_var,
                                          font=FONTS['normal'], width=30, state='readonly')
        self.supplier_combo.pack(side=tk.RIGHT, padx=5)
        self.supplier_combo.bind('<<ComboboxSelected>>', self.on_supplier_selected)
        
        # الصف الثاني - فترة التقرير
        row2_frame = tk.Frame(selection_frame, bg=COLORS['background'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        # من تاريخ
        tk.Label(row2_frame, text="من تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.from_date_var = tk.StringVar(value=get_current_date())
        from_date_entry = tk.Entry(row2_frame, textvariable=self.from_date_var, 
                                  font=FONTS['normal'], width=12, justify='right')
        from_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # إلى تاريخ
        tk.Label(row2_frame, text="إلى تاريخ:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        
        self.to_date_var = tk.StringVar(value=get_current_date())
        to_date_entry = tk.Entry(row2_frame, textvariable=self.to_date_var, 
                                font=FONTS['normal'], width=12, justify='right')
        to_date_entry.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثالث - فترات سريعة وأزرار
        row3_frame = tk.Frame(selection_frame, bg=COLORS['background'])
        row3_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(row3_frame, text="فترات سريعة:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        quick_periods = [
            ('هذا الشهر', 'this_month'),
            ('الشهر الماضي', 'last_month'),
            ('هذا العام', 'this_year'),
            ('آخر 3 شهور', 'last_3_months')
        ]
        
        for text, period in quick_periods:
            tk.Button(row3_frame, text=text, font=FONTS['small'],
                     bg=COLORS['info'], fg='white', 
                     command=lambda p=period: self.set_quick_period(p)).pack(side=tk.RIGHT, padx=2)
        
        # زر عرض الكشف
        tk.Button(row3_frame, text="عرض كشف الحساب", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', 
                 command=self.generate_statement).pack(side=tk.RIGHT, padx=10)
        
        # إطار معلومات المورد
        self.supplier_info_frame = tk.LabelFrame(self.window, text="معلومات المورد", 
                                               font=FONTS['heading'], bg=COLORS['background'])
        self.supplier_info_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء عناصر معلومات المورد
        self.create_supplier_info_widgets()
        
        # إطار ملخص الحساب
        summary_frame = tk.LabelFrame(self.window, text="ملخص الحساب", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        summary_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        # إنشاء عناصر ملخص الحساب
        self.create_summary_widgets(summary_frame)
        
        # إطار كشف الحساب
        statement_frame = tk.LabelFrame(self.window, text="كشف الحساب", 
                                       font=FONTS['heading'], bg=COLORS['background'])
        statement_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # إنشاء جدول كشف الحساب
        self.create_statement_table(statement_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.print_statement).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="تصدير Excel", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.export_statement).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def create_supplier_info_widgets(self):
        """إنشاء عناصر معلومات المورد"""
        info_container = tk.Frame(self.supplier_info_frame, bg=COLORS['background'])
        info_container.pack(fill='x', padx=10, pady=10)
        
        # الصف الأول
        row1 = tk.Frame(info_container, bg=COLORS['background'])
        row1.pack(fill='x', pady=2)
        
        tk.Label(row1, text="اسم المورد:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        self.supplier_name_label = tk.Label(row1, text="", font=FONTS['normal'], 
                                           bg=COLORS['background'], fg=COLORS['primary'])
        self.supplier_name_label.pack(side=tk.RIGHT, padx=5)
        
        tk.Label(row1, text="الهاتف:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        self.supplier_phone_label = tk.Label(row1, text="", font=FONTS['normal'], 
                                            bg=COLORS['background'], fg=COLORS['primary'])
        self.supplier_phone_label.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثاني
        row2 = tk.Frame(info_container, bg=COLORS['background'])
        row2.pack(fill='x', pady=2)
        
        tk.Label(row2, text="البريد الإلكتروني:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        self.supplier_email_label = tk.Label(row2, text="", font=FONTS['normal'], 
                                            bg=COLORS['background'], fg=COLORS['primary'])
        self.supplier_email_label.pack(side=tk.RIGHT, padx=5)
        
        tk.Label(row2, text="العنوان:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=(20, 5))
        self.supplier_address_label = tk.Label(row2, text="", font=FONTS['normal'], 
                                              bg=COLORS['background'], fg=COLORS['primary'])
        self.supplier_address_label.pack(side=tk.RIGHT, padx=5)
        
    def create_summary_widgets(self, parent):
        """إنشاء عناصر ملخص الحساب"""
        summary_container = tk.Frame(parent, bg=COLORS['background'])
        summary_container.pack(fill='x', padx=10, pady=10)
        
        self.summary_labels = {}
        summary_info = [
            ('opening_balance', 'الرصيد الافتتاحي', COLORS['secondary']),
            ('total_purchases', 'إجمالي المشتريات', COLORS['danger']),
            ('total_payments', 'إجمالي المدفوعات', COLORS['success']),
            ('closing_balance', 'الرصيد الختامي', COLORS['primary'])
        ]
        
        for i, (key, label, color) in enumerate(summary_info):
            summary_frame = tk.Frame(summary_container, bg=COLORS['background'], 
                                   relief='raised', bd=1)
            summary_frame.pack(side=tk.RIGHT, padx=10, pady=5, fill='x', expand=True)
            
            tk.Label(summary_frame, text=label, font=FONTS['small'], 
                    bg=COLORS['background'], fg=COLORS['text']).pack(pady=(5, 0))
            
            self.summary_labels[key] = tk.Label(summary_frame, text="0.00", font=FONTS['heading'], 
                                              bg=COLORS['background'], fg=color)
            self.summary_labels[key].pack(pady=(0, 5))
            
    def create_statement_table(self, parent):
        """إنشاء جدول كشف الحساب"""
        table_frame = tk.Frame(parent, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تحديد الأعمدة
        columns = ('التاريخ', 'البيان', 'رقم المرجع', 'مدين', 'دائن', 'الرصيد')
        self.statement_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد عناوين الأعمدة
        for col in columns:
            self.statement_tree.heading(col, text=col)
            if col in ['مدين', 'دائن', 'الرصيد']:
                self.statement_tree.column(col, width=120, anchor='center')
            elif col == 'التاريخ':
                self.statement_tree.column(col, width=100, anchor='center')
            elif col == 'رقم المرجع':
                self.statement_tree.column(col, width=100, anchor='center')
            else:
                self.statement_tree.column(col, width=200, anchor='center')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.statement_tree.yview)
        self.statement_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.statement_tree.xview)
        self.statement_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.statement_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            query = "SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name"
            suppliers = self.db_manager.execute_query(query)
            
            supplier_list = []
            for supplier in suppliers:
                supplier_list.append(f"{supplier['id']}: {supplier['name']}")
            
            self.supplier_combo['values'] = supplier_list
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الموردين:\n{str(e)}")
            
    def set_quick_period(self, period):
        """تعيين فترة سريعة"""
        try:
            if period == 'last_3_months':
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=90)
            else:
                start_date, end_date = get_date_range(period)
                
            self.from_date_var.set(start_date.strftime('%Y-%m-%d'))
            self.to_date_var.set(end_date.strftime('%Y-%m-%d'))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تعيين الفترة:\n{str(e)}")
            
    def on_supplier_selected(self, event=None):
        """معالج اختيار المورد"""
        if not self.supplier_var.get():
            return
            
        try:
            supplier_id = int(self.supplier_var.get().split(':')[0])
            
            # جلب معلومات المورد
            query = "SELECT * FROM suppliers WHERE id = ?"
            suppliers = self.db_manager.execute_query(query, (supplier_id,))
            
            if suppliers:
                supplier = suppliers[0]
                self.supplier_name_label.config(text=supplier['name'])
                self.supplier_phone_label.config(text=supplier['phone'] or 'غير محدد')
                self.supplier_email_label.config(text=supplier['email'] or 'غير محدد')
                self.supplier_address_label.config(text=supplier['address'] or 'غير محدد')
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في جلب معلومات المورد:\n{str(e)}")

    def generate_statement(self):
        """إنشاء كشف حساب المورد"""
        if not self.supplier_var.get():
            messagebox.showwarning("تحذير", "يرجى اختيار مورد أولاً")
            return

        try:
            supplier_id = int(self.supplier_var.get().split(':')[0])
            from_date = self.from_date_var.get()
            to_date = self.to_date_var.get()

            # مسح البيانات الحالية
            for item in self.statement_tree.get_children():
                self.statement_tree.delete(item)

            # حساب الرصيد الافتتاحي
            opening_balance = self.calculate_opening_balance(supplier_id, from_date)

            # جلب المعاملات في الفترة المحددة
            transactions = self.get_supplier_transactions(supplier_id, from_date, to_date)

            # عرض الرصيد الافتتاحي
            current_balance = opening_balance
            if opening_balance != 0:
                self.statement_tree.insert('', 'end', values=(
                    from_date, 'الرصيد الافتتاحي', '',
                    f"{opening_balance:.2f}" if opening_balance > 0 else '',
                    f"{abs(opening_balance):.2f}" if opening_balance < 0 else '',
                    f"{current_balance:.2f}"
                ))

            # عرض المعاملات
            total_purchases = 0
            total_payments = 0

            for transaction in transactions:
                if transaction['type'] == 'purchase':
                    # فاتورة شراء (مدين)
                    amount = transaction['amount']
                    current_balance += amount
                    total_purchases += amount

                    self.statement_tree.insert('', 'end', values=(
                        transaction['date'],
                        f"فاتورة شراء - {transaction['description']}",
                        transaction['reference'],
                        f"{amount:.2f}",
                        '',
                        f"{current_balance:.2f}"
                    ))

                elif transaction['type'] == 'payment':
                    # دفعة (دائن)
                    amount = transaction['amount']
                    current_balance -= amount
                    total_payments += amount

                    self.statement_tree.insert('', 'end', values=(
                        transaction['date'],
                        f"دفعة - {transaction['description']}",
                        transaction['reference'],
                        '',
                        f"{amount:.2f}",
                        f"{current_balance:.2f}"
                    ))

            # تحديث ملخص الحساب
            self.update_summary(opening_balance, total_purchases, total_payments, current_balance)

            # تسجيل العملية
            supplier_name = self.supplier_var.get().split(':')[1]
            log_user_activity(
                self.db_manager,
                self.current_user['id'],
                "عرض كشف حساب مورد",
                f"المورد: {supplier_name}, الفترة: من {from_date} إلى {to_date}",
                "supplier_statement",
                supplier_id
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء كشف الحساب:\n{str(e)}")

    def calculate_opening_balance(self, supplier_id, from_date):
        """حساب الرصيد الافتتاحي"""
        try:
            # حساب إجمالي المشتريات قبل تاريخ البداية
            purchases_query = """
                SELECT COALESCE(SUM(total_amount), 0) as total_purchases
                FROM purchase_invoices
                WHERE supplier_id = ? AND DATE(invoice_date) < ?
            """
            purchases_result = self.db_manager.execute_query(purchases_query, (supplier_id, from_date))
            total_purchases = purchases_result[0]['total_purchases'] if purchases_result else 0

            # حساب إجمالي المدفوعات قبل تاريخ البداية
            payments_query = """
                SELECT COALESCE(SUM(amount), 0) as total_payments
                FROM payments
                WHERE invoice_type = 'purchase'
                AND invoice_id IN (SELECT id FROM purchase_invoices WHERE supplier_id = ?)
                AND DATE(payment_date) < ?
            """
            payments_result = self.db_manager.execute_query(payments_query, (supplier_id, from_date))
            total_payments = payments_result[0]['total_payments'] if payments_result else 0

            return total_purchases - total_payments

        except Exception as e:
            print(f"خطأ في حساب الرصيد الافتتاحي: {str(e)}")
            return 0

    def get_supplier_transactions(self, supplier_id, from_date, to_date):
        """جلب معاملات المورد في الفترة المحددة"""
        transactions = []

        try:
            # جلب فواتير الشراء
            purchases_query = """
                SELECT invoice_date as date, total_amount as amount, invoice_number as reference,
                       'purchase' as type, 'فاتورة شراء' as description
                FROM purchase_invoices
                WHERE supplier_id = ? AND DATE(invoice_date) BETWEEN ? AND ?
                ORDER BY invoice_date, id
            """
            purchases = self.db_manager.execute_query(purchases_query, (supplier_id, from_date, to_date))

            for purchase in purchases:
                transactions.append(dict(purchase))

            # جلب المدفوعات
            payments_query = """
                SELECT p.payment_date as date, p.amount, p.reference_number as reference,
                       'payment' as type,
                       CASE
                           WHEN p.reference_number IS NOT NULL THEN p.reference_number
                           ELSE 'دفعة نقدية'
                       END as description
                FROM payments p
                JOIN purchase_invoices pi ON p.invoice_id = pi.id
                WHERE p.invoice_type = 'purchase' AND pi.supplier_id = ?
                AND DATE(p.payment_date) BETWEEN ? AND ?
                ORDER BY p.payment_date, p.id
            """
            payments = self.db_manager.execute_query(payments_query, (supplier_id, from_date, to_date))

            for payment in payments:
                transactions.append(dict(payment))

            # ترتيب المعاملات حسب التاريخ
            transactions.sort(key=lambda x: x['date'])

            return transactions

        except Exception as e:
            print(f"خطأ في جلب معاملات المورد: {str(e)}")
            return []

    def update_summary(self, opening_balance, total_purchases, total_payments, closing_balance):
        """تحديث ملخص الحساب"""
        self.summary_labels['opening_balance'].config(text=f"{opening_balance:.2f}")
        self.summary_labels['total_purchases'].config(text=f"{total_purchases:.2f}")
        self.summary_labels['total_payments'].config(text=f"{total_payments:.2f}")
        self.summary_labels['closing_balance'].config(text=f"{closing_balance:.2f}")

        # تغيير لون الرصيد الختامي حسب القيمة
        if closing_balance > 0:
            self.summary_labels['closing_balance'].config(fg=COLORS['danger'])  # أحمر للمديونية
        elif closing_balance < 0:
            self.summary_labels['closing_balance'].config(fg=COLORS['success'])  # أخضر للرصيد الدائن
        else:
            self.summary_labels['closing_balance'].config(fg=COLORS['primary'])  # أزرق للصفر

    def print_statement(self):
        """طباعة كشف الحساب"""
        if not self.supplier_var.get():
            messagebox.showwarning("تحذير", "يرجى اختيار مورد وعرض كشف الحساب أولاً")
            return

        messagebox.showinfo("قريباً", "سيتم تطوير ميزة الطباعة قريباً")

    def export_statement(self):
        """تصدير كشف الحساب"""
        if not self.supplier_var.get():
            messagebox.showwarning("تحذير", "يرجى اختيار مورد وعرض كشف الحساب أولاً")
            return

        messagebox.showinfo("قريباً", "سيتم تطوير ميزة التصدير قريباً")
