# -*- coding: utf-8 -*-
"""
شاشة إدارة الموردين
"""

import tkinter as tk
from tkinter import ttk, messagebox
from config.settings import COLORS, FONTS
from utils.database_manager import DatabaseManager
from utils.helpers import validate_email, validate_phone, format_currency
from utils.arabic_support import ArabicSupport

class SuppliersManagement:
    """كلاس إدارة الموردين"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        self.setup_window()
        self.create_widgets()
        self.load_suppliers()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة الموردين")
        self.window.geometry("1100x600")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1100
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة الموردين",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)
        
        # الأزرار
        tk.Button(buttons_frame, text="إضافة مورد جديد", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=self.add_supplier_dialog).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تعديل المورد", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', command=self.edit_supplier_dialog).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="حذف المورد", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=self.delete_supplier).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="كشف حساب", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', command=self.supplier_statement).pack(side=tk.RIGHT, padx=5)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['secondary'], fg='white', command=self.load_suppliers).pack(side=tk.RIGHT, padx=5)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg=COLORS['background'])
        search_frame.pack(pady=5)
        
        tk.Label(search_frame, text="البحث:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=FONTS['normal'], width=30, justify='right')
        search_entry.pack(side=tk.RIGHT, padx=5)
        
        # إطار جدول الموردين
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview للموردين
        columns = ('ID', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرصيد الحالي', 'الحالة')
        self.suppliers_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=18)
        
        # تحديد عناوين الأعمدة
        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            if col == 'ID':
                self.suppliers_tree.column(col, width=50, anchor='center')
            elif col == 'الرصيد الحالي':
                self.suppliers_tree.column(col, width=100, anchor='center')
            else:
                self.suppliers_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب العناصر
        self.suppliers_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج بالتعديل
        self.suppliers_tree.bind('<Double-1>', lambda e: self.edit_supplier_dialog())
        
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)
                
            # جلب الموردين من قاعدة البيانات
            query = """
                SELECT id, name, phone, email, address, current_balance, is_active, created_at
                FROM suppliers
                ORDER BY name
            """
            suppliers = self.db_manager.execute_query(query)
            
            # إضافة الموردين إلى الجدول
            for supplier in suppliers:
                status = "نشط" if supplier['is_active'] else "غير نشط"
                
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['id'],
                    supplier['name'],
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['address'] or '',
                    f"{supplier['current_balance']:.2f}",
                    status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الموردين:\n{str(e)}")
            
    def on_search_change(self, *args):
        """معالج تغيير البحث"""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            self.load_suppliers()
            return
            
        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)
                
            # البحث في الموردين
            query = """
                SELECT id, name, phone, email, address, current_balance, is_active, created_at
                FROM suppliers
                WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?
                ORDER BY name
            """
            search_pattern = f"%{search_term}%"
            suppliers = self.db_manager.execute_query(query, (search_pattern, search_pattern, search_pattern))
            
            # إضافة النتائج إلى الجدول
            for supplier in suppliers:
                status = "نشط" if supplier['is_active'] else "غير نشط"
                
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['id'],
                    supplier['name'],
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['address'] or '',
                    f"{supplier['current_balance']:.2f}",
                    status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في البحث:\n{str(e)}")
            
    def get_selected_supplier(self):
        """الحصول على المورد المحدد"""
        selection = self.suppliers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد من القائمة")
            return None
            
        item = self.suppliers_tree.item(selection[0])
        supplier_id = item['values'][0]
        
        # جلب بيانات المورد الكاملة
        query = "SELECT * FROM suppliers WHERE id = ?"
        suppliers = self.db_manager.execute_query(query, (supplier_id,))
        
        if suppliers:
            return dict(suppliers[0])
        return None
        
    def add_supplier_dialog(self):
        """حوار إضافة مورد جديد"""
        self.supplier_dialog(mode='add')
        
    def edit_supplier_dialog(self):
        """حوار تعديل مورد"""
        supplier = self.get_selected_supplier()
        if supplier:
            self.supplier_dialog(mode='edit', supplier_data=supplier)
            
    def supplier_statement(self):
        """عرض كشف حساب المورد"""
        supplier = self.get_selected_supplier()
        if supplier:
            messagebox.showinfo("قريباً", f"سيتم عرض كشف حساب المورد '{supplier['name']}' قريباً")
            
    def delete_supplier(self):
        """حذف مورد"""
        supplier = self.get_selected_supplier()
        if not supplier:
            return
            
        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", 
                                   f"هل أنت متأكد من حذف المورد '{supplier['name']}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه.")
        
        if result:
            try:
                query = "DELETE FROM suppliers WHERE id = ?"
                self.db_manager.execute_query(query, (supplier['id'],))
                messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
                self.load_suppliers()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف المورد:\n{str(e)}")

    def supplier_dialog(self, mode='add', supplier_data=None):
        """حوار إضافة/تعديل مورد"""
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة مورد جديد" if mode == 'add' else "تعديل المورد")
        dialog.geometry("500x550")
        dialog.configure(bg=COLORS['background'])
        dialog.resizable(False, False)
        ArabicSupport.setup_window_rtl(dialog)

        # توسيط الحوار
        dialog.transient(self.window)
        dialog.grab_set()

        # المتغيرات
        name_var = tk.StringVar(value=supplier_data['name'] if supplier_data else '')
        phone_var = tk.StringVar(value=supplier_data['phone'] if supplier_data else '')
        email_var = tk.StringVar(value=supplier_data['email'] if supplier_data else '')
        address_var = tk.StringVar(value=supplier_data['address'] if supplier_data else '')
        tax_number_var = tk.StringVar(value=supplier_data['tax_number'] if supplier_data else '')
        notes_var = tk.StringVar(value=supplier_data['notes'] if supplier_data else '')
        active_var = tk.BooleanVar(value=bool(supplier_data['is_active']) if supplier_data else True)

        # إطار المحتوى
        content_frame = tk.Frame(dialog, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم المورد
        tk.Label(content_frame, text="اسم المورد:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        name_entry = tk.Entry(content_frame, textvariable=name_var, font=FONTS['normal'],
                             width=35, justify='right')
        name_entry.pack(pady=(0, 10))
        name_entry.focus()

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        phone_entry = tk.Entry(content_frame, textvariable=phone_var, font=FONTS['normal'],
                              width=35, justify='right')
        phone_entry.pack(pady=(0, 10))

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        email_entry = tk.Entry(content_frame, textvariable=email_var, font=FONTS['normal'],
                              width=35, justify='right')
        email_entry.pack(pady=(0, 10))

        # العنوان
        tk.Label(content_frame, text="العنوان:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        address_entry = tk.Entry(content_frame, textvariable=address_var, font=FONTS['normal'],
                                width=35, justify='right')
        address_entry.pack(pady=(0, 10))

        # الرقم الضريبي
        tk.Label(content_frame, text="الرقم الضريبي:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        tax_entry = tk.Entry(content_frame, textvariable=tax_number_var, font=FONTS['normal'],
                            width=35, justify='right')
        tax_entry.pack(pady=(0, 10))

        # الملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        notes_entry = tk.Entry(content_frame, textvariable=notes_var, font=FONTS['normal'],
                              width=35, justify='right')
        notes_entry.pack(pady=(0, 15))

        # حالة المورد
        active_check = tk.Checkbutton(content_frame, text="المورد نشط", variable=active_var,
                                    font=FONTS['normal'], bg=COLORS['background'])
        active_check.pack(pady=10)

        # عرض الرصيد الحالي (للتعديل فقط)
        if mode == 'edit' and supplier_data:
            balance_frame = tk.LabelFrame(content_frame, text="معلومات الحساب",
                                        font=FONTS['normal'], bg=COLORS['background'])
            balance_frame.pack(fill='x', pady=(0, 15))

            tk.Label(balance_frame, text=f"الرصيد الحالي: {format_currency(supplier_data['current_balance'])}",
                    font=FONTS['normal'], bg=COLORS['background'], fg=COLORS['info']).pack(pady=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(content_frame, bg=COLORS['background'])
        buttons_frame.pack(pady=20)

        def save_supplier():
            # التحقق من البيانات
            if not name_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال اسم المورد")
                return

            # التحقق من البريد الإلكتروني
            if email_var.get().strip() and not validate_email(email_var.get().strip()):
                messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
                return

            # التحقق من رقم الهاتف
            if phone_var.get().strip() and not validate_phone(phone_var.get().strip()):
                messagebox.showerror("خطأ", "رقم الهاتف غير صحيح")
                return

            try:
                if mode == 'add':
                    # إضافة مورد جديد
                    query = """
                        INSERT INTO suppliers (name, phone, email, address, tax_number, notes, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        name_var.get().strip(),
                        phone_var.get().strip() or None,
                        email_var.get().strip() or None,
                        address_var.get().strip() or None,
                        tax_number_var.get().strip() or None,
                        notes_var.get().strip() or None,
                        1 if active_var.get() else 0
                    )
                else:
                    # تعديل مورد موجود
                    query = """
                        UPDATE suppliers
                        SET name=?, phone=?, email=?, address=?, tax_number=?,
                            notes=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    params = (
                        name_var.get().strip(),
                        phone_var.get().strip() or None,
                        email_var.get().strip() or None,
                        address_var.get().strip() or None,
                        tax_number_var.get().strip() or None,
                        notes_var.get().strip() or None,
                        1 if active_var.get() else 0,
                        supplier_data['id']
                    )

                self.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم حفظ المورد بنجاح")
                dialog.destroy()
                self.load_suppliers()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ المورد:\n{str(e)}")

        tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', command=save_supplier).pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
