# -*- coding: utf-8 -*-
"""
دليل المستخدم التفاعلي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from datetime import datetime
from config.settings import COLORS, FONTS, COMPANY_INFO
from utils.helpers import log_user_activity
from utils.arabic_support import ArabicSupport

class UserGuide:
    """كلاس دليل المستخدم التفاعلي"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        
        self.setup_window()
        self.create_widgets()
        self.load_guide_content()
        
        # تسجيل العملية
        try:
            from utils.database_manager import DatabaseManager
            db_manager = DatabaseManager()
            log_user_activity(
                db_manager,
                current_user['id'],
                "فتح دليل المستخدم",
                "تم فتح دليل المستخدم التفاعلي",
                "help"
            )
        except:
            pass
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("دليل المستخدم - برنامج محاسبة المبيعات والمخازن")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.window, bg=COLORS['primary'], height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="📖 دليل المستخدم - برنامج محاسبة المبيعات والمخازن",
            font=FONTS['title'],
            bg=COLORS['primary'],
            fg='white'
        )
        title_label.pack(expand=True)
        
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إطار التنقل الجانبي
        nav_frame = tk.Frame(main_frame, bg=COLORS['background'], width=250)
        nav_frame.pack(side=tk.RIGHT, fill='y', padx=(0, 10))
        nav_frame.pack_propagate(False)
        
        # إطار المحتوى
        content_frame = tk.Frame(main_frame, bg=COLORS['background'])
        content_frame.pack(side=tk.RIGHT, fill='both', expand=True)
        
        # إنشاء قائمة التنقل
        self.create_navigation(nav_frame)
        
        # إنشاء منطقة المحتوى
        self.create_content_area(content_frame)
        
        # إطار أزرار التحكم
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # أزرار التحكم
        tk.Button(buttons_frame, text="طباعة الدليل", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.print_guide).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="حفظ كـ PDF", font=FONTS['button'],
                 bg=COLORS['success'], fg='white', width=15,
                 command=self.save_as_pdf).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="البحث في الدليل", font=FONTS['button'],
                 bg=COLORS['warning'], fg='white', width=15,
                 command=self.search_guide).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def create_navigation(self, parent):
        """إنشاء قائمة التنقل"""
        nav_label = tk.Label(parent, text="📋 فهرس المحتويات", 
                            font=FONTS['heading'], bg=COLORS['background'])
        nav_label.pack(pady=(0, 10))
        
        # إطار قائمة التنقل
        nav_list_frame = tk.Frame(parent, bg=COLORS['background'])
        nav_list_frame.pack(fill='both', expand=True)
        
        # قائمة التنقل
        self.nav_tree = ttk.Treeview(nav_list_frame, show='tree')
        
        # شريط التمرير
        nav_scrollbar = ttk.Scrollbar(nav_list_frame, orient='vertical', command=self.nav_tree.yview)
        self.nav_tree.configure(yscrollcommand=nav_scrollbar.set)
        
        self.nav_tree.pack(side=tk.LEFT, fill='both', expand=True)
        nav_scrollbar.pack(side=tk.RIGHT, fill='y')
        
        # ربط الأحداث
        self.nav_tree.bind('<<TreeviewSelect>>', self.on_nav_select)
        
    def create_content_area(self, parent):
        """إنشاء منطقة المحتوى"""
        # عنوان المحتوى
        self.content_title = tk.Label(parent, text="مرحباً بك في دليل المستخدم", 
                                     font=FONTS['heading'], bg=COLORS['background'])
        self.content_title.pack(pady=(0, 10))
        
        # منطقة النص
        text_frame = tk.Frame(parent, bg=COLORS['background'])
        text_frame.pack(fill='both', expand=True)
        
        self.content_text = tk.Text(text_frame, font=FONTS['normal'], 
                                   wrap=tk.WORD, bg='white', fg=COLORS['text'],
                                   padx=15, pady=15, state='disabled')
        
        # شريط التمرير للنص
        text_scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.content_text.yview)
        self.content_text.configure(yscrollcommand=text_scrollbar.set)
        
        self.content_text.pack(side=tk.LEFT, fill='both', expand=True)
        text_scrollbar.pack(side=tk.RIGHT, fill='y')
        
    def load_guide_content(self):
        """تحميل محتوى الدليل"""
        # تحديد أقسام الدليل
        self.guide_sections = {
            'intro': {
                'title': '🏠 مقدمة عن البرنامج',
                'content': self.get_intro_content()
            },
            'getting_started': {
                'title': '🚀 البدء السريع',
                'content': self.get_getting_started_content()
            },
            'login': {
                'title': '🔐 تسجيل الدخول',
                'content': self.get_login_content()
            },
            'main_interface': {
                'title': '🖥️ الواجهة الرئيسية',
                'content': self.get_main_interface_content()
            },
            'products': {
                'title': '📦 إدارة المنتجات',
                'content': self.get_products_content()
            },
            'customers': {
                'title': '👥 إدارة العملاء',
                'content': self.get_customers_content()
            },
            'suppliers': {
                'title': '🏪 إدارة الموردين',
                'content': self.get_suppliers_content()
            },
            'sales': {
                'title': '💰 إدارة المبيعات',
                'content': self.get_sales_content()
            },
            'purchases': {
                'title': '🛒 إدارة المشتريات',
                'content': self.get_purchases_content()
            },
            'inventory': {
                'title': '📊 إدارة المخزون',
                'content': self.get_inventory_content()
            },
            'reports': {
                'title': '📈 التقارير',
                'content': self.get_reports_content()
            },
            'backup': {
                'title': '💾 النسخ الاحتياطي',
                'content': self.get_backup_content()
            },
            'users': {
                'title': '👤 إدارة المستخدمين',
                'content': self.get_users_content()
            },
            'settings': {
                'title': '⚙️ الإعدادات',
                'content': self.get_settings_content()
            },
            'troubleshooting': {
                'title': '🔧 حل المشاكل',
                'content': self.get_troubleshooting_content()
            },
            'shortcuts': {
                'title': '⌨️ اختصارات لوحة المفاتيح',
                'content': self.get_shortcuts_content()
            },
            'faq': {
                'title': '❓ الأسئلة الشائعة',
                'content': self.get_faq_content()
            },
            'contact': {
                'title': '📞 التواصل والدعم',
                'content': self.get_contact_content()
            }
        }
        
        # إضافة الأقسام إلى قائمة التنقل
        for section_id, section_data in self.guide_sections.items():
            self.nav_tree.insert('', 'end', iid=section_id, text=section_data['title'])
        
        # عرض المقدمة افتراضياً
        self.show_section('intro')
        
    def on_nav_select(self, event):
        """معالج اختيار قسم من قائمة التنقل"""
        selection = self.nav_tree.selection()
        if selection:
            section_id = selection[0]
            self.show_section(section_id)
            
    def show_section(self, section_id):
        """عرض قسم معين من الدليل"""
        if section_id in self.guide_sections:
            section = self.guide_sections[section_id]
            
            # تحديث العنوان
            self.content_title.config(text=section['title'])
            
            # تحديث المحتوى
            self.content_text.config(state='normal')
            self.content_text.delete('1.0', tk.END)
            self.content_text.insert('1.0', section['content'])
            self.content_text.config(state='disabled')
            
            # تحديد القسم في قائمة التنقل
            self.nav_tree.selection_set(section_id)
            
    def get_intro_content(self):
        """محتوى مقدمة البرنامج"""
        return f"""مرحباً بك في برنامج محاسبة المبيعات والمخزون

🎯 نظرة عامة:
برنامج محاسبة المبيعات والمخزون هو نظام متكامل لإدارة العمليات التجارية، يوفر حلولاً شاملة لإدارة المبيعات والمشتريات والمخزون والعملاء والموردين.

✨ الميزات الرئيسية:
• إدارة شاملة للمنتجات والفئات
• نظام متقدم لإدارة العملاء والموردين
• معالجة المبيعات والمشتريات بسهولة
• تتبع دقيق للمخزون والحركات
• تقارير مفصلة وإحصائيات شاملة
• نظام نسخ احتياطي متقدم
• إدارة المستخدمين والصلاحيات
• واجهة عربية سهلة الاستخدام

🎯 الهدف من البرنامج:
يهدف البرنامج إلى تبسيط وأتمتة العمليات التجارية، مما يوفر الوقت والجهد ويقلل من الأخطاء البشرية، ويساعد في اتخاذ قرارات مدروسة بناءً على البيانات الدقيقة.

👥 المستخدمون المستهدفون:
• أصحاب المتاجر والشركات الصغيرة والمتوسطة
• مديري المبيعات والمشتريات
• محاسبي المخازن
• مديري الأعمال

🔧 متطلبات النظام:
• نظام التشغيل: Windows 7 أو أحدث
• الذاكرة: 2 جيجابايت RAM أو أكثر
• مساحة التخزين: 100 ميجابايت أو أكثر
• دقة الشاشة: 1024x768 أو أعلى

📞 الدعم الفني:
في حالة وجود أي استفسارات أو مشاكل، يمكنك التواصل معنا من خلال قسم "التواصل والدعم" في هذا الدليل.

🚀 ابدأ الآن:
للبدء في استخدام البرنامج، انتقل إلى قسم "البدء السريع" لتعلم الخطوات الأساسية."""

    def get_getting_started_content(self):
        """محتوى البدء السريع"""
        return """🚀 البدء السريع

هذا القسم سيساعدك على البدء في استخدام البرنامج بسرعة وسهولة.

📋 الخطوات الأساسية:

1️⃣ تشغيل البرنامج:
• انقر نقراً مزدوجاً على أيقونة البرنامج
• أو شغل الملف main.py إذا كنت تستخدم الكود المصدري

2️⃣ تسجيل الدخول:
• استخدم بيانات المدير الافتراضية:
  - اسم المستخدم: admin
  - كلمة المرور: admin123
• أو استخدم حساب المستخدم العادي:
  - اسم المستخدم: user
  - كلمة المرور: user123

3️⃣ التعرف على الواجهة الرئيسية:
• شريط القوائم في الأعلى
• منطقة العمل الرئيسية في الوسط
• شريط الحالة في الأسفل

4️⃣ الإعداد الأولي:
• اذهب إلى قائمة "ملف" → "إعدادات البرنامج"
• أدخل معلومات الشركة
• حدد العملة المستخدمة
• اضبط إعدادات النسخ الاحتياطي

5️⃣ إضافة البيانات الأساسية:
• أضف فئات المنتجات من قائمة "المخزون"
• أضف المنتجات الأساسية
• أضف العملاء الرئيسيين
• أضف الموردين المهمين

6️⃣ إجراء أول عملية:
• جرب إضافة منتج جديد
• أو سجل عملية بيع بسيطة
• أو أضف عميل جديد

💡 نصائح للمبتدئين:
• ابدأ بإدخال بيانات قليلة للتجربة
• استخدم وظيفة البحث للعثور على البيانات بسرعة
• احفظ نسخة احتياطية بانتظام
• راجع التقارير لفهم أداء عملك

⚠️ تحذيرات مهمة:
• لا تشارك بيانات تسجيل الدخول مع أشخاص غير مخولين
• احرص على إنشاء نسخ احتياطية دورية
• تأكد من صحة البيانات قبل الحفظ

🎯 الخطوات التالية:
بعد إتمام الإعداد الأولي، يمكنك الانتقال إلى الأقسام المتخصصة في هذا الدليل لتعلم كيفية استخدام كل ميزة بالتفصيل."""

    def get_login_content(self):
        """محتوى تسجيل الدخول"""
        return """🔐 تسجيل الدخول

نظام تسجيل الدخول يضمن أمان البيانات ويتيح إدارة صلاحيات المستخدمين.

👤 أنواع المستخدمين:

1️⃣ المدير (Admin):
• صلاحيات كاملة على النظام
• يمكنه إدارة المستخدمين
• الوصول إلى جميع التقارير
• إدارة النسخ الاحتياطي
• تعديل إعدادات النظام

2️⃣ المستخدم العادي (User):
• صلاحيات محدودة
• يمكنه إدارة المبيعات والمشتريات
• عرض التقارير الأساسية
• لا يمكنه تعديل الإعدادات

🔑 بيانات الدخول الافتراضية:

المدير:
• اسم المستخدم: admin
• كلمة المرور: admin123

المستخدم العادي:
• اسم المستخدم: user
• كلمة المرور: user123

📝 خطوات تسجيل الدخول:

1. شغل البرنامج
2. أدخل اسم المستخدم في الحقل الأول
3. أدخل كلمة المرور في الحقل الثاني
4. اضغط "تسجيل الدخول" أو اضغط Enter

🔒 ميزات الأمان:

• تشفير كلمات المرور
• تسجيل عمليات تسجيل الدخول
• انتهاء الجلسة التلقائي
• حماية من محاولات الاختراق

⚠️ نصائح الأمان:

• غير كلمة المرور الافتراضية فوراً
• استخدم كلمة مرور قوية (8 أحرف على الأقل)
• لا تشارك بيانات الدخول مع الآخرين
• سجل الخروج عند الانتهاء من العمل

🔧 حل مشاكل تسجيل الدخول:

❌ "اسم المستخدم أو كلمة المرور غير صحيحة":
• تأكد من كتابة البيانات بشكل صحيح
• تحقق من حالة الأحرف (كبيرة/صغيرة)
• جرب البيانات الافتراضية

❌ "خطأ في الاتصال بقاعدة البيانات":
• تأكد من وجود ملف قاعدة البيانات
• تحقق من صلاحيات الملفات
• أعد تشغيل البرنامج

🆕 إنشاء مستخدم جديد:
يمكن للمدير إنشاء مستخدمين جدد من خلال:
قائمة "الإدارة" → "إدارة المستخدمين" → "إضافة مستخدم جديد"

🚪 تسجيل الخروج:
• من قائمة "ملف" → "تسجيل الخروج"
• أو أغلق البرنامج مباشرة"""

    def get_main_interface_content(self):
        """محتوى الواجهة الرئيسية"""
        return """🖥️ الواجهة الرئيسية

الواجهة الرئيسية هي نقطة الانطلاق لجميع عمليات البرنامج.

📋 مكونات الواجهة:

1️⃣ شريط القوائم (أعلى الشاشة):
• ملف: النسخ الاحتياطي، الإعدادات، الخروج
• المبيعات: إدارة المبيعات والفواتير
• المشتريات: إدارة المشتريات والموردين
• المخزون: إدارة المنتجات والمخزون
• العملاء: إدارة بيانات العملاء
• التقارير: جميع التقارير والإحصائيات
• الإدارة: إدارة المستخدمين والنظام (للمدير فقط)
• مساعدة: دليل المستخدم ومعلومات البرنامج

2️⃣ منطقة العمل الرئيسية:
• عرض النوافذ والشاشات المختلفة
• منطقة تفاعلية لإدخال وعرض البيانات

3️⃣ شريط الحالة (أسفل الشاشة):
• عرض معلومات المستخدم الحالي
• حالة الاتصال بقاعدة البيانات
• الوقت والتاريخ الحالي

🎨 ميزات الواجهة:

✨ تصميم عربي:
• دعم كامل للغة العربية
• اتجاه النص من اليمين إلى اليسار
• خطوط واضحة ومقروءة

🎯 سهولة الاستخدام:
• قوائم منظمة ومنطقية
• أيقونات واضحة ومفهومة
• ألوان متناسقة ومريحة للعين

⚡ الاستجابة السريعة:
• تحميل سريع للشاشات
• تحديث فوري للبيانات
• تفاعل سلس مع المستخدم

🔧 التخصيص:
• إمكانية تغيير حجم النوافذ
• ترتيب العناصر حسب الحاجة
• حفظ تفضيلات المستخدم

⌨️ اختصارات لوحة المفاتيح:
• Ctrl+N: إضافة جديد
• Ctrl+S: حفظ
• Ctrl+F: بحث
• F5: تحديث
• Escape: إلغاء/إغلاق

🖱️ استخدام الماوس:
• نقرة واحدة: تحديد
• نقرة مزدوجة: فتح/تحرير
• النقر بالزر الأيمن: قائمة سياقية

📱 تجربة المستخدم:
• واجهة بديهية لا تحتاج تدريب مكثف
• رسائل واضحة ومفيدة
• تأكيدات للعمليات المهمة
• معالجة أخطاء ذكية"""

    def get_products_content(self):
        """محتوى إدارة المنتجات"""
        return """📦 إدارة المنتجات

نظام إدارة المنتجات يتيح لك إضافة وتعديل وتتبع جميع منتجاتك بسهولة.

➕ إضافة منتج جديد:

1. اذهب إلى قائمة "المخزون" → "إدارة المنتجات"
2. اضغط "إضافة منتج جديد"
3. املأ البيانات المطلوبة:
   • اسم المنتج (مطلوب)
   • الباركود (اختياري)
   • الفئة (اختر من القائمة)
   • سعر الشراء
   • سعر البيع
   • الكمية الحالية
   • الحد الأدنى للمخزون
   • الوصف (اختياري)
4. اضغط "حفظ"

✏️ تعديل منتج موجود:

1. ابحث عن المنتج في القائمة
2. انقر نقراً مزدوجاً على المنتج
3. أو اضغط "تعديل" بعد تحديد المنتج
4. عدل البيانات المطلوبة
5. اضغط "حفظ التعديلات"

🗑️ حذف منتج:

1. حدد المنتج من القائمة
2. اضغط "حذف"
3. أكد عملية الحذف

⚠️ تحذير: لا يمكن حذف منتج له حركات مخزون

🔍 البحث عن المنتجات:

• البحث بالاسم
• البحث بالباركود
• البحث بالفئة
• فلترة حسب المخزون المنخفض

📊 معلومات المنتج:

🏷️ البيانات الأساسية:
• اسم المنتج
• كود المنتج (يُنشأ تلقائياً)
• الباركود
• الفئة

💰 بيانات الأسعار:
• سعر الشراء
• سعر البيع
• هامش الربح (يُحسب تلقائياً)

📦 بيانات المخزون:
• الكمية الحالية
• الحد الأدنى للمخزون
• تنبيهات المخزون المنخفض

📝 معلومات إضافية:
• الوصف
• ملاحظات
• تاريخ الإضافة
• آخر تحديث

🎯 نصائح مهمة:

✅ أفضل الممارسات:
• استخدم أسماء واضحة ومميزة للمنتجات
• حدد فئات منطقية للتنظيم
• احرص على دقة أسعار الشراء والبيع
• راجع المخزون بانتظام

⚠️ تحذيرات:
• تأكد من صحة الأسعار قبل الحفظ
• لا تحذف منتجات لها حركات مخزون
• احرص على تحديث الكميات عند الاستلام

🔧 حل المشاكل الشائعة:

❌ "لا يمكن حفظ المنتج":
• تأكد من ملء الحقول المطلوبة
• تحقق من صحة الأسعار (أرقام موجبة)

❌ "المنتج موجود مسبقاً":
• تحقق من اسم المنتج أو الباركود
• استخدم اسماً مختلفاً

❌ "خطأ في حساب هامش الربح":
• تأكد من إدخال سعر الشراء أولاً
• تحقق من أن سعر البيع أكبر من سعر الشراء"""

    def get_customers_content(self):
        """محتوى إدارة العملاء"""
        return """👥 إدارة العملاء

نظام إدارة العملاء يساعدك في تتبع وإدارة جميع عملائك وحساباتهم.

➕ إضافة عميل جديد:

1. اذهب إلى قائمة "العملاء" → "إدارة العملاء"
2. اضغط "إضافة عميل جديد"
3. املأ البيانات:
   • اسم العميل (مطلوب)
   • رقم الهاتف
   • العنوان
   • البريد الإلكتروني
   • نوع العميل (فرد/شركة)
   • حد الائتمان
   • ملاحظات
4. اضغط "حفظ"

✏️ تعديل بيانات عميل:

1. ابحث عن العميل في القائمة
2. انقر نقراً مزدوجاً على العميل
3. عدل البيانات المطلوبة
4. اضغط "حفظ التعديلات"

💰 إدارة حساب العميل:

📊 عرض كشف الحساب:
• الرصيد الحالي
• المبيعات الإجمالية
• المدفوعات
• المبلغ المستحق

💳 تسجيل دفعة:
1. حدد العميل
2. اضغط "تسجيل دفعة"
3. أدخل المبلغ المدفوع
4. اختر طريقة الدفع
5. أضف ملاحظات إذا لزم الأمر
6. اضغط "حفظ"

🔍 البحث عن العملاء:

• البحث بالاسم
• البحث برقم الهاتف
• فلترة حسب نوع العميل
• فلترة حسب الرصيد

📊 تقارير العملاء:

📈 تقرير أفضل العملاء:
• العملاء الأكثر شراءً
• أعلى المبالغ المشتراة
• تكرار الشراء

💰 تقرير أرصدة العملاء:
• العملاء المدينون
• العملاء الدائنون
• إجمالي المديونيات

🎯 نصائح مهمة:

✅ أفضل الممارسات:
• احرص على دقة بيانات الاتصال
• حدد حدود ائتمان مناسبة
• تابع المدفوعات بانتظام
• احتفظ بملاحظات مفيدة

⚠️ تحذيرات:
• لا تتجاوز حد الائتمان المحدد
• تابع العملاء المتأخرين في السداد
• تحقق من صحة أرقام الهواتف

🔧 حل المشاكل الشائعة:

❌ "لا يمكن حذف العميل":
• العميل له فواتير مسجلة
• احذف الفواتير أولاً أو اتركه غير نشط

❌ "خطأ في حساب الرصيد":
• تحقق من جميع الفواتير والدفعات
• راجع تواريخ العمليات

❌ "رقم الهاتف غير صحيح":
• تأكد من تنسيق رقم الهاتف
• استخدم الأرقام فقط مع رمز البلد"""

    def get_suppliers_content(self):
        """محتوى إدارة الموردين"""
        return """🏪 إدارة الموردين

نظام إدارة الموردين يساعدك في تتبع وإدارة جميع مورديك وحساباتهم.

➕ إضافة مورد جديد:

1. اذهب إلى قائمة "المشتريات" → "إدارة الموردين"
2. اضغط "إضافة مورد جديد"
3. املأ البيانات:
   • اسم المورد (مطلوب)
   • رقم الهاتف
   • العنوان
   • البريد الإلكتروني
   • نوع المورد
   • شروط الدفع
   • ملاحظات
4. اضغط "حفظ"

💰 إدارة حساب المورد:

📊 عرض كشف الحساب:
• الرصيد الحالي
• المشتريات الإجمالية
• المدفوعات للمورد
• المبلغ المستحق للمورد

💳 تسجيل دفعة للمورد:
1. حدد المورد
2. اضغط "تسجيل دفعة"
3. أدخل المبلغ المدفوع
4. اختر طريقة الدفع
5. اضغط "حفظ"

🎯 نصائح إدارة الموردين:
• احتفظ بعلاقات جيدة مع الموردين
• تابع شروط الدفع والتسليم
• قارن الأسعار بين الموردين
• احرص على التوثيق الجيد للعمليات"""

    def get_sales_content(self):
        """محتوى إدارة المبيعات"""
        return """💰 إدارة المبيعات

نظام إدارة المبيعات يتيح لك تسجيل ومتابعة جميع عمليات البيع.

🧾 إنشاء فاتورة بيع جديدة:

1. اذهب إلى قائمة "المبيعات" → "فاتورة بيع جديدة"
2. اختر العميل من القائمة
3. أضف المنتجات:
   • ابحث عن المنتج
   • حدد الكمية
   • تحقق من السعر
   • اضغط "إضافة"
4. راجع إجمالي الفاتورة
5. اختر طريقة الدفع
6. اضغط "حفظ الفاتورة"

📋 إدارة الفواتير:

🔍 البحث عن الفواتير:
• البحث برقم الفاتورة
• البحث باسم العميل
• فلترة حسب التاريخ
• فلترة حسب حالة الدفع

✏️ تعديل فاتورة:
• يمكن تعديل الفواتير غير المدفوعة فقط
• انقر نقراً مزدوجاً على الفاتورة
• عدل البيانات المطلوبة
• احفظ التعديلات

🖨️ طباعة الفاتورة:
• حدد الفاتورة
• اضغط "طباعة"
• أو اضغط Ctrl+P

💳 طرق الدفع:
• نقداً
• بطاقة ائتمان
• شيك
• تحويل بنكي
• آجل (دين على العميل)

📊 تقارير المبيعات:
• تقرير المبيعات اليومية
• تقرير المبيعات الشهرية
• تقرير أفضل المنتجات مبيعاً
• تقرير أداء المبيعات"""

    def get_purchases_content(self):
        """محتوى إدارة المشتريات"""
        return """🛒 إدارة المشتريات

نظام إدارة المشتريات يساعدك في تتبع وإدارة جميع عمليات الشراء.

🧾 إنشاء فاتورة شراء جديدة:

1. اذهب إلى قائمة "المشتريات" → "فاتورة شراء جديدة"
2. اختر المورد من القائمة
3. أضف المنتجات المشتراة:
   • اختر المنتج أو أضف منتج جديد
   • حدد الكمية المشتراة
   • أدخل سعر الشراء
   • اضغط "إضافة"
4. راجع إجمالي الفاتورة
5. اختر طريقة الدفع
6. اضغط "حفظ الفاتورة"

📦 استلام البضائع:

✅ تأكيد الاستلام:
• تحقق من الكميات المستلمة
• تأكد من جودة البضائع
• طابق مع فاتورة الشراء
• سجل أي ملاحظات

📊 تحديث المخزون:
• يتم تحديث المخزون تلقائياً عند حفظ فاتورة الشراء
• تحقق من الكميات الجديدة في المخزون
• راجع تكلفة البضائع

💰 إدارة المدفوعات:
• تسجيل دفعات للموردين
• متابعة المستحقات
• جدولة المدفوعات

📈 تقارير المشتريات:
• تقرير المشتريات حسب المورد
• تقرير تكلفة البضائع
• تقرير أداء الموردين"""

    def get_inventory_content(self):
        """محتوى إدارة المخزون"""
        return """📊 إدارة المخزون

نظام إدارة المخزون يوفر تتبعاً دقيقاً لجميع حركات المخزون.

📦 حركات المخزون:

📈 أنواع الحركات:
• دخول بضاعة (من المشتريات)
• خروج بضاعة (من المبيعات)
• تسوية مخزون
• تلف أو فقدان
• تحويل بين المخازن

📊 عرض حركات المخزون:
1. اذهب إلى قائمة "المخزون" → "حركات المخزون"
2. اختر المنتج أو الفترة الزمنية
3. راجع جميع الحركات
4. تحقق من الأرصدة

🔍 جرد المخزون:

📋 إجراء الجرد:
1. اذهب إلى قائمة "المخزون" → "جرد المخزون"
2. اختر المنتجات للجرد
3. أدخل الكميات الفعلية
4. قارن مع الكميات النظرية
5. سجل الفروقات
6. احفظ نتائج الجرد

⚠️ تنبيهات المخزون:

🔔 المخزون المنخفض:
• تنبيهات تلقائية عند انخفاض المخزون
• قائمة بالمنتجات التي تحتاج إعادة طلب
• تحديد الحد الأدنى لكل منتج

📊 تقارير المخزون:
• تقرير حالة المخزون
• تقرير المنتجات الراكدة
• تقرير سرعة دوران المخزون
• تقرير قيمة المخزون

🎯 نصائح إدارة المخزون:
• راجع المخزون بانتظام
• حدد حدود دنيا مناسبة
• تابع المنتجات سريعة الحركة
• احرص على دقة البيانات"""

    def get_reports_content(self):
        """محتوى التقارير"""
        return """📈 التقارير

نظام التقارير يوفر إحصائيات شاملة ومفصلة عن جميع عمليات البرنامج.

📊 تقارير المبيعات:

💰 تقرير المبيعات اليومية:
• إجمالي المبيعات لليوم
• عدد الفواتير
• متوسط قيمة الفاتورة
• طرق الدفع المستخدمة

📅 تقرير المبيعات الشهرية:
• مبيعات كل شهر
• مقارنة مع الأشهر السابقة
• نمو المبيعات
• أفضل الأيام في الشهر

🏆 تقرير أفضل المنتجات:
• المنتجات الأكثر مبيعاً
• الكميات المباعة
• الإيرادات من كل منتج
• هامش الربح

👥 تقرير أفضل العملاء:
• العملاء الأكثر شراءً
• قيمة مشتريات كل عميل
• تكرار الشراء
• متوسط قيمة الشراء

📊 تقارير المشتريات:

🛒 تقرير المشتريات حسب المورد:
• إجمالي المشتريات من كل مورد
• عدد فواتير الشراء
• متوسط قيمة الفاتورة
• تقييم أداء الموردين

💰 تقرير تكلفة البضائع:
• تكلفة البضائع المباعة
• هامش الربح الإجمالي
• نسبة التكلفة إلى المبيعات

📦 تقارير المخزون:

📋 تقرير حالة المخزون:
• الكميات الحالية لكل منتج
• قيمة المخزون
• المنتجات منخفضة المخزون
• المنتجات نافدة المخزون

🔄 تقرير حركات المخزون:
• جميع حركات الدخول والخروج
• أسباب الحركات
• تواريخ الحركات
• الأرصدة بعد كل حركة

💰 تقارير مالية:

📊 تقرير الأرباح والخسائر:
• إجمالي الإيرادات
• إجمالي التكاليف
• صافي الربح
• نسبة الربح

💳 تقرير التدفق النقدي:
• المقبوضات النقدية
• المدفوعات النقدية
• الرصيد النقدي
• التدفق الصافي

🎯 استخدام التقارير:

📅 تحديد الفترة الزمنية:
• اختر تاريخ البداية والنهاية
• أو اختر فترة محددة مسبقاً
• يومي، أسبوعي، شهري، سنوي

🔍 فلترة البيانات:
• فلترة حسب العميل أو المورد
• فلترة حسب المنتج أو الفئة
• فلترة حسب طريقة الدفع

🖨️ طباعة وحفظ التقارير:
• طباعة التقرير مباشرة
• حفظ كملف PDF
• تصدير إلى Excel
• إرسال بالبريد الإلكتروني

📊 تحليل البيانات:
• استخدم التقارير لاتخاذ قرارات مدروسة
• قارن الأداء بين الفترات المختلفة
• حدد الاتجاهات والأنماط
• اكتشف الفرص والتحديات"""

    def get_backup_content(self):
        """محتوى النسخ الاحتياطي"""
        return """💾 النسخ الاحتياطي

نظام النسخ الاحتياطي يضمن حماية بياناتك من الفقدان أو التلف.

🔄 أنواع النسخ الاحتياطي:

📱 نسخ احتياطي يدوي:
• ينشئه المستخدم عند الحاجة
• يمكن اختيار مكان الحفظ
• مناسب قبل التحديثات المهمة

⏰ نسخ احتياطي تلقائي:
• ينشئه النظام تلقائياً
• حسب فترة زمنية محددة (افتراضي 7 أيام)
• يحفظ في مجلد النسخ الاحتياطي

🛡️ نسخ احتياطي قبل الاستعادة:
• ينشئه النظام قبل استعادة نسخة احتياطية
• لحماية البيانات الحالية

➕ إنشاء نسخة احتياطية:

📋 الطريقة السريعة:
1. اذهب إلى قائمة "ملف" → "النسخ الاحتياطي"
2. اختر مكان الحفظ
3. انتظر انتهاء العملية

🔧 الطريقة المتقدمة:
1. اذهب إلى قائمة "الإدارة" → "إدارة النسخ الاحتياطي"
2. اضغط "إنشاء نسخة احتياطية"
3. راجع الإحصائيات والمعلومات

🔄 استعادة نسخة احتياطية:

⚠️ تحذير مهم:
استعادة النسخة الاحتياطية ستستبدل جميع البيانات الحالية!

📋 خطوات الاستعادة:
1. اذهب إلى قائمة "ملف" → "استعادة النسخة الاحتياطية"
2. اختر النسخة الاحتياطية:
   • من ملف خارجي
   • من النسخ المحفوظة
3. راجع معلومات النسخة
4. اختر خيارات الاستعادة:
   • إنشاء نسخة احتياطية قبل الاستعادة ✅
   • التحقق من سلامة النسخة ✅
   • إعادة تشغيل البرنامج تلقائياً ✅
5. اضغط "استعادة النسخة الاحتياطية"
6. أكد العملية
7. انتظر انتهاء الاستعادة

🗂️ إدارة النسخ الاحتياطية:

📊 عرض النسخ المتاحة:
• قائمة بجميع النسخ الاحتياطية
• معلومات كل نسخة (التاريخ، الحجم، النوع)
• حالة كل نسخة (صحيحة/تالفة)

🗑️ حذف النسخ القديمة:
• حذف النسخ غير المرغوب فيها
• تنظيف تلقائي للنسخ القديمة
• الاحتفاظ بآخر 10 نسخ افتراضياً

🔍 معاينة النسخة الاحتياطية:
• عرض محتويات النسخة
• معلومات تفصيلية
• التحقق من سلامة البيانات

⚙️ إعدادات النسخ الاحتياطي:

🔄 النسخ التلقائي:
• تفعيل/إلغاء النسخ التلقائي
• تحديد فترة النسخ (بالأيام)
• تحديد عدد النسخ المحفوظة

📁 مجلد النسخ:
• مكان حفظ النسخ التلقائية
• افتراضياً: data/backups/

🎯 نصائح مهمة:

✅ أفضل الممارسات:
• أنشئ نسخة احتياطية قبل أي تحديث مهم
• احتفظ بنسخ احتياطية في أماكن متعددة
• اختبر استعادة النسخ الاحتياطية دورياً
• راجع إعدادات النسخ التلقائي

⚠️ تحذيرات:
• لا تحذف جميع النسخ الاحتياطية
• تأكد من سلامة النسخة قبل الاستعادة
• احتفظ بنسخة احتياطية خارجية
• لا تقاطع عملية النسخ أو الاستعادة"""

    def get_users_content(self):
        """محتوى إدارة المستخدمين"""
        return """👤 إدارة المستخدمين

نظام إدارة المستخدمين يتيح للمدير إدارة حسابات المستخدمين وصلاحياتهم.

👥 أنواع المستخدمين:

🔑 المدير (Admin):
• صلاحيات كاملة على النظام
• إدارة المستخدمين والصلاحيات
• الوصول إلى جميع التقارير
• إدارة النسخ الاحتياطي والإعدادات

👤 المستخدم العادي (User):
• صلاحيات محدودة
• إدارة المبيعات والمشتريات
• عرض التقارير الأساسية
• لا يمكنه تعديل الإعدادات

➕ إضافة مستخدم جديد:

📋 خطوات الإضافة:
1. اذهب إلى قائمة "الإدارة" → "إدارة المستخدمين"
2. اضغط "إضافة مستخدم جديد"
3. املأ البيانات:
   • اسم المستخدم (مطلوب، فريد)
   • كلمة المرور (8 أحرف على الأقل)
   • تأكيد كلمة المرور
   • الاسم الكامل
   • البريد الإلكتروني
   • نوع المستخدم (مدير/مستخدم عادي)
   • حالة الحساب (نشط/غير نشط)
4. اضغط "حفظ"

✏️ تعديل بيانات مستخدم:

🔧 البيانات القابلة للتعديل:
• الاسم الكامل
• البريد الإلكتروني
• نوع المستخدم
• حالة الحساب
• كلمة المرور (إذا لزم الأمر)

⚠️ ملاحظات مهمة:
• لا يمكن تعديل اسم المستخدم بعد الإنشاء
• يجب وجود مدير واحد على الأقل نشط
• لا يمكن حذف المدير الأساسي

🔒 إدارة كلمات المرور:

🔑 تغيير كلمة المرور:
1. حدد المستخدم
2. اضغط "تغيير كلمة المرور"
3. أدخل كلمة المرور الجديدة
4. أكد كلمة المرور
5. احفظ التغييرات

✅ متطلبات كلمة المرور:
• 8 أحرف على الأقل
• تحتوي على أحرف وأرقام
• تجنب كلمات المرور الضعيفة
• غير كلمة المرور دورياً

📊 مراقبة نشاط المستخدمين:

📋 سجل النشاط:
• تسجيل الدخول والخروج
• العمليات المنجزة
• التوقيت والتاريخ
• عنوان IP (إن أمكن)

📈 إحصائيات الاستخدام:
• عدد مرات تسجيل الدخول
• آخر نشاط للمستخدم
• العمليات الأكثر استخداماً
• وقت الاستخدام الإجمالي

🛡️ الأمان والصلاحيات:

🔐 ميزات الأمان:
• تشفير كلمات المرور
• تسجيل جميع العمليات
• حماية من محاولات الاختراق
• انتهاء الجلسة التلقائي

⚙️ إدارة الصلاحيات:
• تحديد صلاحيات كل مستخدم
• منع الوصول لوظائف معينة
• تخصيص الصلاحيات حسب الحاجة

🎯 نصائح إدارة المستخدمين:

✅ أفضل الممارسات:
• استخدم أسماء مستخدمين واضحة
• حدد صلاحيات مناسبة لكل مستخدم
• راجع نشاط المستخدمين دورياً
• عطل الحسابات غير المستخدمة

⚠️ تحذيرات أمنية:
• لا تشارك بيانات تسجيل الدخول
• غير كلمات المرور الافتراضية فوراً
• راقب محاولات تسجيل الدخول المشبوهة
• احتفظ بنسخة احتياطية من بيانات المستخدمين"""

    def get_settings_content(self):
        """محتوى الإعدادات"""
        return """⚙️ الإعدادات

نظام الإعدادات يتيح لك تخصيص البرنامج حسب احتياجاتك.

🏢 إعدادات الشركة:

📋 المعلومات الأساسية:
• اسم الشركة
• العنوان الكامل
• أرقام الهواتف
• البريد الإلكتروني
• الموقع الإلكتروني
• رقم السجل التجاري
• الرقم الضريبي

🖼️ الشعار والهوية:
• رفع شعار الشركة
• اختيار ألوان الواجهة
• تخصيص رأس الفواتير

💰 إعدادات العملة:

💱 العملة الأساسية:
• اختيار العملة المستخدمة
• رمز العملة
• عدد الخانات العشرية
• تنسيق عرض الأرقام

🔄 أسعار الصرف:
• إضافة عملات إضافية
• تحديث أسعار الصرف
• التحويل التلقائي

📊 إعدادات النظام:

🎨 واجهة المستخدم:
• حجم الخط
• ألوان الواجهة
• لغة الواجهة
• اتجاه النص

📁 مسارات الملفات:
• مجلد قاعدة البيانات
• مجلد النسخ الاحتياطي
• مجلد التقارير
• مجلد الصور

🔔 التنبيهات والإشعارات:

⚠️ تنبيهات المخزون:
• تفعيل تنبيهات المخزون المنخفض
• تحديد الحد الأدنى العام
• تنبيهات المنتجات المنتهية الصلاحية

💰 تنبيهات مالية:
• تنبيهات المديونيات المتأخرة
• تنبيهات حدود الائتمان
• تنبيهات الفواتير المستحقة

💾 إعدادات النسخ الاحتياطي:

⏰ النسخ التلقائي:
• تفعيل/إلغاء النسخ التلقائي
• فترة النسخ (بالأيام)
• وقت النسخ المفضل
• عدد النسخ المحفوظة

📁 مجلد النسخ:
• تحديد مجلد النسخ الاحتياطي
• النسخ إلى مواقع متعددة
• النسخ إلى التخزين السحابي

🖨️ إعدادات الطباعة:

📄 تخطيط الفواتير:
• حجم الورق
• هوامش الصفحة
• معلومات الرأس والتذييل
• تفاصيل الفاتورة المعروضة

🖨️ الطابعة الافتراضية:
• اختيار الطابعة المفضلة
• إعدادات جودة الطباعة
• طباعة تلقائية للفواتير

🔐 إعدادات الأمان:

🔒 كلمات المرور:
• سياسة كلمات المرور
• مدة انتهاء كلمة المرور
• عدد محاولات تسجيل الدخول
• قفل الحساب التلقائي

📝 تسجيل العمليات:
• تفعيل تسجيل العمليات
• مستوى التفاصيل المسجلة
• مدة الاحتفاظ بالسجلات
• تصدير السجلات

💾 حفظ الإعدادات:

✅ تطبيق التغييرات:
1. عدل الإعدادات المطلوبة
2. اضغط "حفظ الإعدادات"
3. أعد تشغيل البرنامج إذا لزم الأمر

🔄 استعادة الإعدادات الافتراضية:
• إعادة تعيين جميع الإعدادات
• الاحتفاظ ببيانات الشركة
• تأكيد العملية قبل التنفيذ

📤 تصدير واستيراد الإعدادات:
• تصدير الإعدادات لملف
• استيراد إعدادات من ملف
• مشاركة الإعدادات بين الأجهزة"""

    def get_troubleshooting_content(self):
        """محتوى حل المشاكل"""
        return """🔧 حل المشاكل

هذا القسم يساعدك في حل المشاكل الشائعة التي قد تواجهها.

❌ مشاكل تسجيل الدخول:

🔐 "اسم المستخدم أو كلمة المرور غير صحيحة":
✅ الحلول:
• تأكد من كتابة البيانات بشكل صحيح
• تحقق من حالة الأحرف (كبيرة/صغيرة)
• جرب البيانات الافتراضية (admin/admin123)
• تأكد من عدم وجود مسافات إضافية

🔌 "خطأ في الاتصال بقاعدة البيانات":
✅ الحلول:
• تأكد من وجود ملف database.db
• تحقق من صلاحيات الملفات
• أعد تشغيل البرنامج كمدير
• تحقق من مساحة القرص الصلب

❌ مشاكل الأداء:

🐌 "البرنامج بطيء":
✅ الحلول:
• أغلق البرامج الأخرى غير الضرورية
• تأكد من توفر ذاكرة كافية (2 جيجابايت على الأقل)
• نظف ملفات النظام المؤقتة
• أعد تشغيل الجهاز

💾 "نفاد مساحة القرص":
✅ الحلول:
• احذف الملفات غير الضرورية
• نقل النسخ الاحتياطية القديمة
• استخدم أداة تنظيف القرص
• فكر في ترقية القرص الصلب

❌ مشاكل البيانات:

📊 "البيانات غير صحيحة":
✅ الحلول:
• تحقق من صحة البيانات المدخلة
• راجع التقارير للتأكد من الدقة
• استعد نسخة احتياطية حديثة
• اتصل بالدعم الفني إذا لزم الأمر

🔄 "فقدان البيانات":
✅ الحلول:
• استعد آخر نسخة احتياطية
• تحقق من مجلد النسخ الاحتياطي
• لا تدخل بيانات جديدة حتى الاستعادة
• اتصل بالدعم الفني فوراً

❌ مشاكل الطباعة:

🖨️ "لا يمكن الطباعة":
✅ الحلول:
• تأكد من تشغيل الطابعة
• تحقق من اتصال الطابعة بالجهاز
• تأكد من وجود ورق وحبر
• جرب طباعة من برنامج آخر
• أعد تثبيت تعريف الطابعة

📄 "تخطيط الطباعة غير صحيح":
✅ الحلول:
• تحقق من إعدادات الطباعة
• اختر حجم الورق الصحيح
• اضبط الهوامش في الإعدادات
• جرب طابعة أخرى للمقارنة

❌ مشاكل النسخ الاحتياطي:

💾 "فشل في إنشاء نسخة احتياطية":
✅ الحلول:
• تأكد من توفر مساحة كافية
• تحقق من صلاحيات الكتابة
• أغلق البرامج الأخرى
• جرب مجلد حفظ مختلف

🔄 "فشل في استعادة النسخة الاحتياطية":
✅ الحلول:
• تأكد من سلامة ملف النسخة الاحتياطية
• تحقق من صحة تنسيق الملف
• جرب نسخة احتياطية أخرى
• أنشئ نسخة احتياطية من البيانات الحالية أولاً

❌ مشاكل عامة:

🖥️ "البرنامج لا يعمل":
✅ الحلول:
• تأكد من تثبيت Python (إذا كنت تستخدم الكود المصدري)
• تحقق من وجود جميع الملفات المطلوبة
• أعد تشغيل الجهاز
• أعد تثبيت البرنامج

⚠️ "رسائل خطأ غير مفهومة":
✅ الحلول:
• اكتب نص رسالة الخطأ بالكامل
• ابحث عن الحل في هذا الدليل
• راجع سجل العمليات للتفاصيل
• اتصل بالدعم الفني مع تفاصيل الخطأ

🆘 طلب المساعدة:

📞 متى تتصل بالدعم الفني:
• عند فقدان البيانات المهمة
• عند ظهور أخطاء متكررة
• عند عدم قدرتك على حل المشكلة
• عند الحاجة لتدريب إضافي

📋 معلومات مطلوبة عند طلب الدعم:
• وصف دقيق للمشكلة
• خطوات إعادة إنتاج المشكلة
• رسائل الخطأ (إن وجدت)
• إصدار البرنامج ونظام التشغيل
• حجم قاعدة البيانات"""

    def get_shortcuts_content(self):
        """محتوى اختصارات لوحة المفاتيح"""
        return """⌨️ اختصارات لوحة المفاتيح

استخدم هذه الاختصارات لتسريع عملك وزيادة الإنتاجية.

🔧 اختصارات عامة:

⚡ الاختصارات الأساسية:
• Ctrl + N: إضافة جديد
• Ctrl + S: حفظ
• Ctrl + F: بحث
• Ctrl + P: طباعة
• F5: تحديث/إعادة تحميل
• Escape: إلغاء/إغلاق النافذة الحالية
• Enter: تأكيد/موافق
• Tab: الانتقال للحقل التالي
• Shift + Tab: الانتقال للحقل السابق

📋 اختصارات التنقل:
• Alt + F: قائمة ملف
• Alt + S: قائمة المبيعات
• Alt + P: قائمة المشتريات
• Alt + I: قائمة المخزون
• Alt + C: قائمة العملاء
• Alt + R: قائمة التقارير
• Alt + A: قائمة الإدارة
• Alt + H: قائمة المساعدة

💰 اختصارات المبيعات:

🧾 فاتورة البيع:
• Ctrl + Shift + S: فاتورة بيع جديدة
• F2: إضافة منتج للفاتورة
• F3: البحث عن منتج
• F4: اختيار عميل
• Ctrl + Enter: حفظ الفاتورة
• Ctrl + P: طباعة الفاتورة
• Delete: حذف سطر من الفاتورة

🛒 اختصارات المشتريات:

📦 فاتورة الشراء:
• Ctrl + Shift + P: فاتورة شراء جديدة
• F2: إضافة منتج للفاتورة
• F4: اختيار مورد
• Ctrl + Enter: حفظ فاتورة الشراء

📊 اختصارات المخزون:

📦 إدارة المنتجات:
• Ctrl + Shift + I: منتج جديد
• F2: تعديل منتج
• Delete: حذف منتج (مع تأكيد)
• Ctrl + F: البحث في المنتجات

👥 اختصارات العملاء:

🙋 إدارة العملاء:
• Ctrl + Shift + C: عميل جديد
• F2: تعديل بيانات عميل
• F3: كشف حساب العميل
• Ctrl + M: تسجيل دفعة من العميل

📈 اختصارات التقارير:

📊 التقارير السريعة:
• Ctrl + R: تقرير المبيعات اليومية
• Ctrl + Shift + R: تقرير المبيعات الشهرية
• Ctrl + I: تقرير حالة المخزون
• Ctrl + Shift + I: تقرير حركات المخزون

🔧 اختصارات الإدارة:

👤 إدارة المستخدمين:
• Ctrl + U: إدارة المستخدمين
• Ctrl + Shift + U: مستخدم جديد
• Ctrl + L: سجل نشاط المستخدمين

💾 النسخ الاحتياطي:
• Ctrl + B: إنشاء نسخة احتياطية
• Ctrl + Shift + B: استعادة نسخة احتياطية
• Ctrl + Alt + B: إدارة النسخ الاحتياطي

⚙️ اختصارات النوافذ:

🖥️ إدارة النوافذ:
• Alt + F4: إغلاق النافذة الحالية
• Ctrl + W: إغلاق النافذة الحالية
• Alt + Tab: التنقل بين النوافذ المفتوحة
• Windows + M: تصغير جميع النوافذ

📝 اختصارات النصوص:

✏️ تحرير النص:
• Ctrl + A: تحديد الكل
• Ctrl + C: نسخ
• Ctrl + V: لصق
• Ctrl + X: قص
• Ctrl + Z: تراجع
• Ctrl + Y: إعادة

🎯 نصائح لاستخدام الاختصارات:

✅ أفضل الممارسات:
• احفظ الاختصارات الأكثر استخداماً
• مارس الاختصارات بانتظام
• استخدم الاختصارات بدلاً من الماوس عند الإمكان
• علم الاختصارات للمستخدمين الآخرين

⚡ نصائح لزيادة السرعة:
• استخدم Tab للتنقل بين الحقول
• استخدم Enter لتأكيد العمليات
• استخدم Escape للإلغاء السريع
• استخدم F5 لتحديث البيانات

🔧 تخصيص الاختصارات:
• يمكن تخصيص بعض الاختصارات من الإعدادات
• احرص على عدم تعارض الاختصارات
• اختبر الاختصارات الجديدة قبل الاعتماد عليها"""

    def get_faq_content(self):
        """محتوى الأسئلة الشائعة"""
        return """❓ الأسئلة الشائعة

إجابات على الأسئلة الأكثر شيوعاً حول استخدام البرنامج.

🔐 أسئلة تسجيل الدخول:

❓ نسيت كلمة المرور، ماذا أفعل؟
✅ يمكن للمدير إعادة تعيين كلمة المرور من خلال:
• قائمة "الإدارة" → "إدارة المستخدمين"
• اختيار المستخدم → "تغيير كلمة المرور"
• إذا نسيت كلمة مرور المدير، اتصل بالدعم الفني

❓ هل يمكن أن يكون لدي أكثر من مدير؟
✅ نعم، يمكن إنشاء عدة حسابات مدير، لكن يُنصح بوجود مدير رئيسي واحد لأغراض الأمان.

💰 أسئلة المبيعات والمشتريات:

❓ كيف أتعامل مع المرتجعات؟
✅ يمكن التعامل مع المرتجعات من خلال:
• قائمة "المبيعات" → "مرتجعات المبيعات"
• أو قائمة "المشتريات" → "مرتجعات المشتريات"
• سيتم تحديث المخزون تلقائياً

❓ هل يمكن تعديل الفاتورة بعد حفظها؟
✅ يمكن تعديل الفواتير غير المدفوعة فقط. الفواتير المدفوعة لا يمكن تعديلها لأغراض المحاسبة.

❓ كيف أتعامل مع الخصومات؟
✅ يمكن إضافة خصم على مستوى:
• السطر الواحد في الفاتورة
• إجمالي الفاتورة
• خصم نسبي أو مبلغ ثابت

📦 أسئلة المخزون:

❓ لماذا لا يتطابق المخزون النظري مع الفعلي؟
✅ قد يحدث هذا بسبب:
• أخطاء في إدخال البيانات
• تلف أو فقدان بضائع
• عدم تسجيل بعض الحركات
• استخدم وظيفة "جرد المخزون" لتصحيح الأرصدة

❓ كيف أضبط الحد الأدنى للمخزون؟
✅ من خلال:
• قائمة "المخزون" → "إدارة المنتجات"
• اختيار المنتج → تعديل → "الحد الأدنى للمخزون"

📊 أسئلة التقارير:

❓ لماذا لا تظهر بياناتي في التقرير؟
✅ تحقق من:
• الفترة الزمنية المحددة
• فلاتر التقرير
• صحة البيانات المدخلة
• تحديث التقرير (F5)

❓ هل يمكن تصدير التقارير لـ Excel؟
✅ نعم، معظم التقارير يمكن تصديرها إلى:
• ملف PDF
• ملف Excel
• طباعة مباشرة

💾 أسئلة النسخ الاحتياطي:

❓ كم مرة يجب أن أنشئ نسخة احتياطية؟
✅ يُنصح بـ:
• نسخة احتياطية يومية للبيانات المهمة
• نسخة احتياطية أسبوعية كحد أدنى
• نسخة احتياطية قبل أي تحديث مهم

❓ أين يتم حفظ النسخ الاحتياطية التلقائية؟
✅ افتراضياً في مجلد: data/backups/
يمكن تغيير المجلد من الإعدادات.

❓ هل النسخ الاحتياطية آمنة؟
✅ النسخ الاحتياطية مضغوطة ومحمية، لكن يُنصح بـ:
• حفظ نسخ في أماكن متعددة
• استخدام كلمات مرور قوية
• تشفير النسخ الحساسة

👥 أسئلة المستخدمين:

❓ كيف أغير صلاحيات مستخدم؟
✅ المدير فقط يمكنه:
• قائمة "الإدارة" → "إدارة المستخدمين"
• اختيار المستخدم → تعديل → تغيير النوع

❓ هل يمكن تتبع نشاط المستخدمين؟
✅ نعم، من خلال:
• قائمة "الإدارة" → "سجل العمليات"
• يتم تسجيل جميع العمليات المهمة

⚙️ أسئلة تقنية:

❓ ما هي متطلبات النظام؟
✅ الحد الأدنى:
• Windows 7 أو أحدث
• 2 جيجابايت RAM
• 100 ميجابايت مساحة تخزين
• دقة شاشة 1024x768

❓ هل يعمل البرنامج على Mac أو Linux؟
✅ البرنامج مطور لـ Windows، لكن يمكن تشغيله على أنظمة أخرى باستخدام:
• Wine على Linux
• Virtual Machine
• تواصل مع الدعم الفني للمساعدة

❓ هل يمكن استخدام البرنامج على شبكة؟
✅ النسخة الحالية مصممة للاستخدام المحلي، لكن يمكن:
• مشاركة قاعدة البيانات عبر الشبكة
• استخدام التخزين السحابي للنسخ الاحتياطية
• تواصل معنا لحلول الشبكات

💡 نصائح عامة:

❓ كيف أحسن أداء البرنامج؟
✅ نصائح للأداء الأمثل:
• نظف قاعدة البيانات دورياً
• احذف البيانات القديمة غير المهمة
• أغلق البرامج الأخرى عند الاستخدام المكثف
• استخدم جهاز بمواصفات جيدة

❓ كيف أتعلم استخدام البرنامج بسرعة؟
✅ للتعلم السريع:
• ابدأ بالبيانات التجريبية
• اقرأ هذا الدليل بالكامل
• جرب كل ميزة بنفسك
• اطلب التدريب من الدعم الفني"""

    def get_contact_content(self):
        """محتوى التواصل والدعم"""
        return """📞 التواصل والدعم

نحن هنا لمساعدتك في أي وقت. تواصل معنا للحصول على الدعم والمساعدة.

🆘 الدعم الفني:

📧 البريد الإلكتروني:
• <EMAIL>
• وقت الاستجابة: خلال 24 ساعة
• أرفق تفاصيل المشكلة وصور الشاشة إن أمكن

📞 الهاتف:
• +966 11 123 4567
• أوقات العمل: الأحد - الخميس، 9 صباحاً - 5 مساءً
• للمشاكل العاجلة والدعم المباشر

💬 الدردشة المباشرة:
• متوفرة على موقعنا الإلكتروني
• أوقات العمل: الأحد - الخميس، 9 صباحاً - 5 مساءً
• للاستفسارات السريعة

🎓 التدريب والتعليم:

📚 دورات تدريبية:
• دورات مجانية عبر الإنترنت
• تدريب شخصي في موقعك
• ورش عمل جماعية
• مواد تدريبية مكتوبة ومرئية

🎥 فيديوهات تعليمية:
• قناتنا على YouTube
• فيديوهات خطوة بخطوة
• نصائح وحيل للاستخدام الأمثل
• تحديثات دورية بالميزات الجديدة

📖 مصادر التعلم:
• هذا الدليل التفاعلي
• أدلة PDF قابلة للتحميل
• مقالات في المدونة
• منتدى المستخدمين

🔧 خدمات إضافية:

⚙️ التخصيص والتطوير:
• تخصيص البرنامج حسب احتياجاتك
• إضافة ميزات جديدة
• تكامل مع أنظمة أخرى
• تطوير تقارير خاصة

🔄 التحديثات:
• تحديثات مجانية للميزات الجديدة
• إصلاحات الأخطاء
• تحسينات الأداء
• ميزات أمان محدثة

💾 خدمات البيانات:
• استرداد البيانات المفقودة
• تنظيف وتحسين قاعدة البيانات
• نقل البيانات من أنظمة أخرى
• نسخ احتياطية سحابية

🌐 المواقع والحسابات:

🖥️ الموقع الرسمي:
• www.salesinventory.com
• تحميل التحديثات
• الأخبار والإعلانات
• مركز المساعدة

📱 وسائل التواصل الاجتماعي:
• تويتر: @SalesInventory
• فيسبوك: Sales Inventory System
• لينكد إن: Sales Inventory Solutions
• يوتيوب: Sales Inventory Tutorials

📋 عند طلب الدعم:

📝 معلومات مطلوبة:
• وصف دقيق للمشكلة
• خطوات إعادة إنتاج المشكلة
• رسائل الخطأ (نص كامل أو صورة)
• إصدار البرنامج
• نظام التشغيل
• حجم قاعدة البيانات تقريباً

📸 صور الشاشة:
• صورة للخطأ أو المشكلة
• صورة للإعدادات ذات الصلة
• فيديو قصير إذا أمكن

⏰ أوقات الاستجابة:

🚨 المشاكل العاجلة:
• فقدان البيانات: خلال ساعة
• عدم القدرة على العمل: خلال 4 ساعات
• مشاكل أمنية: فوراً

📞 المشاكل العادية:
• أسئلة الاستخدام: خلال 24 ساعة
• طلبات التحسين: خلال 48 ساعة
• التدريب: حسب الجدولة المتفق عليها

💰 الأسعار والباقات:

🆓 الدعم المجاني:
• دليل المستخدم
• التحديثات الأساسية
• الدعم عبر البريد الإلكتروني
• منتدى المستخدمين

💎 الدعم المميز:
• دعم هاتفي مباشر
• استجابة أسرع
• تدريب شخصي
• تخصيص محدود

🏢 الدعم المؤسسي:
• دعم على مدار الساعة
• مدير حساب مخصص
• تخصيص كامل
• تدريب شامل للفريق

🤝 شراكات وتعاون:

👥 شركاء التنفيذ:
• شركاء معتمدون في مختلف المناطق
• خدمات التنفيذ والتدريب
• الدعم المحلي
• التخصيص والتطوير

🏪 موزعون:
• موزعون معتمدون
• خصومات للكميات
• دعم ما بعد البيع
• تدريب المستخدمين

شكراً لاختيارك برنامج محاسبة المبيعات والمخازن!
نحن ملتزمون بتقديم أفضل خدمة ودعم لضمان نجاحك."""

    def print_guide(self):
        """طباعة دليل المستخدم"""
        try:
            # إنشاء نافذة معاينة الطباعة
            print_window = tk.Toplevel(self.window)
            print_window.title("معاينة الطباعة - دليل المستخدم")
            print_window.geometry("800x600")
            print_window.configure(bg=COLORS['background'])

            # توسيط النافذة
            print_window.update_idletasks()
            x = (print_window.winfo_screenwidth() // 2) - (400)
            y = (print_window.winfo_screenheight() // 2) - (300)
            print_window.geometry(f'800x600+{x}+{y}')

            # العنوان
            tk.Label(print_window, text="معاينة الطباعة",
                    font=FONTS['heading'], bg=COLORS['background']).pack(pady=10)

            # منطقة النص للطباعة
            text_frame = tk.Frame(print_window, bg=COLORS['background'])
            text_frame.pack(fill='both', expand=True, padx=20, pady=10)

            print_text = tk.Text(text_frame, font=FONTS['small'],
                               wrap=tk.WORD, bg='white', fg=COLORS['text'])

            scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=print_text.yview)
            print_text.configure(yscrollcommand=scrollbar.set)

            # تجميع محتوى الدليل للطباعة
            full_content = self.get_full_guide_content()
            print_text.insert('1.0', full_content)
            print_text.config(state='disabled')

            print_text.pack(side=tk.LEFT, fill='both', expand=True)
            scrollbar.pack(side=tk.RIGHT, fill='y')

            # أزرار التحكم
            buttons_frame = tk.Frame(print_window, bg=COLORS['background'])
            buttons_frame.pack(fill='x', padx=20, pady=10)

            tk.Button(buttons_frame, text="طباعة", font=FONTS['button'],
                     bg=COLORS['success'], fg='white', width=15,
                     command=lambda: self.execute_print(print_text)).pack(side=tk.LEFT, padx=5)

            tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                     bg=COLORS['danger'], fg='white', width=15,
                     command=print_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في معاينة الطباعة:\n{str(e)}")

    def execute_print(self, text_widget):
        """تنفيذ عملية الطباعة"""
        try:
            # هذه دالة مبسطة للطباعة
            # في التطبيق الحقيقي، يمكن استخدام مكتبات طباعة متقدمة
            import subprocess
            import tempfile

            # إنشاء ملف مؤقت للطباعة
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                content = text_widget.get('1.0', tk.END)
                f.write(content)
                temp_file = f.name

            # فتح الملف للطباعة (Windows)
            subprocess.run(['notepad', '/p', temp_file], check=True)

            messagebox.showinfo("نجح", "تم إرسال الدليل للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في الطباعة:\n{str(e)}")

    def save_as_pdf(self):
        """حفظ دليل المستخدم كملف PDF"""
        try:
            from tkinter import filedialog

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ دليل المستخدم",
                defaultextension=".pdf",
                filetypes=[("ملفات PDF", "*.pdf"), ("جميع الملفات", "*.*")],
                initialfilename="دليل_المستخدم.pdf"
            )

            if file_path:
                # هذه دالة مبسطة - في التطبيق الحقيقي يمكن استخدام مكتبة reportlab
                messagebox.showinfo("معلومات",
                                   "ميزة حفظ PDF ستكون متاحة في التحديث القادم.\n"
                                   "يمكنك حالياً استخدام الطباعة وحفظها كـ PDF من إعدادات الطابعة.")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ PDF:\n{str(e)}")

    def search_guide(self):
        """البحث في دليل المستخدم"""
        try:
            # إنشاء نافذة البحث
            search_window = tk.Toplevel(self.window)
            search_window.title("البحث في دليل المستخدم")
            search_window.geometry("500x400")
            search_window.configure(bg=COLORS['background'])
            search_window.transient(self.window)

            # توسيط النافذة
            search_window.update_idletasks()
            x = (search_window.winfo_screenwidth() // 2) - (250)
            y = (search_window.winfo_screenheight() // 2) - (200)
            search_window.geometry(f'500x400+{x}+{y}')

            # العنوان
            tk.Label(search_window, text="البحث في دليل المستخدم",
                    font=FONTS['heading'], bg=COLORS['background']).pack(pady=10)

            # إطار البحث
            search_frame = tk.Frame(search_window, bg=COLORS['background'])
            search_frame.pack(fill='x', padx=20, pady=10)

            tk.Label(search_frame, text="كلمة البحث:", font=FONTS['normal'],
                    bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)

            search_entry = tk.Entry(search_frame, font=FONTS['normal'], width=30)
            search_entry.pack(side=tk.RIGHT, padx=5, fill='x', expand=True)

            search_button = tk.Button(search_frame, text="بحث", font=FONTS['button'],
                                    bg=COLORS['primary'], fg='white', width=10)
            search_button.pack(side=tk.RIGHT, padx=5)

            # نتائج البحث
            results_frame = tk.LabelFrame(search_window, text="نتائج البحث",
                                        font=FONTS['normal'], bg=COLORS['background'])
            results_frame.pack(fill='both', expand=True, padx=20, pady=10)

            results_listbox = tk.Listbox(results_frame, font=FONTS['normal'])
            results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical',
                                            command=results_listbox.yview)
            results_listbox.configure(yscrollcommand=results_scrollbar.set)

            results_listbox.pack(side=tk.LEFT, fill='both', expand=True, padx=(10, 0), pady=10)
            results_scrollbar.pack(side=tk.RIGHT, fill='y', padx=(0, 10), pady=10)

            # دالة البحث
            def perform_search():
                search_term = search_entry.get().strip()
                if not search_term:
                    messagebox.showwarning("تحذير", "يرجى إدخال كلمة البحث")
                    return

                results_listbox.delete(0, tk.END)
                found_results = []

                # البحث في جميع أقسام الدليل
                for section_id, section_data in self.guide_sections.items():
                    title = section_data['title']
                    content = section_data['content']

                    if search_term.lower() in title.lower() or search_term.lower() in content.lower():
                        found_results.append((section_id, title))

                if found_results:
                    for section_id, title in found_results:
                        results_listbox.insert(tk.END, title)

                    # ربط النقر على النتيجة
                    def on_result_select(event):
                        selection = results_listbox.curselection()
                        if selection:
                            selected_index = selection[0]
                            section_id, title = found_results[selected_index]
                            self.show_section(section_id)
                            search_window.destroy()

                    results_listbox.bind('<Double-1>', on_result_select)
                else:
                    results_listbox.insert(tk.END, "لم يتم العثور على نتائج")

            search_button.config(command=perform_search)
            search_entry.bind('<Return>', lambda e: perform_search())

            # زر الإغلاق
            tk.Button(search_window, text="إغلاق", font=FONTS['button'],
                     bg=COLORS['danger'], fg='white', width=15,
                     command=search_window.destroy).pack(pady=10)

            # تركيز على حقل البحث
            search_entry.focus()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في البحث:\n{str(e)}")

    def get_full_guide_content(self):
        """الحصول على المحتوى الكامل للدليل"""
        full_content = f"""
دليل المستخدم - برنامج محاسبة المبيعات والمخازن
{'=' * 60}

تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
المستخدم: {self.current_user['username']}

{'=' * 60}

"""

        for section_id, section_data in self.guide_sections.items():
            full_content += f"\n{section_data['title']}\n"
            full_content += "=" * len(section_data['title']) + "\n\n"
            full_content += section_data['content'] + "\n\n"
            full_content += "-" * 60 + "\n\n"

        return full_content
