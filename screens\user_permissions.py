# -*- coding: utf-8 -*-
"""
شاشة إدارة صلاحيات المستخدمين
"""

import tkinter as tk
from tkinter import ttk, messagebox
from config.settings import COLORS, FONTS, USER_ROLES
from utils.database_manager import DatabaseManager
from utils.helpers import check_user_permission, show_permission_error, log_user_activity
from utils.arabic_support import ArabicSupport

class UserPermissions:
    """كلاس إدارة صلاحيات المستخدمين"""
    
    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()
        
        # التحقق من الصلاحية
        if not check_user_permission(current_user['role'], 'users_management'):
            show_permission_error('إدارة صلاحيات المستخدمين')
            return
        
        self.setup_window()
        self.create_widgets()
        self.load_users()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة صلاحيات المستخدمين")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        ArabicSupport.setup_window_rtl(self.window)
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة صلاحيات المستخدمين",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)
        
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # الجانب الأيسر - قائمة المستخدمين
        left_frame = tk.LabelFrame(main_frame, text="المستخدمين", 
                                  font=FONTS['heading'], bg=COLORS['background'])
        left_frame.pack(side=tk.RIGHT, fill='y', padx=(0, 10))
        
        # قائمة المستخدمين
        self.users_listbox = tk.Listbox(
            left_frame,
            font=FONTS['normal'],
            width=25,
            height=20,
            selectmode=tk.SINGLE
        )
        self.users_listbox.pack(padx=10, pady=10, fill='both', expand=True)
        self.users_listbox.bind('<<ListboxSelect>>', self.on_user_select)
        
        # الجانب الأيمن - تفاصيل الصلاحيات
        right_frame = tk.LabelFrame(main_frame, text="صلاحيات المستخدم", 
                                   font=FONTS['heading'], bg=COLORS['background'])
        right_frame.pack(side=tk.LEFT, fill='both', expand=True)
        
        # معلومات المستخدم المحدد
        self.user_info_frame = tk.Frame(right_frame, bg=COLORS['background'])
        self.user_info_frame.pack(fill='x', padx=10, pady=10)
        
        self.selected_user_label = tk.Label(
            self.user_info_frame,
            text="اختر مستخدماً لعرض صلاحياته",
            font=FONTS['heading'],
            bg=COLORS['background'],
            fg=COLORS['secondary']
        )
        self.selected_user_label.pack()
        
        # إطار تغيير الدور
        role_frame = tk.LabelFrame(right_frame, text="تغيير دور المستخدم", 
                                  font=FONTS['normal'], bg=COLORS['background'])
        role_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        tk.Label(role_frame, text="الدور الجديد:", font=FONTS['normal'], 
                bg=COLORS['background']).pack(side=tk.RIGHT, padx=5)
        
        self.role_var = tk.StringVar()
        self.role_combo = ttk.Combobox(role_frame, textvariable=self.role_var, 
                                      font=FONTS['normal'], width=20, state='readonly')
        self.role_combo['values'] = list(USER_ROLES.keys())
        self.role_combo.pack(side=tk.RIGHT, padx=5)
        
        tk.Button(role_frame, text="تحديث الدور", font=FONTS['button'],
                 bg=COLORS['primary'], fg='white', 
                 command=self.update_user_role).pack(side=tk.RIGHT, padx=10)
        
        # إطار عرض الصلاحيات
        permissions_frame = tk.LabelFrame(right_frame, text="الصلاحيات الحالية", 
                                         font=FONTS['normal'], bg=COLORS['background'])
        permissions_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إنشاء إطار قابل للتمرير للصلاحيات
        canvas = tk.Canvas(permissions_frame, bg=COLORS['background'])
        scrollbar = ttk.Scrollbar(permissions_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="right", fill="both", expand=True)
        scrollbar.pack(side="left", fill="y")
        
        # إطار الأزرار السفلي
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(buttons_frame, text="تحديث", font=FONTS['button'],
                 bg=COLORS['info'], fg='white', width=15,
                 command=self.load_users).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="إغلاق", font=FONTS['button'],
                 bg=COLORS['danger'], fg='white', width=15,
                 command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            # مسح القائمة الحالية
            self.users_listbox.delete(0, tk.END)
            
            # جلب المستخدمين من قاعدة البيانات
            query = "SELECT id, name, username, role, is_active FROM users ORDER BY name"
            users = self.db_manager.execute_query(query)
            
            for user in users:
                status = "نشط" if user['is_active'] else "غير نشط"
                role_name = USER_ROLES.get(user['role'], {}).get('name', user['role'])
                display_text = f"{user['name']} ({user['username']}) - {role_name} - {status}"
                self.users_listbox.insert(tk.END, display_text)
                
            # حفظ بيانات المستخدمين للمرجع
            self.users_data = users
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المستخدمين:\n{str(e)}")
            
    def on_user_select(self, event=None):
        """معالج اختيار مستخدم"""
        try:
            selection = self.users_listbox.curselection()
            if not selection:
                return
                
            user_index = selection[0]
            selected_user = self.users_data[user_index]
            
            # تحديث معلومات المستخدم المحدد
            self.selected_user_label.config(
                text=f"المستخدم: {selected_user['name']} ({selected_user['username']})"
            )
            
            # تحديث الدور في القائمة المنسدلة
            self.role_var.set(selected_user['role'])
            
            # عرض الصلاحيات
            self.display_permissions(selected_user['role'])
            
            # حفظ المستخدم المحدد
            self.selected_user = selected_user
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في اختيار المستخدم:\n{str(e)}")
            
    def display_permissions(self, role):
        """عرض صلاحيات الدور"""
        try:
            # مسح الصلاحيات الحالية
            for widget in self.scrollable_frame.winfo_children():
                widget.destroy()
                
            # الحصول على صلاحيات الدور
            permissions = USER_ROLES.get(role, {}).get('permissions', [])
            
            if 'all' in permissions:
                # المدير له جميع الصلاحيات
                tk.Label(
                    self.scrollable_frame,
                    text="🔑 جميع الصلاحيات (مدير النظام)",
                    font=FONTS['heading'],
                    bg=COLORS['background'],
                    fg=COLORS['success']
                ).pack(anchor='w', padx=10, pady=5)
                
                # عرض جميع الصلاحيات المتاحة
                all_permissions = [
                    'users_management', 'sales_create', 'sales_view', 'sales_management',
                    'purchases_create', 'purchases_management', 'products_management',
                    'inventory_management', 'customers_management', 'suppliers_management',
                    'reports_view', 'system_settings'
                ]
                
                for perm in all_permissions:
                    perm_text = self.get_permission_text(perm)
                    tk.Label(
                        self.scrollable_frame,
                        text=f"✅ {perm_text}",
                        font=FONTS['normal'],
                        bg=COLORS['background'],
                        fg=COLORS['success']
                    ).pack(anchor='w', padx=20, pady=2)
                    
            else:
                # عرض الصلاحيات المحددة
                if permissions:
                    for perm in permissions:
                        perm_text = self.get_permission_text(perm)
                        tk.Label(
                            self.scrollable_frame,
                            text=f"✅ {perm_text}",
                            font=FONTS['normal'],
                            bg=COLORS['background'],
                            fg=COLORS['success']
                        ).pack(anchor='w', padx=10, pady=2)
                else:
                    tk.Label(
                        self.scrollable_frame,
                        text="❌ لا توجد صلاحيات محددة",
                        font=FONTS['normal'],
                        bg=COLORS['background'],
                        fg=COLORS['danger']
                    ).pack(anchor='w', padx=10, pady=5)
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في عرض الصلاحيات:\n{str(e)}")
            
    def get_permission_text(self, permission):
        """الحصول على النص العربي للصلاحية"""
        permission_texts = {
            'users_management': 'إدارة المستخدمين',
            'sales_create': 'إنشاء فواتير المبيعات',
            'sales_view': 'عرض فواتير المبيعات',
            'sales_management': 'إدارة المبيعات',
            'purchases_create': 'إنشاء فواتير المشتريات',
            'purchases_management': 'إدارة المشتريات',
            'products_management': 'إدارة المنتجات',
            'inventory_management': 'إدارة المخزون',
            'customers_management': 'إدارة العملاء',
            'suppliers_management': 'إدارة الموردين',
            'reports_view': 'عرض التقارير',
            'system_settings': 'إعدادات النظام'
        }
        return permission_texts.get(permission, permission)
        
    def update_user_role(self):
        """تحديث دور المستخدم"""
        try:
            if not hasattr(self, 'selected_user'):
                messagebox.showwarning("تنبيه", "يرجى اختيار مستخدم أولاً")
                return
                
            new_role = self.role_var.get()
            if not new_role:
                messagebox.showwarning("تنبيه", "يرجى اختيار دور جديد")
                return
                
            if new_role == self.selected_user['role']:
                messagebox.showinfo("معلومة", "الدور المحدد هو نفس الدور الحالي")
                return
                
            # تأكيد التغيير
            result = messagebox.askyesno(
                "تأكيد التحديث",
                f"هل تريد تغيير دور المستخدم '{self.selected_user['name']}' "
                f"من '{USER_ROLES.get(self.selected_user['role'], {}).get('name', self.selected_user['role'])}' "
                f"إلى '{USER_ROLES.get(new_role, {}).get('name', new_role)}'؟"
            )
            
            if result:
                # تحديث الدور في قاعدة البيانات
                query = "UPDATE users SET role = ? WHERE id = ?"
                self.db_manager.execute_query(query, [new_role, self.selected_user['id']])
                
                # تسجيل العملية
                old_role_name = USER_ROLES.get(self.selected_user['role'], {}).get('name', self.selected_user['role'])
                new_role_name = USER_ROLES.get(new_role, {}).get('name', new_role)
                details = f"تغيير دور المستخدم '{self.selected_user['name']}' من '{old_role_name}' إلى '{new_role_name}'"
                log_user_activity(
                    self.db_manager,
                    self.current_user['id'],
                    "تحديث صلاحيات مستخدم",
                    details,
                    "users",
                    self.selected_user['id']
                )
                
                messagebox.showinfo("نجح", "تم تحديث دور المستخدم بنجاح")
                
                # إعادة تحميل البيانات
                self.load_users()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحديث دور المستخدم:\n{str(e)}")
