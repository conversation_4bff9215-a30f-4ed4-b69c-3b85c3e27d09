# -*- coding: utf-8 -*-
"""
شاشة إدارة المستخدمين
"""

import tkinter as tk
from tkinter import ttk, messagebox
from config.settings import COLORS, FONTS, USER_ROLES
from utils.database_manager import DatabaseManager
from utils.helpers import validate_email, validate_phone, get_user_role_text

class UsersManagement:
    """كلاس إدارة المستخدمين"""

    def __init__(self, parent, current_user):
        self.parent = parent
        self.current_user = current_user
        self.db_manager = DatabaseManager()

        # التحقق من الصلاحيات
        if current_user['role'] != 'admin':
            messagebox.showerror("خطأ", "ليس لديك صلاحية للوصول إلى إدارة المستخدمين")
            return

        self.setup_window()
        self.create_widgets()
        self.load_users()

    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة المستخدمين")
        self.window.geometry("900x600")
        self.window.configure(bg=COLORS['background'])

        # توسيط النافذة
        self.center_window()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 900
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="إدارة المستخدمين",
            font=FONTS['title'],
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg=COLORS['background'])
        buttons_frame.pack(pady=10)

        # زر إضافة مستخدم جديد
        add_button = tk.Button(
            buttons_frame,
            text="إضافة مستخدم جديد",
            font=FONTS['button'],
            bg=COLORS['success'],
            fg='white',
            command=self.add_user_dialog
        )
        add_button.pack(side=tk.LEFT, padx=5)

        # زر تعديل مستخدم
        edit_button = tk.Button(
            buttons_frame,
            text="تعديل المستخدم",
            font=FONTS['button'],
            bg=COLORS['warning'],
            fg='white',
            command=self.edit_user_dialog
        )
        edit_button.pack(side=tk.LEFT, padx=5)

        # زر حذف مستخدم
        delete_button = tk.Button(
            buttons_frame,
            text="حذف المستخدم",
            font=FONTS['button'],
            bg=COLORS['danger'],
            fg='white',
            command=self.delete_user
        )
        delete_button.pack(side=tk.LEFT, padx=5)

        # زر تحديث القائمة
        refresh_button = tk.Button(
            buttons_frame,
            text="تحديث",
            font=FONTS['button'],
            bg=COLORS['info'],
            fg='white',
            command=self.load_users
        )
        refresh_button.pack(side=tk.LEFT, padx=5)

        # إطار جدول المستخدمين
        table_frame = tk.Frame(self.window, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # إنشاء Treeview للمستخدمين
        columns = ('ID', 'اسم المستخدم', 'الاسم الكامل', 'الدور', 'البريد الإلكتروني', 'الهاتف', 'الحالة')
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تحديد عناوين الأعمدة
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)

        # ترتيب العناصر
        self.users_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # ربط النقر المزدوج بالتعديل
        self.users_tree.bind('<Double-1>', lambda e: self.edit_user_dialog())

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            # مسح البيانات الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # جلب المستخدمين من قاعدة البيانات
            query = """
                SELECT id, username, name, role, email, phone, is_active, created_at
                FROM users
                ORDER BY created_at DESC
            """
            users = self.db_manager.execute_query(query)

            # إضافة المستخدمين إلى الجدول
            for user in users:
                status = "نشط" if user['is_active'] else "غير نشط"
                role_text = get_user_role_text(user['role'])

                self.users_tree.insert('', 'end', values=(
                    user['id'],
                    user['username'],
                    user['name'],
                    role_text,
                    user['email'] or '',
                    user['phone'] or '',
                    status
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المستخدمين:\n{str(e)}")

    def get_selected_user(self):
        """الحصول على المستخدم المحدد"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم من القائمة")
            return None

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]

        # جلب بيانات المستخدم الكاملة
        query = "SELECT * FROM users WHERE id = ?"
        users = self.db_manager.execute_query(query, (user_id,))

        if users:
            return dict(users[0])
        return None

    def add_user_dialog(self):
        """حوار إضافة مستخدم جديد"""
        self.user_dialog(mode='add')

    def edit_user_dialog(self):
        """حوار تعديل مستخدم"""
        user = self.get_selected_user()
        if user:
            self.user_dialog(mode='edit', user_data=user)

    def user_dialog(self, mode='add', user_data=None):
        """حوار إضافة/تعديل مستخدم"""
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة مستخدم جديد" if mode == 'add' else "تعديل المستخدم")
        dialog.geometry("400x500")
        dialog.configure(bg=COLORS['background'])
        dialog.resizable(False, False)

        # توسيط الحوار
        dialog.transient(self.window)
        dialog.grab_set()

        # المتغيرات
        username_var = tk.StringVar(value=user_data['username'] if user_data else '')
        name_var = tk.StringVar(value=user_data['name'] if user_data else '')
        email_var = tk.StringVar(value=user_data['email'] if user_data else '')
        phone_var = tk.StringVar(value=user_data['phone'] if user_data else '')
        role_var = tk.StringVar(value=user_data['role'] if user_data else 'salesperson')
        active_var = tk.BooleanVar(value=bool(user_data['is_active']) if user_data else True)
        password_var = tk.StringVar()

        # إطار المحتوى
        content_frame = tk.Frame(dialog, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم المستخدم
        tk.Label(content_frame, text="اسم المستخدم:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        username_entry = tk.Entry(content_frame, textvariable=username_var, font=FONTS['normal'],
                                width=30, justify='right')
        username_entry.pack(pady=(0, 10))

        # الاسم الكامل
        tk.Label(content_frame, text="الاسم الكامل:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        name_entry = tk.Entry(content_frame, textvariable=name_var, font=FONTS['normal'],
                            width=30, justify='right')
        name_entry.pack(pady=(0, 10))

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        email_entry = tk.Entry(content_frame, textvariable=email_var, font=FONTS['normal'],
                             width=30, justify='right')
        email_entry.pack(pady=(0, 10))

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        phone_entry = tk.Entry(content_frame, textvariable=phone_var, font=FONTS['normal'],
                             width=30, justify='right')
        phone_entry.pack(pady=(0, 10))

        # الدور
        tk.Label(content_frame, text="الدور:", font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        role_combo = ttk.Combobox(content_frame, textvariable=role_var, font=FONTS['normal'], width=27)
        role_combo['values'] = list(USER_ROLES.keys())
        role_combo.pack(pady=(0, 10))

        # كلمة المرور
        password_label_text = "كلمة المرور الجديدة:" if mode == 'edit' else "كلمة المرور:"
        tk.Label(content_frame, text=password_label_text, font=FONTS['normal'],
                bg=COLORS['background']).pack(anchor='e', pady=(0, 5))
        password_entry = tk.Entry(content_frame, textvariable=password_var, font=FONTS['normal'],
                                width=30, show='*')
        password_entry.pack(pady=(0, 10))

        if mode == 'edit':
            tk.Label(content_frame, text="(اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور)",
                    font=FONTS['small'], bg=COLORS['background'], fg=COLORS['info']).pack(pady=(0, 10))

        # حالة المستخدم
        active_check = tk.Checkbutton(content_frame, text="المستخدم نشط", variable=active_var,
                                    font=FONTS['normal'], bg=COLORS['background'])
        active_check.pack(pady=10)

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(content_frame, bg=COLORS['background'])
        buttons_frame.pack(pady=20)

        def save_user():
            # التحقق من البيانات
            if not username_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
                return

            if not name_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
                return

            if mode == 'add' and not password_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
                return

            # التحقق من البريد الإلكتروني
            if email_var.get().strip() and not validate_email(email_var.get().strip()):
                messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
                return

            # التحقق من رقم الهاتف
            if phone_var.get().strip() and not validate_phone(phone_var.get().strip()):
                messagebox.showerror("خطأ", "رقم الهاتف غير صحيح")
                return

            try:
                if mode == 'add':
                    # إضافة مستخدم جديد
                    password_hash = self.db_manager.hash_password(password_var.get().strip())
                    query = """
                        INSERT INTO users (username, password_hash, name, role, email, phone, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        username_var.get().strip(),
                        password_hash,
                        name_var.get().strip(),
                        role_var.get(),
                        email_var.get().strip() or None,
                        phone_var.get().strip() or None,
                        1 if active_var.get() else 0
                    )
                else:
                    # تعديل مستخدم موجود
                    if password_var.get().strip():
                        # تحديث كلمة المرور أيضاً
                        password_hash = self.db_manager.hash_password(password_var.get().strip())
                        query = """
                            UPDATE users
                            SET username=?, password_hash=?, name=?, role=?, email=?, phone=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                            WHERE id=?
                        """
                        params = (
                            username_var.get().strip(),
                            password_hash,
                            name_var.get().strip(),
                            role_var.get(),
                            email_var.get().strip() or None,
                            phone_var.get().strip() or None,
                            1 if active_var.get() else 0,
                            user_data['id']
                        )
                    else:
                        # عدم تحديث كلمة المرور
                        query = """
                            UPDATE users
                            SET username=?, name=?, role=?, email=?, phone=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                            WHERE id=?
                        """
                        params = (
                            username_var.get().strip(),
                            name_var.get().strip(),
                            role_var.get(),
                            email_var.get().strip() or None,
                            phone_var.get().strip() or None,
                            1 if active_var.get() else 0,
                            user_data['id']
                        )

                self.db_manager.execute_query(query, params)
                messagebox.showinfo("نجح", "تم حفظ المستخدم بنجاح")
                dialog.destroy()
                self.load_users()

            except Exception as e:
                if "UNIQUE constraint failed" in str(e):
                    messagebox.showerror("خطأ", "اسم المستخدم موجود مسبقاً")
                else:
                    messagebox.showerror("خطأ", f"حدث خطأ في حفظ المستخدم:\n{str(e)}")

        save_button = tk.Button(buttons_frame, text="حفظ", font=FONTS['button'],
                              bg=COLORS['success'], fg='white', command=save_user)
        save_button.pack(side=tk.LEFT, padx=5)

        cancel_button = tk.Button(buttons_frame, text="إلغاء", font=FONTS['button'],
                                bg=COLORS['danger'], fg='white', command=dialog.destroy)
        cancel_button.pack(side=tk.LEFT, padx=5)

    def delete_user(self):
        """حذف مستخدم"""
        user = self.get_selected_user()
        if not user:
            return

        # منع حذف المستخدم الحالي
        if user['id'] == self.current_user['id']:
            messagebox.showerror("خطأ", "لا يمكنك حذف حسابك الخاص")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف المستخدم '{user['name']}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه.")

        if result:
            try:
                query = "DELETE FROM users WHERE id = ?"
                self.db_manager.execute_query(query, (user['id'],))
                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                self.load_users()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف المستخدم:\n{str(e)}")