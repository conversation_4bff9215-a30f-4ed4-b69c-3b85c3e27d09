# -*- coding: utf-8 -*-
"""
إعداد بسيط لجدول فئات المنتجات
"""

import os
import sys
import sqlite3
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import DATABASE_PATH

def setup_categories():
    """إعداد جدول فئات المنتجات"""
    try:
        print("🚀 بدء إعداد جدول فئات المنتجات...")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # حذف الجدول القديم إذا كان موجوداً
        cursor.execute("DROP TABLE IF EXISTS categories")
        print("🗑️ تم حذف الجدول القديم")
        
        # إنشاء جدول الفئات الجديد
        create_table_query = """
            CREATE TABLE categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                user_id INTEGER NOT NULL DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """
        
        cursor.execute(create_table_query)
        print("✅ تم إنشاء جدول الفئات الجديد")
        
        # إنشاء فهارس
        indexes = [
            "CREATE INDEX idx_categories_name ON categories (name)",
            "CREATE INDEX idx_categories_is_active ON categories (is_active)",
            "CREATE INDEX idx_categories_user_id ON categories (user_id)"
        ]
        
        for index_query in indexes:
            cursor.execute(index_query)
        
        print("✅ تم إنشاء الفهارس")
        
        # إنشاء فئات تجريبية
        sample_categories = [
            ('إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية'),
            ('ملابس', 'ملابس رجالية ونسائية وأطفال'),
            ('أغذية ومشروبات', 'مواد غذائية ومشروبات متنوعة'),
            ('أدوات منزلية', 'أدوات وأجهزة منزلية'),
            ('كتب وقرطاسية', 'كتب ومواد قرطاسية ومكتبية'),
            ('رياضة وترفيه', 'معدات رياضية وألعاب ترفيهية'),
            ('صحة وجمال', 'منتجات العناية الشخصية والصحة'),
            ('أثاث ومفروشات', 'أثاث منزلي ومفروشات'),
            ('سيارات وقطع غيار', 'قطع غيار ومعدات السيارات'),
            ('أدوات وعدد', 'أدوات يدوية وعدد صناعية')
        ]
        
        insert_query = """
            INSERT INTO categories (name, description, is_active, user_id, created_at)
            VALUES (?, ?, 1, 1, ?)
        """
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for name, description in sample_categories:
            cursor.execute(insert_query, (name, description, current_time))
        
        print(f"✅ تم إنشاء {len(sample_categories)} فئة تجريبية")
        
        # ربط المنتجات الموجودة بالفئات
        print("\n🔄 ربط المنتجات بالفئات...")
        
        # جلب المنتجات النشطة
        cursor.execute("SELECT id, name FROM products WHERE is_active = 1")
        products = cursor.fetchall()
        
        if products:
            # جلب معرفات الفئات
            cursor.execute("SELECT id FROM categories WHERE is_active = 1")
            category_ids = [row[0] for row in cursor.fetchall()]
            
            import random
            
            products_updated = 0
            for product in products:
                # اختيار فئة عشوائية
                category_id = random.choice(category_ids)
                
                # تحديث المنتج
                cursor.execute("UPDATE products SET category_id = ? WHERE id = ?", 
                             (category_id, product['id']))
                products_updated += 1
            
            print(f"✅ تم ربط {products_updated} منتج بالفئات")
        else:
            print("ℹ️ لا توجد منتجات لربطها بالفئات")
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض الإحصائيات
        print("\n📊 إحصائيات فئات المنتجات:")
        print("=" * 50)
        
        # إجمالي الفئات
        cursor.execute("SELECT COUNT(*) FROM categories")
        total_categories = cursor.fetchone()[0]
        print(f"📋 إجمالي الفئات: {total_categories}")
        
        # الفئات النشطة
        cursor.execute("SELECT COUNT(*) FROM categories WHERE is_active = 1")
        active_categories = cursor.fetchone()[0]
        print(f"✅ الفئات النشطة: {active_categories}")
        
        # توزيع المنتجات حسب الفئات
        cursor.execute("""
            SELECT c.name, COUNT(p.id) as products_count
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
            WHERE c.is_active = 1
            GROUP BY c.id, c.name
            ORDER BY products_count DESC
        """)
        
        category_stats = cursor.fetchall()
        
        print(f"\n📈 توزيع المنتجات حسب الفئات:")
        print("-" * 40)
        
        for stat in category_stats:
            print(f"• {stat['name']}: {stat['products_count']} منتج")
        
        print("\n💡 نصائح للاختبار:")
        print("1. اذهب إلى قائمة المخزون → إدارة الفئات")
        print("2. جرب إنشاء فئة جديدة")
        print("3. جرب تعديل فئة موجودة")
        print("4. جرب تغيير حالة الفئة (تفعيل/إلغاء تفعيل)")
        print("5. استخدم البحث والفلاتر")
        print("6. لاحظ الإحصائيات في أعلى الشاشة")
        print("7. انقر نقرة مزدوجة على فئة لعرض التفاصيل")
        print("8. اذهب إلى إدارة المنتجات ولاحظ إمكانية اختيار الفئة")
        
        conn.close()
        print("\n✅ تم إعداد نظام إدارة الفئات بنجاح!")
        
    except Exception as e:
        print(f"❌ حدث خطأ في إعداد الفئات: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    setup_categories()
