# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام محاسبة المبيعات والمخازن
"""

import os
import sys
import sqlite3
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database_manager import DatabaseManager
from utils.helpers import *

class SystemTester:
    """كلاس اختبار النظام"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """تسجيل نتيجة الاختبار"""
        status = "✅ نجح" if success else "❌ فشل"
        result = f"{status} - {test_name}"
        if message:
            result += f": {message}"
        
        self.test_results.append((test_name, success, message))
        print(result)
        
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            conn.close()
            
            success = result[0] == 1
            self.log_test("اتصال قاعدة البيانات", success)
            return success
        except Exception as e:
            self.log_test("اتصال قاعدة البيانات", False, str(e))
            return False
            
    def test_database_tables(self):
        """اختبار وجود جداول قاعدة البيانات"""
        required_tables = [
            'users', 'categories', 'products', 'customers', 'suppliers',
            'sales_invoices', 'sales_invoice_items', 'purchase_invoices',
            'purchase_invoice_items', 'payments', 'inventory_movements'
        ]
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            if not missing_tables:
                self.log_test("جداول قاعدة البيانات", True, f"جميع الجداول موجودة ({len(required_tables)})")
                return True
            else:
                self.log_test("جداول قاعدة البيانات", False, f"جداول مفقودة: {', '.join(missing_tables)}")
                return False
                
        except Exception as e:
            self.log_test("جداول قاعدة البيانات", False, str(e))
            return False
            
    def test_default_admin_user(self):
        """اختبار وجود المستخدم الافتراضي"""
        try:
            user = self.db_manager.authenticate_user('admin', 'admin123')
            
            if user:
                self.log_test("المستخدم الافتراضي", True, f"المستخدم: {user['name']}")
                return True
            else:
                self.log_test("المستخدم الافتراضي", False, "فشل في تسجيل الدخول")
                return False
                
        except Exception as e:
            self.log_test("المستخدم الافتراضي", False, str(e))
            return False
            
    def test_helper_functions(self):
        """اختبار الدوال المساعدة"""
        tests_passed = 0
        total_tests = 0
        
        # اختبار تنسيق العملة
        total_tests += 1
        try:
            result = format_currency(1234.56)
            if "1,234.56" in result:
                tests_passed += 1
            else:
                print(f"  ❌ تنسيق العملة: النتيجة غير متوقعة - {result}")
        except Exception as e:
            print(f"  ❌ تنسيق العملة: {str(e)}")
            
        # اختبار التحقق من البريد الإلكتروني
        total_tests += 1
        try:
            valid_email = validate_email("<EMAIL>")
            invalid_email = validate_email("invalid-email")
            if valid_email and not invalid_email:
                tests_passed += 1
            else:
                print(f"  ❌ التحقق من البريد الإلكتروني: النتيجة غير متوقعة")
        except Exception as e:
            print(f"  ❌ التحقق من البريد الإلكتروني: {str(e)}")
            
        # اختبار التحقق من الأرقام
        total_tests += 1
        try:
            valid_number = is_valid_number("123.45", 0, 1000)
            invalid_number = is_valid_number("abc", 0, 1000)
            if valid_number and not invalid_number:
                tests_passed += 1
            else:
                print(f"  ❌ التحقق من الأرقام: النتيجة غير متوقعة")
        except Exception as e:
            print(f"  ❌ التحقق من الأرقام: {str(e)}")
            
        # اختبار حساب الضريبة
        total_tests += 1
        try:
            tax = calculate_tax(100, 10)  # 10% من 100 = 10
            if abs(tax - 10.0) < 0.01:
                tests_passed += 1
            else:
                print(f"  ❌ حساب الضريبة: النتيجة غير متوقعة - {tax}")
        except Exception as e:
            print(f"  ❌ حساب الضريبة: {str(e)}")
            
        success = tests_passed == total_tests
        self.log_test("الدوال المساعدة", success, f"{tests_passed}/{total_tests} اختبارات نجحت")
        return success
        
    def test_invoice_number_generation(self):
        """اختبار إنشاء أرقام الفواتير"""
        try:
            sales_number = self.db_manager.get_next_invoice_number('sales')
            purchase_number = self.db_manager.get_next_invoice_number('purchase')
            
            sales_valid = sales_number.startswith('INV-')
            purchase_valid = purchase_number.startswith('PUR-')
            
            if sales_valid and purchase_valid:
                self.log_test("إنشاء أرقام الفواتير", True, f"مبيعات: {sales_number}, مشتريات: {purchase_number}")
                return True
            else:
                self.log_test("إنشاء أرقام الفواتير", False, "تنسيق الأرقام غير صحيح")
                return False
                
        except Exception as e:
            self.log_test("إنشاء أرقام الفواتير", False, str(e))
            return False
            
    def test_sample_data_insertion(self):
        """اختبار إدراج بيانات تجريبية"""
        try:
            # إضافة فئة تجريبية
            category_query = "INSERT INTO categories (name, description) VALUES (?, ?)"
            self.db_manager.execute_query(category_query, ("فئة تجريبية", "فئة للاختبار"))
            
            # إضافة منتج تجريبي
            product_query = """
                INSERT INTO products (name, category_id, unit, cost_price, selling_price, stock_quantity, min_stock_level)
                VALUES (?, 1, ?, ?, ?, ?, ?)
            """
            self.db_manager.execute_query(product_query, ("منتج تجريبي", "قطعة", 10.0, 15.0, 100, 10))
            
            # إضافة عميل تجريبي
            customer_query = "INSERT INTO customers (name, phone, email) VALUES (?, ?, ?)"
            self.db_manager.execute_query(customer_query, ("عميل تجريبي", "123456789", "<EMAIL>"))
            
            # إضافة مورد تجريبي
            supplier_query = "INSERT INTO suppliers (name, phone, email) VALUES (?, ?, ?)"
            self.db_manager.execute_query(supplier_query, ("مورد تجريبي", "987654321", "<EMAIL>"))
            
            self.log_test("إدراج البيانات التجريبية", True, "تم إدراج فئة، منتج، عميل، ومورد")
            return True
            
        except Exception as e:
            self.log_test("إدراج البيانات التجريبية", False, str(e))
            return False
            
    def test_file_structure(self):
        """اختبار هيكل الملفات"""
        required_files = [
            'main.py',
            'config/settings.py',
            'utils/database_manager.py',
            'utils/helpers.py',
            'utils/arabic_support.py',
            'screens/login_screen.py',
            'screens/main_interface.py',
            'screens/users_management.py',
            'screens/products_management.py',
            'screens/customers_management.py',
            'screens/suppliers_management.py',
            'screens/sales_management.py',
            'screens/purchases_management.py',
            'screens/reports_management.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
                
        if not missing_files:
            self.log_test("هيكل الملفات", True, f"جميع الملفات موجودة ({len(required_files)})")
            return True
        else:
            self.log_test("هيكل الملفات", False, f"ملفات مفقودة: {', '.join(missing_files)}")
            return False
            
    def test_imports(self):
        """اختبار استيراد الوحدات"""
        modules_to_test = [
            'config.settings',
            'utils.database_manager',
            'utils.helpers',
            'utils.arabic_support',
            'screens.login_screen',
            'screens.main_interface',
            'screens.users_management',
            'screens.products_management',
            'screens.customers_management',
            'screens.suppliers_management',
            'screens.sales_management',
            'screens.purchases_management',
            'screens.reports_management'
        ]
        
        failed_imports = []
        for module in modules_to_test:
            try:
                __import__(module)
            except Exception as e:
                failed_imports.append(f"{module}: {str(e)}")
                
        if not failed_imports:
            self.log_test("استيراد الوحدات", True, f"جميع الوحدات تم استيرادها ({len(modules_to_test)})")
            return True
        else:
            self.log_test("استيراد الوحدات", False, f"فشل في: {', '.join(failed_imports)}")
            return False
            
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار نظام محاسبة المبيعات والمخازن")
        print("=" * 60)
        
        tests = [
            self.test_file_structure,
            self.test_imports,
            self.test_database_connection,
            self.test_database_tables,
            self.test_default_admin_user,
            self.test_helper_functions,
            self.test_invoice_number_generation,
            self.test_sample_data_insertion
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test in tests:
            if test():
                passed_tests += 1
                
        print("=" * 60)
        print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبارات نجحت")
        
        if passed_tests == total_tests:
            print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        else:
            print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
            
        return passed_tests == total_tests

def main():
    """الدالة الرئيسية"""
    tester = SystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ يمكنك الآن تشغيل البرنامج باستخدام: python main.py")
    else:
        print("\n❌ يرجى إصلاح الأخطاء قبل تشغيل البرنامج")
        
    return success

if __name__ == "__main__":
    main()
