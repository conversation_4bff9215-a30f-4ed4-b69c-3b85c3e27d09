# -*- coding: utf-8 -*-
"""
دعم اللغة العربية والاتجاه من اليمين إلى اليسار
"""

import tkinter as tk
from config.settings import FONTS, SYSTEM_SETTINGS

class ArabicSupport:
    """كلاس دعم اللغة العربية"""
    
    @staticmethod
    def configure_rtl_widget(widget, widget_type='default'):
        """تكوين عنصر واجهة لدعم RTL"""
        try:
            if widget_type == 'entry':
                widget.configure(justify='right')
            elif widget_type == 'label':
                widget.configure(anchor='e')
            elif widget_type == 'text':
                widget.configure(justify='right')
            elif widget_type == 'button':
                widget.configure(anchor='center')
        except Exception:
            pass
        
        return widget
    
    @staticmethod
    def create_rtl_entry(parent, **kwargs):
        """إنشاء Entry مع دعم RTL"""
        entry = tk.Entry(parent, justify='right', **kwargs)
        return entry
    
    @staticmethod
    def create_rtl_label(parent, text="", **kwargs):
        """إنشاء Label مع دعم RTL"""
        label = tk.Label(parent, text=text, anchor='e', **kwargs)
        return label
    
    @staticmethod
    def create_rtl_text(parent, **kwargs):
        """إنشاء Text مع دعم RTL"""
        text_widget = tk.Text(parent, **kwargs)
        # تعيين اتجاه النص
        text_widget.configure(justify='right')
        return text_widget
    
    @staticmethod
    def apply_arabic_font(widget):
        """تطبيق خط عربي على العنصر"""
        try:
            widget.configure(font=FONTS['normal'])
        except Exception:
            pass
    
    @staticmethod
    def setup_window_rtl(window):
        """إعداد النافذة لدعم RTL"""
        try:
            # تعيين الخط الافتراضي
            window.option_add('*Font', FONTS['normal'])
            
            # تعيين اتجاه النص
            if SYSTEM_SETTINGS.get('direction') == 'rtl':
                window.option_add('*Entry.justify', 'right')
                window.option_add('*Text.justify', 'right')
                
        except Exception:
            pass
    
    @staticmethod
    def format_arabic_number(number):
        """تنسيق الأرقام للعربية"""
        try:
            # تحويل الأرقام الإنجليزية إلى عربية إذا لزم الأمر
            arabic_digits = '٠١٢٣٤٥٦٧٨٩'
            english_digits = '0123456789'
            
            number_str = str(number)
            
            # يمكن إضافة تحويل الأرقام هنا إذا رغبت
            # for i, digit in enumerate(english_digits):
            #     number_str = number_str.replace(digit, arabic_digits[i])
            
            return number_str
        except:
            return str(number)
    
    @staticmethod
    def align_text_right(widget):
        """محاذاة النص إلى اليمين"""
        try:
            widget_class = widget.winfo_class()
            
            if widget_class == 'Entry':
                widget.configure(justify='right')
            elif widget_class == 'Label':
                widget.configure(anchor='e')
            elif widget_class == 'Text':
                widget.configure(justify='right')
            elif widget_class == 'Listbox':
                widget.configure(justify='right')
                
        except Exception:
            pass
    
    @staticmethod
    def create_arabic_frame(parent, **kwargs):
        """إنشاء إطار مع دعم العربية"""
        frame = tk.Frame(parent, **kwargs)
        ArabicSupport.setup_frame_rtl(frame)
        return frame
    
    @staticmethod
    def setup_frame_rtl(frame):
        """إعداد الإطار لدعم RTL"""
        try:
            # تطبيق الإعدادات على جميع العناصر الفرعية
            for child in frame.winfo_children():
                ArabicSupport.align_text_right(child)
                ArabicSupport.apply_arabic_font(child)
        except Exception:
            pass
    
    @staticmethod
    def create_rtl_treeview(parent, **kwargs):
        """إنشاء Treeview مع دعم RTL"""
        from tkinter import ttk
        
        tree = ttk.Treeview(parent, **kwargs)
        
        # تعيين محاذاة الأعمدة إلى اليمين
        try:
            if 'columns' in kwargs:
                for col in kwargs['columns']:
                    tree.column(col, anchor='e')
        except Exception:
            pass
            
        return tree
    
    @staticmethod
    def pack_rtl(widget, side='right', **kwargs):
        """تعبئة العنصر مع دعم RTL"""
        if SYSTEM_SETTINGS.get('direction') == 'rtl':
            if side == 'left':
                side = 'right'
            elif side == 'right':
                side = 'left'
        
        widget.pack(side=side, **kwargs)
    
    @staticmethod
    def grid_rtl(widget, row=0, column=0, sticky='e', **kwargs):
        """ترتيب العنصر في الشبكة مع دعم RTL"""
        if SYSTEM_SETTINGS.get('direction') == 'rtl':
            if 'w' in sticky:
                sticky = sticky.replace('w', 'e')
            elif 'e' in sticky:
                sticky = sticky.replace('e', 'w')
            elif sticky == '':
                sticky = 'e'
        
        widget.grid(row=row, column=column, sticky=sticky, **kwargs)

# دوال مساعدة سريعة
def rtl_entry(parent, **kwargs):
    """إنشاء Entry مع دعم RTL - دالة مختصرة"""
    return ArabicSupport.create_rtl_entry(parent, **kwargs)

def rtl_label(parent, text="", **kwargs):
    """إنشاء Label مع دعم RTL - دالة مختصرة"""
    return ArabicSupport.create_rtl_label(parent, text, **kwargs)

def rtl_text(parent, **kwargs):
    """إنشاء Text مع دعم RTL - دالة مختصرة"""
    return ArabicSupport.create_rtl_text(parent, **kwargs)

def setup_rtl(window):
    """إعداد النافذة لدعم RTL - دالة مختصرة"""
    return ArabicSupport.setup_window_rtl(window)
