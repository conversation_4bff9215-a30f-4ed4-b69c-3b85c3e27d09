# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطي
"""

import os
import shutil
import sqlite3
import zipfile
import json
from datetime import datetime, timedelta
from tkinter import messagebox, filedialog
from config.settings import DATABASE_PATH, DATABASE_DIR
from utils.helpers import log_user_activity

class BackupManager:
    """كلاس إدارة النسخ الاحتياطي"""
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        self.backup_dir = os.path.join(DATABASE_DIR, 'backups')
        self.ensure_backup_directory()
        
    def ensure_backup_directory(self):
        """التأكد من وجود مجلد النسخ الاحتياطي"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            
    def create_backup(self, user_id=None, backup_type='manual', show_dialog=True):
        """إنشاء نسخة احتياطية"""
        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{backup_type}_{timestamp}.zip"
            
            if show_dialog:
                # السماح للمستخدم باختيار مكان الحفظ
                backup_path = filedialog.asksaveasfilename(
                    title="حفظ النسخة الاحتياطية",
                    defaultextension=".zip",
                    filetypes=[("ملفات مضغوطة", "*.zip"), ("جميع الملفات", "*.*")],
                    initialfilename=backup_filename
                )
                
                if not backup_path:
                    return False, "تم إلغاء العملية"
            else:
                # حفظ تلقائي في مجلد النسخ الاحتياطي
                backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
                # إضافة قاعدة البيانات
                if os.path.exists(DATABASE_PATH):
                    backup_zip.write(DATABASE_PATH, 'database.db')
                
                # إضافة ملف الإعدادات إذا كان موجوداً
                settings_file = os.path.join(DATABASE_DIR, 'program_settings.json')
                if os.path.exists(settings_file):
                    backup_zip.write(settings_file, 'program_settings.json')
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'created_at': datetime.now().isoformat(),
                    'backup_type': backup_type,
                    'database_size': os.path.getsize(DATABASE_PATH) if os.path.exists(DATABASE_PATH) else 0,
                    'version': '1.0',
                    'user_id': user_id
                }
                
                backup_zip.writestr('backup_info.json', json.dumps(backup_info, ensure_ascii=False, indent=2))
            
            # تسجيل العملية
            if self.db_manager and user_id:
                log_user_activity(
                    self.db_manager,
                    user_id,
                    "إنشاء نسخة احتياطية",
                    f"تم إنشاء نسخة احتياطية: {os.path.basename(backup_path)}",
                    "backup"
                )
            
            # تنظيف النسخ القديمة (للنسخ التلقائية فقط)
            if not show_dialog:
                self.cleanup_old_backups()
            
            return True, backup_path
            
        except Exception as e:
            error_msg = f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
            return False, error_msg
            
    def restore_backup(self, user_id=None, backup_path=None):
        """استعادة نسخة احتياطية"""
        try:
            if not backup_path:
                # السماح للمستخدم باختيار ملف النسخة الاحتياطية
                backup_path = filedialog.askopenfilename(
                    title="اختيار النسخة الاحتياطية",
                    filetypes=[("ملفات مضغوطة", "*.zip"), ("جميع الملفات", "*.*")]
                )
                
                if not backup_path:
                    return False, "تم إلغاء العملية"
            
            # التحقق من صحة الملف
            if not zipfile.is_zipfile(backup_path):
                return False, "الملف المختار ليس نسخة احتياطية صحيحة"
            
            # تأكيد الاستعادة
            result = messagebox.askyesno(
                "تأكيد الاستعادة",
                "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n"
                "سيتم استبدال البيانات الحالية بالكامل.\n"
                "هذا الإجراء لا يمكن التراجع عنه."
            )
            
            if not result:
                return False, "تم إلغاء العملية"
            
            # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
            current_backup_result, current_backup_path = self.create_backup(
                user_id, 'pre_restore', show_dialog=False
            )
            
            # استخراج النسخة الاحتياطية
            temp_dir = os.path.join(DATABASE_DIR, 'temp_restore')
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir)
            
            with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                backup_zip.extractall(temp_dir)
            
            # التحقق من محتويات النسخة الاحتياطية
            database_file = os.path.join(temp_dir, 'database.db')
            if not os.path.exists(database_file):
                shutil.rmtree(temp_dir)
                return False, "النسخة الاحتياطية لا تحتوي على قاعدة بيانات صحيحة"
            
            # التحقق من صحة قاعدة البيانات
            try:
                conn = sqlite3.connect(database_file)
                conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                conn.close()
            except Exception as e:
                shutil.rmtree(temp_dir)
                return False, f"قاعدة البيانات في النسخة الاحتياطية تالفة: {str(e)}"
            
            # استعادة قاعدة البيانات
            if os.path.exists(DATABASE_PATH):
                os.remove(DATABASE_PATH)
            shutil.move(database_file, DATABASE_PATH)
            
            # استعادة ملف الإعدادات إذا كان موجوداً
            settings_file = os.path.join(temp_dir, 'program_settings.json')
            if os.path.exists(settings_file):
                target_settings = os.path.join(DATABASE_DIR, 'program_settings.json')
                shutil.move(settings_file, target_settings)
            
            # قراءة معلومات النسخة الاحتياطية
            backup_info_file = os.path.join(temp_dir, 'backup_info.json')
            backup_info = {}
            if os.path.exists(backup_info_file):
                with open(backup_info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
            
            # تنظيف المجلد المؤقت
            shutil.rmtree(temp_dir)
            
            # تسجيل العملية
            if self.db_manager and user_id:
                log_user_activity(
                    self.db_manager,
                    user_id,
                    "استعادة نسخة احتياطية",
                    f"تم استعادة نسخة احتياطية من: {os.path.basename(backup_path)}",
                    "restore"
                )
            
            return True, f"تم استعادة النسخة الاحتياطية بنجاح\nتاريخ النسخة: {backup_info.get('created_at', 'غير محدد')}"
            
        except Exception as e:
            error_msg = f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}"
            return False, error_msg
            
    def cleanup_old_backups(self, max_backups=10):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            # جلب قائمة النسخ الاحتياطية
            backup_files = []
            for filename in os.listdir(self.backup_dir):
                if filename.startswith('backup_') and filename.endswith('.zip'):
                    filepath = os.path.join(self.backup_dir, filename)
                    backup_files.append((filepath, os.path.getmtime(filepath)))
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # حذف النسخ الزائدة
            if len(backup_files) > max_backups:
                for filepath, _ in backup_files[max_backups:]:
                    try:
                        os.remove(filepath)
                        print(f"تم حذف النسخة الاحتياطية القديمة: {os.path.basename(filepath)}")
                    except Exception as e:
                        print(f"خطأ في حذف النسخة الاحتياطية: {str(e)}")
                        
        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {str(e)}")
            
    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            backup_list = []
            
            if not os.path.exists(self.backup_dir):
                return backup_list
            
            for filename in os.listdir(self.backup_dir):
                if filename.startswith('backup_') and filename.endswith('.zip'):
                    filepath = os.path.join(self.backup_dir, filename)
                    
                    # معلومات الملف
                    file_size = os.path.getsize(filepath)
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                    
                    # محاولة قراءة معلومات النسخة الاحتياطية
                    backup_info = {}
                    try:
                        with zipfile.ZipFile(filepath, 'r') as backup_zip:
                            if 'backup_info.json' in backup_zip.namelist():
                                info_data = backup_zip.read('backup_info.json')
                                backup_info = json.loads(info_data.decode('utf-8'))
                    except:
                        pass
                    
                    backup_list.append({
                        'filename': filename,
                        'filepath': filepath,
                        'size': file_size,
                        'created_at': file_time,
                        'backup_type': backup_info.get('backup_type', 'unknown'),
                        'info': backup_info
                    })
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            backup_list.sort(key=lambda x: x['created_at'], reverse=True)
            
            return backup_list
            
        except Exception as e:
            print(f"خطأ في جلب قائمة النسخ الاحتياطية: {str(e)}")
            return []
            
    def auto_backup_check(self, user_id=None):
        """فحص الحاجة للنسخ الاحتياطي التلقائي"""
        try:
            # تحميل إعدادات النسخ الاحتياطي
            settings = self.load_backup_settings()
            
            if not settings.get('auto_backup', True):
                return False, "النسخ الاحتياطي التلقائي معطل"
            
            backup_interval = settings.get('backup_interval', 7)  # أيام
            
            # البحث عن آخر نسخة احتياطية تلقائية
            backup_list = self.get_backup_list()
            last_auto_backup = None
            
            for backup in backup_list:
                if backup['backup_type'] in ['auto', 'automatic']:
                    last_auto_backup = backup['created_at']
                    break
            
            # التحقق من الحاجة لنسخة احتياطية جديدة
            if last_auto_backup is None:
                # لا توجد نسخة احتياطية تلقائية سابقة
                return True, "لا توجد نسخة احتياطية تلقائية سابقة"
            
            days_since_backup = (datetime.now() - last_auto_backup).days
            
            if days_since_backup >= backup_interval:
                return True, f"مضى {days_since_backup} يوم منذ آخر نسخة احتياطية"
            
            return False, f"آخر نسخة احتياطية منذ {days_since_backup} يوم"
            
        except Exception as e:
            return False, f"خطأ في فحص النسخ الاحتياطي: {str(e)}"
            
    def load_backup_settings(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        try:
            settings_file = os.path.join(DATABASE_DIR, 'program_settings.json')
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings.get('system', {})
        except:
            pass
        
        # إعدادات افتراضية
        return {
            'auto_backup': True,
            'backup_interval': 7,
            'max_backup_files': 10
        }
        
    def perform_auto_backup(self, user_id=None):
        """تنفيذ نسخة احتياطية تلقائية"""
        try:
            need_backup, reason = self.auto_backup_check(user_id)
            
            if need_backup:
                success, result = self.create_backup(user_id, 'automatic', show_dialog=False)
                
                if success:
                    print(f"✅ تم إنشاء نسخة احتياطية تلقائية: {os.path.basename(result)}")
                    return True, f"تم إنشاء نسخة احتياطية تلقائية بنجاح"
                else:
                    print(f"❌ فشل في إنشاء النسخة الاحتياطية التلقائية: {result}")
                    return False, result
            else:
                return False, reason
                
        except Exception as e:
            error_msg = f"خطأ في النسخ الاحتياطي التلقائي: {str(e)}"
            print(f"❌ {error_msg}")
            return False, error_msg
