# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
يحتوي على جميع العمليات المتعلقة بقاعدة البيانات
"""

import sqlite3
import os
import hashlib
from datetime import datetime
from config.settings import DATABASE_PATH, get_current_datetime

class DatabaseManager:
    """كلاس إدارة قاعدة البيانات"""
    
    def __init__(self):
        self.db_path = DATABASE_PATH
        
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    name TEXT NOT NULL,
                    role TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول فئات المنتجات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول المنتجات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    barcode TEXT UNIQUE,
                    category_id INTEGER,
                    description TEXT,
                    unit TEXT DEFAULT 'قطعة',
                    cost_price REAL DEFAULT 0,
                    selling_price REAL DEFAULT 0,
                    stock_quantity INTEGER DEFAULT 0,
                    min_stock_level INTEGER DEFAULT 0,
                    image_path TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            ''')
            
            # جدول العملاء
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    tax_number TEXT,
                    credit_limit REAL DEFAULT 0,
                    current_balance REAL DEFAULT 0,
                    notes TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول الموردين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    tax_number TEXT,
                    current_balance REAL DEFAULT 0,
                    notes TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول فواتير المبيعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales_invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    user_id INTEGER NOT NULL,
                    invoice_date TEXT NOT NULL,
                    due_date TEXT,
                    subtotal REAL DEFAULT 0,
                    discount_amount REAL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    total_amount REAL DEFAULT 0,
                    paid_amount REAL DEFAULT 0,
                    remaining_amount REAL DEFAULT 0,
                    payment_status TEXT DEFAULT 'pending',
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # جدول تفاصيل فواتير المبيعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales_invoice_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    discount_amount REAL DEFAULT 0,
                    total_amount REAL NOT NULL,
                    FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            # جدول فواتير المشتريات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    supplier_id INTEGER,
                    user_id INTEGER NOT NULL,
                    invoice_date TEXT NOT NULL,
                    due_date TEXT,
                    subtotal REAL DEFAULT 0,
                    discount_amount REAL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    total_amount REAL DEFAULT 0,
                    paid_amount REAL DEFAULT 0,
                    remaining_amount REAL DEFAULT 0,
                    payment_status TEXT DEFAULT 'pending',
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # جدول تفاصيل فواتير المشتريات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_invoice_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    discount_amount REAL DEFAULT 0,
                    total_amount REAL NOT NULL,
                    FOREIGN KEY (invoice_id) REFERENCES purchase_invoices (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            # جدول المدفوعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_type TEXT NOT NULL, -- 'sales' or 'purchase'
                    invoice_id INTEGER NOT NULL,
                    payment_date TEXT NOT NULL,
                    amount REAL NOT NULL,
                    payment_method TEXT NOT NULL,
                    reference_number TEXT,
                    notes TEXT,
                    user_id INTEGER NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # جدول حركات المخزون
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
                    quantity REAL NOT NULL,
                    reference_type TEXT, -- 'sales', 'purchase', 'adjustment'
                    reference_id INTEGER,
                    notes TEXT,
                    user_id INTEGER NOT NULL,
                    movement_date TEXT NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # جدول سجل نشاط المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_activity_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    action TEXT NOT NULL,
                    details TEXT,
                    table_name TEXT,
                    record_id INTEGER,
                    timestamp TEXT NOT NULL,
                    ip_address TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # إنشاء المستخدم الافتراضي (admin)
            self.create_default_admin()

            # إنشاء فئة افتراضية
            self.create_default_category()

            conn.commit()
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
            
    def create_default_admin(self):
        """إنشاء مستخدم المدير الافتراضي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # التحقق من وجود مستخدم admin
            cursor.execute("SELECT id FROM users WHERE username = 'admin'")
            if cursor.fetchone() is None:
                # إنشاء كلمة مرور مشفرة
                password_hash = self.hash_password('admin123')
                
                cursor.execute('''
                    INSERT INTO users (username, password_hash, name, role, email)
                    VALUES (?, ?, ?, ?, ?)
                ''', ('admin', password_hash, 'المدير العام', 'admin', '<EMAIL>'))
                
            conn.commit()
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
            
    def create_default_category(self):
        """إنشاء فئة افتراضية للمنتجات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # التحقق من وجود فئة افتراضية
            cursor.execute("SELECT id FROM categories WHERE name = 'عام'")
            if cursor.fetchone() is None:
                cursor.execute('''
                    INSERT INTO categories (name, description)
                    VALUES (?, ?)
                ''', ('عام', 'فئة عامة للمنتجات'))
                
            conn.commit()
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
            
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
        
    def verify_password(self, password, password_hash):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == password_hash
        
    def authenticate_user(self, username, password):
        """التحقق من بيانات المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT id, username, password_hash, name, role, email, phone, is_active
                FROM users 
                WHERE username = ? AND is_active = 1
            ''', (username,))
            
            user = cursor.fetchone()
            
            if user and self.verify_password(password, user['password_hash']):
                return dict(user)
            else:
                return None
                
        except Exception as e:
            raise e
        finally:
            conn.close()

    def execute_query(self, query, params=None):
        """تنفيذ استعلام وإرجاع النتائج"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.rowcount

        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()

    def get_next_invoice_number(self, invoice_type='sales'):
        """الحصول على رقم الفاتورة التالي"""
        table_name = f"{invoice_type}_invoices"
        query = f"SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM {table_name}"

        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute(query)
            result = cursor.fetchone()

            if result[0] is None:
                next_number = 1
            else:
                next_number = result[0] + 1

            prefix = "INV-" if invoice_type == 'sales' else "PUR-"
            return f"{prefix}{next_number:06d}"

        except Exception as e:
            raise e
        finally:
            conn.close()

    def backup_database(self, backup_path):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        import shutil
        try:
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception as e:
            raise e

    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        import shutil
        try:
            shutil.copy2(backup_path, self.db_path)
            return True
        except Exception as e:
            raise e
