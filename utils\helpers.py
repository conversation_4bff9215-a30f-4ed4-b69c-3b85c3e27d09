# -*- coding: utf-8 -*-
"""
دوال مساعدة للبرنامج
"""

import os
import re
from datetime import datetime, timedelta
from config.settings import *

def validate_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone):
    """التحقق من صحة رقم الهاتف"""
    # إزالة المسافات والرموز
    phone = re.sub(r'[^\d+]', '', phone)
    
    # التحقق من الطول والتنسيق
    if len(phone) >= 8 and len(phone) <= 15:
        return True
    return False

def format_currency(amount):
    """تنسيق المبلغ بالعملة"""
    try:
        amount = float(amount)
        return f"{amount:,.2f} {COMPANY_INFO['currency_symbol']}"
    except:
        return f"0.00 {COMPANY_INFO['currency_symbol']}"

def format_date(date_string, input_format='%Y-%m-%d', output_format='%d/%m/%Y'):
    """تنسيق التاريخ"""
    try:
        if isinstance(date_string, str):
            date_obj = datetime.strptime(date_string, input_format)
        else:
            date_obj = date_string
        return date_obj.strftime(output_format)
    except:
        return date_string

def get_date_range(period='today'):
    """الحصول على نطاق تاريخ محدد"""
    today = datetime.now().date()
    
    if period == 'today':
        return today, today
    elif period == 'yesterday':
        yesterday = today - timedelta(days=1)
        return yesterday, yesterday
    elif period == 'this_week':
        start_week = today - timedelta(days=today.weekday())
        return start_week, today
    elif period == 'last_week':
        start_last_week = today - timedelta(days=today.weekday() + 7)
        end_last_week = start_last_week + timedelta(days=6)
        return start_last_week, end_last_week
    elif period == 'this_month':
        start_month = today.replace(day=1)
        return start_month, today
    elif period == 'last_month':
        if today.month == 1:
            start_last_month = today.replace(year=today.year-1, month=12, day=1)
        else:
            start_last_month = today.replace(month=today.month-1, day=1)
        
        # آخر يوم في الشهر الماضي
        if start_last_month.month == 12:
            end_last_month = start_last_month.replace(year=start_last_month.year+1, month=1, day=1) - timedelta(days=1)
        else:
            end_last_month = start_last_month.replace(month=start_last_month.month+1, day=1) - timedelta(days=1)
        
        return start_last_month, end_last_month
    elif period == 'this_year':
        start_year = today.replace(month=1, day=1)
        return start_year, today
    else:
        return today, today

def calculate_tax(amount, tax_rate):
    """حساب الضريبة"""
    try:
        amount = float(amount)
        tax_rate = float(tax_rate)
        return amount * (tax_rate / 100)
    except:
        return 0.0

def calculate_discount(amount, discount_rate=0, discount_amount=0):
    """حساب الخصم"""
    try:
        amount = float(amount)
        
        if discount_amount > 0:
            return min(float(discount_amount), amount)
        elif discount_rate > 0:
            return amount * (float(discount_rate) / 100)
        else:
            return 0.0
    except:
        return 0.0

def generate_barcode():
    """إنشاء رقم باركود عشوائي"""
    import random
    return ''.join([str(random.randint(0, 9)) for _ in range(13)])

def ensure_directory_exists(directory_path):
    """التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        return True
    return False

def get_file_size(file_path):
    """الحصول على حجم الملف"""
    try:
        return os.path.getsize(file_path)
    except:
        return 0

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def clean_text(text):
    """تنظيف النص من الأحرف غير المرغوب فيها"""
    if not text:
        return ""
    
    # إزالة المسافات الزائدة
    text = text.strip()
    
    # إزالة الأحرف الخاصة الضارة
    text = re.sub(r'[<>:"/\\|?*]', '', text)
    
    return text

def is_valid_number(value, min_value=None, max_value=None):
    """التحقق من صحة الرقم"""
    try:
        num = float(value)
        
        if min_value is not None and num < min_value:
            return False
            
        if max_value is not None and num > max_value:
            return False
            
        return True
    except:
        return False

def get_payment_status_text(status):
    """الحصول على نص حالة الدفع"""
    status_map = {
        'pending': 'معلق',
        'partial': 'مدفوع جزئياً',
        'paid': 'مدفوع',
        'overdue': 'متأخر'
    }
    return status_map.get(status, 'غير محدد')

def get_user_role_text(role):
    """الحصول على نص دور المستخدم"""
    return USER_ROLES.get(role, {}).get('name', 'غير محدد')

def check_user_permission(user_role, required_permission):
    """التحقق من صلاحية المستخدم"""
    user_permissions = USER_ROLES.get(user_role, {}).get('permissions', [])
    return 'all' in user_permissions or required_permission in user_permissions

def log_user_activity(db_manager, user_id, action, details="", table_name="", record_id=None):
    """تسجيل نشاط المستخدم"""
    try:
        from datetime import datetime

        query = """
            INSERT INTO user_activity_log (
                user_id, action, details, table_name, record_id, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?)
        """

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        params = (user_id, action, details, table_name, record_id, timestamp)

        db_manager.execute_query(query, params)

    except Exception as e:
        print(f"خطأ في تسجيل النشاط: {str(e)}")

def require_permission(permission):
    """ديكوريتر للتحقق من الصلاحيات"""
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            if hasattr(self, 'current_user') and self.current_user:
                if not check_user_permission(self.current_user['role'], permission):
                    from tkinter import messagebox
                    messagebox.showerror("خطأ في الصلاحية",
                                       f"ليس لديك صلاحية لتنفيذ هذا الإجراء.\n"
                                       f"الصلاحية المطلوبة: {permission}")
                    return None
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def show_permission_error(required_permission):
    """عرض رسالة خطأ الصلاحية"""
    from tkinter import messagebox
    messagebox.showerror("خطأ في الصلاحية",
                        f"ليس لديك صلاحية لتنفيذ هذا الإجراء.\n"
                        f"الصلاحية المطلوبة: {required_permission}")

def get_user_permissions_text(user_role):
    """الحصول على نص صلاحيات المستخدم"""
    permissions = USER_ROLES.get(user_role, {}).get('permissions', [])
    if 'all' in permissions:
        return "جميع الصلاحيات"

    permission_names = {
        'users_management': 'إدارة المستخدمين',
        'products_management': 'إدارة المنتجات',
        'customers_management': 'إدارة العملاء',
        'suppliers_management': 'إدارة الموردين',
        'sales_management': 'إدارة المبيعات',
        'purchases_management': 'إدارة المشتريات',
        'reports_view': 'عرض التقارير',
        'system_settings': 'إعدادات النظام',
        'sales_create': 'إنشاء فواتير مبيعات',
        'sales_view': 'عرض فواتير المبيعات',
        'purchases_create': 'إنشاء فواتير مشتريات',
        'inventory_management': 'إدارة المخزون'
    }

    return ', '.join([permission_names.get(p, p) for p in permissions])

def apply_rtl_settings(widget):
    """تطبيق إعدادات الاتجاه من اليمين إلى اليسار على العنصر"""
    try:
        # تطبيق الإعدادات على العنصر الحالي
        if hasattr(widget, 'configure'):
            widget_class = widget.winfo_class()

            # إعدادات خاصة بكل نوع من العناصر
            if widget_class in ['Entry', 'Text']:
                widget.configure(justify='right')
            elif widget_class == 'Label':
                widget.configure(anchor='e')
            elif widget_class == 'Button':
                widget.configure(anchor='center')

        # تطبيق الإعدادات على العناصر الفرعية
        for child in widget.winfo_children():
            apply_rtl_settings(child)

    except Exception:
        # تجاهل الأخطاء في حالة عدم دعم العنصر لهذه الإعدادات
        pass

def configure_widget_rtl(widget, widget_type='default'):
    """تكوين عنصر واجهة لدعم الاتجاه من اليمين إلى اليسار"""
    rtl_configs = {
        'entry': {'justify': 'right'},
        'label': {'anchor': 'e'},
        'button': {'anchor': 'center'},
        'text': {'justify': 'right'},
        'listbox': {'justify': 'right'}
    }

    config = rtl_configs.get(widget_type, {})

    try:
        if config and hasattr(widget, 'configure'):
            widget.configure(**config)
    except Exception:
        pass

    return widget

def calculate_cost_of_goods_sold(db_manager, from_date, to_date):
    """حساب تكلفة البضاعة المباعة بناءً على المبيعات الفعلية"""
    try:
        # الحصول على المنتجات المباعة في الفترة مع كمياتها
        query = """
            SELECT
                p.id,
                p.name,
                SUM(sii.quantity) as sold_quantity,
                p.selling_price
            FROM sales_invoice_items sii
            JOIN sales_invoices si ON sii.invoice_id = si.id
            JOIN products p ON sii.product_id = p.id
            WHERE DATE(si.invoice_date) BETWEEN ? AND ?
            GROUP BY p.id, p.name, p.selling_price
        """

        sold_products = db_manager.execute_query(query, [from_date, to_date])

        total_cost = 0
        for product in sold_products:
            # تقدير تكلفة المنتج (70% من سعر البيع)
            estimated_cost = product['selling_price'] * 0.7
            product_total_cost = product['sold_quantity'] * estimated_cost
            total_cost += product_total_cost

        return total_cost

    except Exception as e:
        print(f"خطأ في حساب تكلفة البضاعة المباعة: {str(e)}")
        return 0

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 بايت"

    size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"
